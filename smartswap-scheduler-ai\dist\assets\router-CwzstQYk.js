import{r as e,c as t}from"./vendor-DH5OV8M2.js";
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},r.apply(this,arguments)}var n,a;(a=n||(n={})).Pop="POP",a.Push="PUSH",a.Replace="REPLACE";const o="popstate";function l(e){return void 0===e&&(e={}),function(e,t,a,l){void 0===l&&(l={});let{window:s=document.defaultView,v5Compat:p=!1}=l,d=s.history,f=n.Pop,m=null,v=g();null==v&&(v=0,d.replaceState(r({},d.state,{idx:v}),""));function g(){return(d.state||{idx:null}).idx}function y(){f=n.Pop;let e=g(),t=null==e?null:e-v;v=e,m&&m({action:f,location:w.location,delta:t})}function E(e,t){f=n.Push;let r=c(w.location,e,t);v=g()+1;let a=u(r,v),o=w.createHref(r);try{d.pushState(a,"",o)}catch(l){if(l instanceof DOMException&&"DataCloneError"===l.name)throw l;s.location.assign(o)}p&&m&&m({action:f,location:w.location,delta:1})}function b(e,t){f=n.Replace;let r=c(w.location,e,t);v=g();let a=u(r,v),o=w.createHref(r);d.replaceState(a,"",o),p&&m&&m({action:f,location:w.location,delta:0})}function x(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,r="string"==typeof e?e:h(e);return r=r.replace(/ $/,"%20"),i(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}let w={get action(){return f},get location(){return e(s,d)},listen(e){if(m)throw new Error("A history only accepts one active listener");return s.addEventListener(o,y),m=e,()=>{s.removeEventListener(o,y),m=null}},createHref:e=>t(s,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:E,replace:b,go:e=>d.go(e)};return w}((function(e,t){let{pathname:r,search:n,hash:a}=e.location;return c("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:h(t)}),0,e)}function i(e,t){if(!1===e||null==e)throw new Error(t)}function s(e,t){if(!e)try{throw new Error(t)}catch(r){}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function c(e,t,n,a){return void 0===n&&(n=null),r({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?p(t):t,{state:n,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function p(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}var d,f;function m(e,t,r){return void 0===r&&(r="/"),function(e,t,r){let n="string"==typeof t?p(t):t,a=L(n.pathname||"/",r);if(null==a)return null;let o=v(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let r=e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]));return r?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=B(a);l=P(o[i],e)}return l}(e,t,r)}function v(e,t,r,n){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===n&&(n="");let a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(i(l.relativePath.startsWith(n),'Absolute route path "'+l.relativePath+'" nested under path "'+n+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),l.relativePath=l.relativePath.slice(n.length));let s=k([n,l.relativePath]),u=r.concat(l);e.children&&e.children.length>0&&(i(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),v(e.children,t,u,s)),(null!=e.path||e.index)&&t.push({path:s,score:S(s,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let n of g(e.path))a(e,t,n);else a(e,t)})),t}function g(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===n.length)return a?[o,""]:[o];let l=g(n.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}(f=d||(d={})).data="data",f.deferred="deferred",f.redirect="redirect",f.error="error";const y=/^:[\w-]+$/,E=3,b=2,x=1,w=10,C=-2,R=e=>"*"===e;function S(e,t){let r=e.split("/"),n=r.length;return r.some(R)&&(n+=C),t&&(n+=b),r.filter((e=>!R(e))).reduce(((e,t)=>e+(y.test(t)?E:""===t?x:w)),n)}function P(e,t,r){let{routesMeta:n}=e,a={},o="/",l=[];for(let i=0;i<n.length;++i){let e=n[i],r=i===n.length-1,s="/"===o?t:t.slice(o.length)||"/",u=U({path:e.relativePath,caseSensitive:e.caseSensitive,end:r},s),c=e.route;if(!u)return null;Object.assign(a,u.params),l.push({params:a,pathname:k([o,u.pathname]),pathnameBase:O(k([o,u.pathnameBase])),route:c}),"/"!==u.pathnameBase&&(o=k([o,u.pathnameBase]))}return l}function U(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t,r){void 0===t&&(t=!1);void 0===r&&(r=!0);s("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(n.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,n]}(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:n.reduce(((e,t,r)=>{let{paramName:n,isOptional:a}=t;if("*"===n){let e=i[r]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[r];return e[n]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:l,pattern:e}}function B(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return s(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}const k=e=>e.join("/").replace(/\/\/+/g,"/"),O=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");const F=["post","put","patch","delete"];new Set(F);const $=["get",...F];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function j(){return j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},j.apply(this,arguments)}new Set($);const D=e.createContext(null),T=e.createContext(null),M=e.createContext(null),A=e.createContext(null),W=e.createContext({outlet:null,matches:[],isDataRoute:!1}),I=e.createContext(null);function N(){return null!=e.useContext(A)}function _(){return N()||i(!1),e.useContext(A).location}function H(t,r){return function(t,r,a,o){N()||i(!1);let{navigator:l}=e.useContext(M),{matches:s}=e.useContext(W),u=s[s.length-1],c=u?u.params:{};!u||u.pathname;let h=u?u.pathnameBase:"/";u&&u.route;let d,f=_();if(r){var v;let e="string"==typeof r?p(r):r;"/"===h||(null==(v=e.pathname)?void 0:v.startsWith(h))||i(!1),d=e}else d=f;let g=d.pathname||"/",y=g;if("/"!==h){let e=h.replace(/^\//,"").split("/");y="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let E=m(t,{pathname:y}),b=function(t,r,n,a){var o;void 0===r&&(r=[]);void 0===n&&(n=null);void 0===a&&(a=null);if(null==t){var l;if(!n)return null;if(n.errors)t=n.matches;else{if(!(null!=(l=a)&&l.v7_partialHydration&&0===r.length&&!n.initialized&&n.matches.length>0))return null;t=n.matches}}let s=t,u=null==(o=n)?void 0:o.errors;if(null!=u){let e=s.findIndex((e=>e.route.id&&void 0!==(null==u?void 0:u[e.route.id])));e>=0||i(!1),s=s.slice(0,Math.min(s.length,e+1))}let c=!1,h=-1;if(n&&a&&a.v7_partialHydration)for(let e=0;e<s.length;e++){let t=s[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(h=e),t.route.id){let{loaderData:e,errors:r}=n,a=t.route.loader&&void 0===e[t.route.id]&&(!r||void 0===r[t.route.id]);if(t.route.lazy||a){c=!0,s=h>=0?s.slice(0,h+1):[s[0]];break}}}return s.reduceRight(((t,a,o)=>{let l,i=!1,p=null,d=null;var f;n&&(l=u&&a.route.id?u[a.route.id]:void 0,p=a.route.errorElement||z,c&&(h<0&&0===o?(K[f="route-fallback"]||(K[f]=!0),i=!0,d=null):h===o&&(i=!0,d=a.route.hydrateFallbackElement||null)));let m=r.concat(s.slice(0,o+1)),v=()=>{let r;return r=l?p:i?d:a.route.Component?e.createElement(a.route.Component,null):a.route.element?a.route.element:t,e.createElement(q,{match:a,routeContext:{outlet:t,matches:m,isDataRoute:null!=n},children:r})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===o)?e.createElement(J,{location:n.location,revalidation:n.revalidation,component:p,error:l,children:v(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):v()}),null)}(E&&E.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:k([h,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:k([h,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),s,a,o);if(r&&b)return e.createElement(A.Provider,{value:{location:j({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:n.Pop}},b);return b}(t,r)}function V(){let t=function(){var t;let r=e.useContext(I),n=function(){let t=e.useContext(T);return t||i(!1),t}(G.UseRouteError),a=function(){let t=function(){let t=e.useContext(W);return t||i(!1),t}(),r=t.matches[t.matches.length-1];return r.route.id||i(!1),r.route.id}();if(void 0!==r)return r;return null==(t=n.errors)?void 0:t[a]}(),r=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},r),n?e.createElement("pre",{style:a},n):null,null)}const z=e.createElement(V,null);class J extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?e.createElement(W.Provider,{value:this.props.routeContext},e.createElement(I.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function q(t){let{routeContext:r,match:n,children:a}=t,o=e.useContext(D);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),e.createElement(W.Provider,{value:r},a)}var G=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(G||{});const K={};function Q(e){i(!1)}function X(t){let{basename:r="/",children:a=null,location:o,navigationType:l=n.Pop,navigator:s,static:u=!1,future:c}=t;N()&&i(!1);let h=r.replace(/^\/*/,"/"),d=e.useMemo((()=>({basename:h,navigator:s,static:u,future:j({v7_relativeSplatPath:!1},c)})),[h,c,s,u]);"string"==typeof o&&(o=p(o));let{pathname:f="/",search:m="",hash:v="",state:g=null,key:y="default"}=o,E=e.useMemo((()=>{let e=L(f,h);return null==e?null:{location:{pathname:e,search:m,hash:v,state:g,key:y},navigationType:l}}),[h,f,m,v,g,y,l]);return null==E?null:e.createElement(M.Provider,{value:d},e.createElement(A.Provider,{children:a,value:E}))}function Y(e){let{children:t,location:r}=e;return H(Z(t),r)}function Z(t,r){void 0===r&&(r=[]);let n=[];return e.Children.forEach(t,((t,a)=>{if(!e.isValidElement(t))return;let o=[...r,a];if(t.type===e.Fragment)return void n.push.apply(n,Z(t.props.children,o));t.type!==Q&&i(!1),t.props.index&&t.props.children&&i(!1);let l={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(l.children=Z(t.props.children,o)),n.push(l)})),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise((()=>{}));try{window.__reactRouterVersion="6"}catch(le){}const ee=t.startTransition;function te(t){let{basename:r,children:n,future:a,window:o}=t,i=e.useRef();null==i.current&&(i.current=l({window:o,v5Compat:!0}));let s=i.current,[u,c]=e.useState({action:s.action,location:s.location}),{v7_startTransition:h}=a||{},p=e.useCallback((e=>{h&&ee?ee((()=>c(e))):c(e)}),[c,h]);return e.useLayoutEffect((()=>s.listen(p)),[s,p]),e.useEffect((()=>{return null==(e=a)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e}),[a]),e.createElement(X,{basename:r,children:n,location:u.location,navigationType:u.action,navigator:s,future:a})}var re,ne,ae,oe;(ne=re||(re={})).UseScrollRestoration="useScrollRestoration",ne.UseSubmit="useSubmit",ne.UseSubmitFetcher="useSubmitFetcher",ne.UseFetcher="useFetcher",ne.useViewTransitionState="useViewTransitionState",(oe=ae||(ae={})).UseFetcher="useFetcher",oe.UseFetchers="useFetchers",oe.UseScrollRestoration="useScrollRestoration";export{te as B,Y as R,Q as a,_ as u};
//# sourceMappingURL=router-CwzstQYk.js.map
