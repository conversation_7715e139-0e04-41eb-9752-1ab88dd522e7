var t,e,s,i,n,r,a,o,h,u,l,c,d,f,p,y,v,m,b,g,w,O,M,C,k,R,S,P,E,q,F,x,Q,W,A,D,U,T,j,K,I,L,H,_,G,B,N,z,$,J,V,X,Y,Z,tt,et,st,it,nt,rt,at,ot,ht,ut,lt,ct,dt,ft,pt,yt,vt,mt,bt,gt,wt,Ot=t=>{throw TypeError(t)},Mt=(t,e,s)=>e.has(t)||Ot("Cannot "+s),Ct=(t,e,s)=>(Mt(t,e,"read from private field"),s?s.call(t):e.get(t)),kt=(t,e,s)=>e.has(t)?Ot("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,s),Rt=(t,e,s,i)=>(Mt(t,e,"write to private field"),i?i.call(t,s):e.set(t,s),s),St=(t,e,s)=>(Mt(t,e,"access private method"),s),Pt=(t,e,s,i)=>({set _(i){Rt(t,e,i,s)},get _(){return Ct(t,e,i)}});import{r as Et}from"./vendor-DH5OV8M2.js";import{j as qt}from"./ui-d9jfY017.js";var Ft=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},xt="undefined"==typeof window||"Deno"in globalThis;function Qt(){}function Wt(t){return"number"==typeof t&&t>=0&&t!==1/0}function At(t,e){return Math.max(t+(e||0)-Date.now(),0)}function Dt(t,e){return"function"==typeof t?t(e):t}function Ut(t,e){return"function"==typeof t?t(e):t}function Tt(t,e){const{type:s="all",exact:i,fetchStatus:n,predicate:r,queryKey:a,stale:o}=t;if(a)if(i){if(e.queryHash!==Kt(a,e.options))return!1}else if(!Lt(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!n||n===e.state.fetchStatus)&&!(r&&!r(e)))}function jt(t,e){const{exact:s,status:i,predicate:n,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(s){if(It(e.options.mutationKey)!==It(r))return!1}else if(!Lt(e.options.mutationKey,r))return!1}return(!i||e.state.status===i)&&!(n&&!n(e))}function Kt(t,e){return((null==e?void 0:e.queryKeyHashFn)||It)(t)}function It(t){return JSON.stringify(t,((t,e)=>Bt(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function Lt(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every((s=>Lt(t[s],e[s]))))}function Ht(t,e){if(t===e)return t;const s=Gt(t)&&Gt(e);if(s||Bt(t)&&Bt(e)){const i=s?t:Object.keys(t),n=i.length,r=s?e:Object.keys(e),a=r.length,o=s?[]:{};let h=0;for(let u=0;u<a;u++){const n=s?u:r[u];(!s&&i.includes(n)||s)&&void 0===t[n]&&void 0===e[n]?(o[n]=void 0,h++):(o[n]=Ht(t[n],e[n]),o[n]===t[n]&&void 0!==t[n]&&h++)}return n===a&&h===n?t:o}return e}function _t(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function Gt(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function Bt(t){if(!Nt(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!Nt(s)&&(!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function Nt(t){return"[object Object]"===Object.prototype.toString.call(t)}function zt(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?Ht(t,e):e}function $t(t,e,s=0){const i=[...t,e];return s&&i.length>s?i.slice(1):i}function Jt(t,e,s=0){const i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var Vt=Symbol();function Xt(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==Vt?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}function Yt(t,e){return"function"==typeof t?t(...e):!!t}var Zt=new(i=class extends Ft{constructor(){super(),kt(this,t),kt(this,e),kt(this,s),Rt(this,s,(t=>{if(!xt&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){Ct(this,e)||this.setEventListener(Ct(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=Ct(this,e))||t.call(this),Rt(this,e,void 0))}setEventListener(t){var i;Rt(this,s,t),null==(i=Ct(this,e))||i.call(this),Rt(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){Ct(this,t)!==e&&(Rt(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){var e;return"boolean"==typeof Ct(this,t)?Ct(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,e=new WeakMap,s=new WeakMap,i),te=new(o=class extends Ft{constructor(){super(),kt(this,n,!0),kt(this,r),kt(this,a),Rt(this,a,(t=>{if(!xt&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}))}onSubscribe(){Ct(this,r)||this.setEventListener(Ct(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=Ct(this,r))||t.call(this),Rt(this,r,void 0))}setEventListener(t){var e;Rt(this,a,t),null==(e=Ct(this,r))||e.call(this),Rt(this,r,t(this.setOnline.bind(this)))}setOnline(t){Ct(this,n)!==t&&(Rt(this,n,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return Ct(this,n)}},n=new WeakMap,r=new WeakMap,a=new WeakMap,o);function ee(){let t,e;const s=new Promise(((s,i)=>{t=s,e=i}));function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}function se(t){return Math.min(1e3*2**t,3e4)}function ie(t){return"online"!==(t??"online")||te.isOnline()}var ne=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function re(t){return t instanceof ne}function ae(t){let e,s=!1,i=0,n=!1;const r=ee(),a=()=>Zt.isFocused()&&("always"===t.networkMode||te.isOnline())&&t.canRun(),o=()=>ie(t.networkMode)&&t.canRun(),h=s=>{var i;n||(n=!0,null==(i=t.onSuccess)||i.call(t,s),null==e||e(),r.resolve(s))},u=s=>{var i;n||(n=!0,null==(i=t.onError)||i.call(t,s),null==e||e(),r.reject(s))},l=()=>new Promise((s=>{var i;e=t=>{(n||a())&&s(t)},null==(i=t.onPause)||i.call(t)})).then((()=>{var s;e=void 0,n||null==(s=t.onContinue)||s.call(t)})),c=()=>{if(n)return;let e;const r=0===i?t.initialPromise:void 0;try{e=r??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(h).catch((e=>{var r;if(n)return;const o=t.retry??(xt?0:3),h=t.retryDelay??se,d="function"==typeof h?h(i,e):h,f=!0===o||"number"==typeof o&&i<o||"function"==typeof o&&o(i,e);var p;!s&&f?(i++,null==(r=t.onFail)||r.call(t,i,e),(p=d,new Promise((t=>{setTimeout(t,p)}))).then((()=>a()?void 0:l())).then((()=>{s?u(e):c()}))):u(e)}))};return{promise:r,cancel:e=>{var s;n||(u(new ne(e)),null==(s=t.abort)||s.call(t))},continue:()=>(null==e||e(),r),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:o,start:()=>(o()?c():l().then(c),r)}}var oe=t=>setTimeout(t,0);var he=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},n=oe;const r=i=>{e?t.push(i):n((()=>{s(i)}))};return{batch:r=>{let a;e++;try{a=r()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n((()=>{i((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{r((()=>{t(...e)}))},schedule:r,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{n=t}}}(),ue=(u=class{constructor(){kt(this,h)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Wt(this.gcTime)&&Rt(this,h,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(xt?1/0:3e5))}clearGcTimeout(){Ct(this,h)&&(clearTimeout(Ct(this,h)),Rt(this,h,void 0))}},h=new WeakMap,u),le=(g=class extends ue{constructor(t){super(),kt(this,m),kt(this,l),kt(this,c),kt(this,d),kt(this,f),kt(this,p),kt(this,y),kt(this,v),Rt(this,v,!1),Rt(this,y,t.defaultOptions),this.setOptions(t.options),this.observers=[],Rt(this,f,t.client),Rt(this,d,Ct(this,f).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,Rt(this,l,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??Ct(this,l),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return null==(t=Ct(this,p))?void 0:t.promise}setOptions(t){this.options={...Ct(this,y),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||Ct(this,d).remove(this)}setData(t,e){const s=zt(this.state.data,t,this.options);return St(this,m,b).call(this,{data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),s}setState(t,e){St(this,m,b).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,s;const i=null==(e=Ct(this,p))?void 0:e.promise;return null==(s=Ct(this,p))||s.cancel(t),i?i.then(Qt).catch(Qt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(Ct(this,l))}isActive(){return this.observers.some((t=>!1!==Ut(t.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Vt||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!At(this.state.dataUpdatedAt,t)}onFocus(){var t;const e=this.observers.find((t=>t.shouldFetchOnWindowFocus()));null==e||e.refetch({cancelRefetch:!1}),null==(t=Ct(this,p))||t.continue()}onOnline(){var t;const e=this.observers.find((t=>t.shouldFetchOnReconnect()));null==e||e.refetch({cancelRefetch:!1}),null==(t=Ct(this,p))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),Ct(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(Ct(this,p)&&(Ct(this,v)?Ct(this,p).cancel({revert:!0}):Ct(this,p).cancelRetry()),this.scheduleGc()),Ct(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||St(this,m,b).call(this,{type:"invalidate"})}fetch(t,e){var s,i,n;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(Ct(this,p))return Ct(this,p).continueRetry(),Ct(this,p).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const r=new AbortController,a=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(Rt(this,v,!0),r.signal)})},o={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:Ct(this,f),state:this.state,fetchFn:()=>{const t=Xt(this.options,e),s={client:Ct(this,f),queryKey:this.queryKey,meta:this.meta};return a(s),Rt(this,v,!1),this.options.persister?this.options.persister(t,s,this):t(s)}};a(o),null==(s=this.options.behavior)||s.onFetch(o,this),Rt(this,c,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(i=o.fetchOptions)?void 0:i.meta)||St(this,m,b).call(this,{type:"fetch",meta:null==(n=o.fetchOptions)?void 0:n.meta});const h=t=>{var e,s,i,n;re(t)&&t.silent||St(this,m,b).call(this,{type:"error",error:t}),re(t)||(null==(s=(e=Ct(this,d).config).onError)||s.call(e,t,this),null==(n=(i=Ct(this,d).config).onSettled)||n.call(i,this.state.data,t,this)),this.scheduleGc()};return Rt(this,p,ae({initialPromise:null==e?void 0:e.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{var e,s,i,n;if(void 0!==t){try{this.setData(t)}catch(r){return void h(r)}null==(s=(e=Ct(this,d).config).onSuccess)||s.call(e,t,this),null==(n=(i=Ct(this,d).config).onSettled)||n.call(i,t,this.state.error,this),this.scheduleGc()}else h(new Error(`${this.queryHash} data is undefined`))},onError:h,onFail:(t,e)=>{St(this,m,b).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{St(this,m,b).call(this,{type:"pause"})},onContinue:()=>{St(this,m,b).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),Ct(this,p).start()}},l=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,y=new WeakMap,v=new WeakMap,m=new WeakSet,b=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...ce(e.data,this.options),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return re(s)&&s.revert&&Ct(this,c)?{...Ct(this,c),fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),he.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),Ct(this,d).notify({query:this,type:"updated",action:t})}))},g);function ce(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:ie(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var de=(O=class extends Ft{constructor(t={}){super(),kt(this,w),this.config=t,Rt(this,w,new Map)}build(t,e,s){const i=e.queryKey,n=e.queryHash??Kt(i,e);let r=this.get(n);return r||(r=new le({client:t,queryKey:i,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(r)),r}add(t){Ct(this,w).has(t.queryHash)||(Ct(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=Ct(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&Ct(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){he.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return Ct(this,w).get(t)}getAll(){return[...Ct(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Tt(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>Tt(t,e))):e}notify(t){he.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){he.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){he.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,O),fe=(P=class extends ue{constructor(t){super(),kt(this,R),kt(this,M),kt(this,C),kt(this,k),this.mutationId=t.mutationId,Rt(this,C,t.mutationCache),Rt(this,M,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){Ct(this,M).includes(t)||(Ct(this,M).push(t),this.clearGcTimeout(),Ct(this,C).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){Rt(this,M,Ct(this,M).filter((e=>e!==t))),this.scheduleGc(),Ct(this,C).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){Ct(this,M).length||("pending"===this.state.status?this.scheduleGc():Ct(this,C).remove(this))}continue(){var t;return(null==(t=Ct(this,k))?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var e,s,i,n,r,a,o,h,u,l,c,d,f,p,y,v,m,b,g,w;const O=()=>{St(this,R,S).call(this,{type:"continue"})};Rt(this,k,ae({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{St(this,R,S).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{St(this,R,S).call(this,{type:"pause"})},onContinue:O,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>Ct(this,C).canRun(this)}));const M="pending"===this.state.status,P=!Ct(this,k).canStart();try{if(M)O();else{St(this,R,S).call(this,{type:"pending",variables:t,isPaused:P}),await(null==(s=(e=Ct(this,C).config).onMutate)?void 0:s.call(e,t,this));const r=await(null==(n=(i=this.options).onMutate)?void 0:n.call(i,t));r!==this.state.context&&St(this,R,S).call(this,{type:"pending",context:r,variables:t,isPaused:P})}const f=await Ct(this,k).start();return await(null==(a=(r=Ct(this,C).config).onSuccess)?void 0:a.call(r,f,t,this.state.context,this)),await(null==(h=(o=this.options).onSuccess)?void 0:h.call(o,f,t,this.state.context)),await(null==(l=(u=Ct(this,C).config).onSettled)?void 0:l.call(u,f,null,this.state.variables,this.state.context,this)),await(null==(d=(c=this.options).onSettled)?void 0:d.call(c,f,null,t,this.state.context)),St(this,R,S).call(this,{type:"success",data:f}),f}catch(E){try{throw await(null==(p=(f=Ct(this,C).config).onError)?void 0:p.call(f,E,t,this.state.context,this)),await(null==(v=(y=this.options).onError)?void 0:v.call(y,E,t,this.state.context)),await(null==(b=(m=Ct(this,C).config).onSettled)?void 0:b.call(m,void 0,E,this.state.variables,this.state.context,this)),await(null==(w=(g=this.options).onSettled)?void 0:w.call(g,void 0,E,t,this.state.context)),E}finally{St(this,R,S).call(this,{type:"error",error:E})}}finally{Ct(this,C).runNext(this)}}},M=new WeakMap,C=new WeakMap,k=new WeakMap,R=new WeakSet,S=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),he.batch((()=>{Ct(this,M).forEach((e=>{e.onMutationUpdate(t)})),Ct(this,C).notify({mutation:this,type:"updated",action:t})}))},P);var pe=(x=class extends Ft{constructor(t={}){super(),kt(this,E),kt(this,q),kt(this,F),this.config=t,Rt(this,E,new Set),Rt(this,q,new Map),Rt(this,F,0)}build(t,e,s){const i=new fe({mutationCache:this,mutationId:++Pt(this,F)._,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){Ct(this,E).add(t);const e=ye(t);if("string"==typeof e){const s=Ct(this,q).get(e);s?s.push(t):Ct(this,q).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(Ct(this,E).delete(t)){const e=ye(t);if("string"==typeof e){const s=Ct(this,q).get(e);if(s)if(s.length>1){const e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&Ct(this,q).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=ye(t);if("string"==typeof e){const s=Ct(this,q).get(e),i=null==s?void 0:s.find((t=>"pending"===t.state.status));return!i||i===t}return!0}runNext(t){var e;const s=ye(t);if("string"==typeof s){const i=null==(e=Ct(this,q).get(s))?void 0:e.find((e=>e!==t&&e.state.isPaused));return(null==i?void 0:i.continue())??Promise.resolve()}return Promise.resolve()}clear(){he.batch((()=>{Ct(this,E).forEach((t=>{this.notify({type:"removed",mutation:t})})),Ct(this,E).clear(),Ct(this,q).clear()}))}getAll(){return Array.from(Ct(this,E))}find(t){const e={exact:!0,...t};return this.getAll().find((t=>jt(e,t)))}findAll(t={}){return this.getAll().filter((e=>jt(t,e)))}notify(t){he.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return he.batch((()=>Promise.all(t.map((t=>t.continue().catch(Qt))))))}},E=new WeakMap,q=new WeakMap,F=new WeakMap,x);function ye(t){var e;return null==(e=t.options.scope)?void 0:e.id}function ve(t){return{onFetch:(e,s)=>{var i,n,r,a,o;const h=e.options,u=null==(r=null==(n=null==(i=e.fetchOptions)?void 0:i.meta)?void 0:n.fetchMore)?void 0:r.direction,l=(null==(a=e.state.data)?void 0:a.pages)||[],c=(null==(o=e.state.data)?void 0:o.pageParams)||[];let d={pages:[],pageParams:[]},f=0;const p=async()=>{let s=!1;const i=Xt(e.options,e.fetchOptions),n=async(t,n,r)=>{if(s)return Promise.reject();if(null==n&&t.pages.length)return Promise.resolve(t);const a={client:e.client,queryKey:e.queryKey,pageParam:n,direction:r?"backward":"forward",meta:e.options.meta};var o;o=a,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",(()=>{s=!0})),e.signal)});const h=await i(a),{maxPages:u}=e.options,l=r?Jt:$t;return{pages:l(t.pages,h,u),pageParams:l(t.pageParams,n,u)}};if(u&&l.length){const t="backward"===u,e={pages:l,pageParams:c},s=(t?be:me)(h,e);d=await n(e,s,t)}else{const e=t??l.length;do{const t=0===f?c[0]??h.initialPageParam:me(h,d);if(f>0&&null==t)break;d=await n(d,t),f++}while(f<e)}return d};e.options.persister?e.fetchFn=()=>{var t,i;return null==(i=(t=e.options).persister)?void 0:i.call(t,p,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s)}:e.fetchFn=p}}}function me(t,{pages:e,pageParams:s}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}function be(t,{pages:e,pageParams:s}){var i;return e.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,e[0],e,s[0],s):void 0}var ge=(I=class{constructor(t={}){kt(this,Q),kt(this,W),kt(this,A),kt(this,D),kt(this,U),kt(this,T),kt(this,j),kt(this,K),Rt(this,Q,t.queryCache||new de),Rt(this,W,t.mutationCache||new pe),Rt(this,A,t.defaultOptions||{}),Rt(this,D,new Map),Rt(this,U,new Map),Rt(this,T,0)}mount(){Pt(this,T)._++,1===Ct(this,T)&&(Rt(this,j,Zt.subscribe((async t=>{t&&(await this.resumePausedMutations(),Ct(this,Q).onFocus())}))),Rt(this,K,te.subscribe((async t=>{t&&(await this.resumePausedMutations(),Ct(this,Q).onOnline())}))))}unmount(){var t,e;Pt(this,T)._--,0===Ct(this,T)&&(null==(t=Ct(this,j))||t.call(this),Rt(this,j,void 0),null==(e=Ct(this,K))||e.call(this),Rt(this,K,void 0))}isFetching(t){return Ct(this,Q).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return Ct(this,W).findAll({...t,status:"pending"}).length}getQueryData(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=Ct(this,Q).get(s.queryHash))?void 0:e.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=Ct(this,Q).build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(Dt(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return Ct(this,Q).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const i=this.defaultQueryOptions({queryKey:t}),n=Ct(this,Q).get(i.queryHash),r=function(t,e){return"function"==typeof t?t(e):t}(e,null==n?void 0:n.state.data);if(void 0!==r)return Ct(this,Q).build(this,i).setData(r,{...s,manual:!0})}setQueriesData(t,e,s){return he.batch((()=>Ct(this,Q).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){var e;const s=this.defaultQueryOptions({queryKey:t});return null==(e=Ct(this,Q).get(s.queryHash))?void 0:e.state}removeQueries(t){const e=Ct(this,Q);he.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=Ct(this,Q);return he.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries({type:"active",...t},e))))}cancelQueries(t,e={}){const s={revert:!0,...e},i=he.batch((()=>Ct(this,Q).findAll(t).map((t=>t.cancel(s)))));return Promise.all(i).then(Qt).catch(Qt)}invalidateQueries(t,e={}){return he.batch((()=>(Ct(this,Q).findAll(t).forEach((t=>{t.invalidate()})),"none"===(null==t?void 0:t.refetchType)?Promise.resolve():this.refetchQueries({...t,type:(null==t?void 0:t.refetchType)??(null==t?void 0:t.type)??"active"},e))))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},i=he.batch((()=>Ct(this,Q).findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(Qt)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(Qt)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=Ct(this,Q).build(this,e);return s.isStaleByTime(Dt(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Qt).catch(Qt)}fetchInfiniteQuery(t){return t.behavior=ve(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Qt).catch(Qt)}ensureInfiniteQueryData(t){return t.behavior=ve(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return te.isOnline()?Ct(this,W).resumePausedMutations():Promise.resolve()}getQueryCache(){return Ct(this,Q)}getMutationCache(){return Ct(this,W)}getDefaultOptions(){return Ct(this,A)}setDefaultOptions(t){Rt(this,A,t)}setQueryDefaults(t,e){Ct(this,D).set(It(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...Ct(this,D).values()],s={};return e.forEach((e=>{Lt(t,e.queryKey)&&Object.assign(s,e.defaultOptions)})),s}setMutationDefaults(t,e){Ct(this,U).set(It(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...Ct(this,U).values()],s={};return e.forEach((e=>{Lt(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...Ct(this,A).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=Kt(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===Vt&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:{...Ct(this,A).mutations,...(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){Ct(this,Q).clear(),Ct(this,W).clear()}},Q=new WeakMap,W=new WeakMap,A=new WeakMap,D=new WeakMap,U=new WeakMap,T=new WeakMap,j=new WeakMap,K=new WeakMap,I),we=(dt=class extends Ft{constructor(t,e){super(),kt(this,st),kt(this,L),kt(this,H),kt(this,_),kt(this,G),kt(this,B),kt(this,N),kt(this,z),kt(this,$),kt(this,J),kt(this,V),kt(this,X),kt(this,Y),kt(this,Z),kt(this,tt),kt(this,et,new Set),this.options=e,Rt(this,L,t),Rt(this,$,null),Rt(this,z,ee()),this.options.experimental_prefetchInRender||Ct(this,z).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(Ct(this,H).addObserver(this),Oe(Ct(this,H),this.options)?St(this,st,it).call(this):this.updateResult(),St(this,st,ot).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Me(Ct(this,H),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Me(Ct(this,H),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,St(this,st,ht).call(this),St(this,st,ut).call(this),Ct(this,H).removeObserver(this)}setOptions(t){const e=this.options,s=Ct(this,H);if(this.options=Ct(this,L).defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof Ut(this.options.enabled,Ct(this,H)))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");St(this,st,lt).call(this),Ct(this,H).setOptions(this.options),e._defaulted&&!_t(this.options,e)&&Ct(this,L).getQueryCache().notify({type:"observerOptionsUpdated",query:Ct(this,H),observer:this});const i=this.hasListeners();i&&Ce(Ct(this,H),s,this.options,e)&&St(this,st,it).call(this),this.updateResult(),!i||Ct(this,H)===s&&Ut(this.options.enabled,Ct(this,H))===Ut(e.enabled,Ct(this,H))&&Dt(this.options.staleTime,Ct(this,H))===Dt(e.staleTime,Ct(this,H))||St(this,st,nt).call(this);const n=St(this,st,rt).call(this);!i||Ct(this,H)===s&&Ut(this.options.enabled,Ct(this,H))===Ut(e.enabled,Ct(this,H))&&n===Ct(this,tt)||St(this,st,at).call(this,n)}getOptimisticResult(t){const e=Ct(this,L).getQueryCache().build(Ct(this,L),t),s=this.createResult(e,t);return function(t,e){if(!_t(t.getCurrentResult(),e))return!0;return!1}(this,s)&&(Rt(this,G,s),Rt(this,N,this.options),Rt(this,B,Ct(this,H).state)),s}getCurrentResult(){return Ct(this,G)}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),null==e||e(s),Reflect.get(t,s))})}trackProp(t){Ct(this,et).add(t)}getCurrentQuery(){return Ct(this,H)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=Ct(this,L).defaultQueryOptions(t),s=Ct(this,L).getQueryCache().build(Ct(this,L),e);return s.fetch().then((()=>this.createResult(s,e)))}fetch(t){return St(this,st,it).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then((()=>(this.updateResult(),Ct(this,G))))}createResult(t,e){var s;const i=Ct(this,H),n=this.options,r=Ct(this,G),a=Ct(this,B),o=Ct(this,N),h=t!==i?t.state:Ct(this,_),{state:u}=t;let l,c={...u},d=!1;if(e._optimisticResults){const s=this.hasListeners(),r=!s&&Oe(t,e),a=s&&Ce(t,i,e,n);(r||a)&&(c={...c,...ce(u.data,t.options)}),"isRestoring"===e._optimisticResults&&(c.fetchStatus="idle")}let{error:f,errorUpdatedAt:p,status:y}=c;l=c.data;let v=!1;if(void 0!==e.placeholderData&&void 0===l&&"pending"===y){let t;(null==r?void 0:r.isPlaceholderData)&&e.placeholderData===(null==o?void 0:o.placeholderData)?(t=r.data,v=!0):t="function"==typeof e.placeholderData?e.placeholderData(null==(s=Ct(this,X))?void 0:s.state.data,Ct(this,X)):e.placeholderData,void 0!==t&&(y="success",l=zt(null==r?void 0:r.data,t,e),d=!0)}if(e.select&&void 0!==l&&!v)if(r&&l===(null==a?void 0:a.data)&&e.select===Ct(this,J))l=Ct(this,V);else try{Rt(this,J,e.select),l=e.select(l),l=zt(null==r?void 0:r.data,l,e),Rt(this,V,l),Rt(this,$,null)}catch(C){Rt(this,$,C)}Ct(this,$)&&(f=Ct(this,$),l=Ct(this,V),p=Date.now(),y="error");const m="fetching"===c.fetchStatus,b="pending"===y,g="error"===y,w=b&&m,O=void 0!==l,M={status:y,fetchStatus:c.fetchStatus,isPending:b,isSuccess:"success"===y,isError:g,isInitialLoading:w,isLoading:w,data:l,dataUpdatedAt:c.dataUpdatedAt,error:f,errorUpdatedAt:p,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>h.dataUpdateCount||c.errorUpdateCount>h.errorUpdateCount,isFetching:m,isRefetching:m&&!b,isLoadingError:g&&!O,isPaused:"paused"===c.fetchStatus,isPlaceholderData:d,isRefetchError:g&&O,isStale:ke(t,e),refetch:this.refetch,promise:Ct(this,z)};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===M.status?t.reject(M.error):void 0!==M.data&&t.resolve(M.data)},s=()=>{const t=Rt(this,z,M.promise=ee());e(t)},n=Ct(this,z);switch(n.status){case"pending":t.queryHash===i.queryHash&&e(n);break;case"fulfilled":"error"!==M.status&&M.data===n.value||s();break;case"rejected":"error"===M.status&&M.error===n.reason||s()}}return M}updateResult(){const t=Ct(this,G),e=this.createResult(Ct(this,H),this.options);if(Rt(this,B,Ct(this,H).state),Rt(this,N,this.options),void 0!==Ct(this,B).data&&Rt(this,X,Ct(this,H)),_t(e,t))return;Rt(this,G,e);St(this,st,ct).call(this,{listeners:(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!Ct(this,et).size)return!0;const i=new Set(s??Ct(this,et));return this.options.throwOnError&&i.add("error"),Object.keys(Ct(this,G)).some((e=>{const s=e;return Ct(this,G)[s]!==t[s]&&i.has(s)}))})()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&St(this,st,ot).call(this)}},L=new WeakMap,H=new WeakMap,_=new WeakMap,G=new WeakMap,B=new WeakMap,N=new WeakMap,z=new WeakMap,$=new WeakMap,J=new WeakMap,V=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,tt=new WeakMap,et=new WeakMap,st=new WeakSet,it=function(t){St(this,st,lt).call(this);let e=Ct(this,H).fetch(this.options,t);return(null==t?void 0:t.throwOnError)||(e=e.catch(Qt)),e},nt=function(){St(this,st,ht).call(this);const t=Dt(this.options.staleTime,Ct(this,H));if(xt||Ct(this,G).isStale||!Wt(t))return;const e=At(Ct(this,G).dataUpdatedAt,t);Rt(this,Y,setTimeout((()=>{Ct(this,G).isStale||this.updateResult()}),e+1))},rt=function(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(Ct(this,H)):this.options.refetchInterval)??!1},at=function(t){St(this,st,ut).call(this),Rt(this,tt,t),!xt&&!1!==Ut(this.options.enabled,Ct(this,H))&&Wt(Ct(this,tt))&&0!==Ct(this,tt)&&Rt(this,Z,setInterval((()=>{(this.options.refetchIntervalInBackground||Zt.isFocused())&&St(this,st,it).call(this)}),Ct(this,tt)))},ot=function(){St(this,st,nt).call(this),St(this,st,at).call(this,St(this,st,rt).call(this))},ht=function(){Ct(this,Y)&&(clearTimeout(Ct(this,Y)),Rt(this,Y,void 0))},ut=function(){Ct(this,Z)&&(clearInterval(Ct(this,Z)),Rt(this,Z,void 0))},lt=function(){const t=Ct(this,L).getQueryCache().build(Ct(this,L),this.options);if(t===Ct(this,H))return;const e=Ct(this,H);Rt(this,H,t),Rt(this,_,t.state),this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))},ct=function(t){he.batch((()=>{t.listeners&&this.listeners.forEach((t=>{t(Ct(this,G))})),Ct(this,L).getQueryCache().notify({query:Ct(this,H),type:"observerResultsUpdated"})}))},dt);function Oe(t,e){return function(t,e){return!1!==Ut(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&Me(t,e,e.refetchOnMount)}function Me(t,e,s){if(!1!==Ut(e.enabled,t)){const i="function"==typeof s?s(t):s;return"always"===i||!1!==i&&ke(t,e)}return!1}function Ce(t,e,s,i){return(t!==e||!1===Ut(i.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&ke(t,s)}function ke(t,e){return!1!==Ut(e.enabled,t)&&t.isStaleByTime(Dt(e.staleTime,t))}var Re=(wt=class extends Ft{constructor(t,e){super(),kt(this,mt),kt(this,ft),kt(this,pt),kt(this,yt),kt(this,vt),Rt(this,ft,t),this.setOptions(e),this.bindMethods(),St(this,mt,bt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;const s=this.options;this.options=Ct(this,ft).defaultMutationOptions(t),_t(this.options,s)||Ct(this,ft).getMutationCache().notify({type:"observerOptionsUpdated",mutation:Ct(this,yt),observer:this}),(null==s?void 0:s.mutationKey)&&this.options.mutationKey&&It(s.mutationKey)!==It(this.options.mutationKey)?this.reset():"pending"===(null==(e=Ct(this,yt))?void 0:e.state.status)&&Ct(this,yt).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||null==(t=Ct(this,yt))||t.removeObserver(this)}onMutationUpdate(t){St(this,mt,bt).call(this),St(this,mt,gt).call(this,t)}getCurrentResult(){return Ct(this,pt)}reset(){var t;null==(t=Ct(this,yt))||t.removeObserver(this),Rt(this,yt,void 0),St(this,mt,bt).call(this),St(this,mt,gt).call(this)}mutate(t,e){var s;return Rt(this,vt,e),null==(s=Ct(this,yt))||s.removeObserver(this),Rt(this,yt,Ct(this,ft).getMutationCache().build(Ct(this,ft),this.options)),Ct(this,yt).addObserver(this),Ct(this,yt).execute(t)}},ft=new WeakMap,pt=new WeakMap,yt=new WeakMap,vt=new WeakMap,mt=new WeakSet,bt=function(){var t;const e=(null==(t=Ct(this,yt))?void 0:t.state)??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};Rt(this,pt,{...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset})},gt=function(t){he.batch((()=>{var e,s,i,n,r,a,o,h;if(Ct(this,vt)&&this.hasListeners()){const u=Ct(this,pt).variables,l=Ct(this,pt).context;"success"===(null==t?void 0:t.type)?(null==(s=(e=Ct(this,vt)).onSuccess)||s.call(e,t.data,u,l),null==(n=(i=Ct(this,vt)).onSettled)||n.call(i,t.data,null,u,l)):"error"===(null==t?void 0:t.type)&&(null==(a=(r=Ct(this,vt)).onError)||a.call(r,t.error,u,l),null==(h=(o=Ct(this,vt)).onSettled)||h.call(o,void 0,t.error,u,l))}this.listeners.forEach((t=>{t(Ct(this,pt))}))}))},wt),Se=Et.createContext(void 0),Pe=t=>{const e=Et.useContext(Se);if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},Ee=({client:t,children:e})=>(Et.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),qt.jsx(Se.Provider,{value:t,children:e})),qe=Et.createContext(!1);qe.Provider;var Fe=Et.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),xe=(t,e,s)=>e.fetchOptimistic(t).catch((()=>{s.clearReset()}));function Qe(t,e,s){var i,n,r,a,o;const h=Pe(),u=Et.useContext(qe),l=Et.useContext(Fe),c=h.defaultQueryOptions(t);null==(n=null==(i=h.getDefaultOptions().queries)?void 0:i._experimental_beforeQuery)||n.call(i,c),c._optimisticResults=u?"isRestoring":"optimistic",(t=>{const e=t.staleTime;t.suspense&&(t.staleTime="function"==typeof e?(...t)=>Math.max(e(...t),1e3):Math.max(e??1e3,1e3),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3)))})(c),((t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))})(c,l),(t=>{Et.useEffect((()=>{t.clearReset()}),[t])})(l);const d=!h.getQueryCache().get(c.queryHash),[f]=Et.useState((()=>new e(h,c))),p=f.getOptimisticResult(c),y=!u&&!1!==t.subscribed;if(Et.useSyncExternalStore(Et.useCallback((t=>{const e=y?f.subscribe(he.batchCalls(t)):Qt;return f.updateResult(),e}),[f,y]),(()=>f.getCurrentResult()),(()=>f.getCurrentResult())),Et.useEffect((()=>{f.setOptions(c)}),[c,f]),((t,e)=>(null==t?void 0:t.suspense)&&e.isPending)(c,p))throw xe(c,f,l);if((({result:t,errorResetBoundary:e,throwOnError:s,query:i,suspense:n})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(n&&void 0===t.data||Yt(s,[t.error,i])))({result:p,errorResetBoundary:l,throwOnError:c.throwOnError,query:h.getQueryCache().get(c.queryHash),suspense:c.suspense}))throw p.error;if(null==(a=null==(r=h.getDefaultOptions().queries)?void 0:r._experimental_afterQuery)||a.call(r,c,p),c.experimental_prefetchInRender&&!xt&&((t,e)=>t.isLoading&&t.isFetching&&!e)(p,u)){const t=d?xe(c,f,l):null==(o=h.getQueryCache().get(c.queryHash))?void 0:o.promise;null==t||t.catch(Qt).finally((()=>{f.updateResult()}))}return c.notifyOnChangeProps?p:f.trackResult(p)}function We(t,e){return Qe(t,we)}function Ae(t,e){const s=Pe(),[i]=Et.useState((()=>new Re(s,t)));Et.useEffect((()=>{i.setOptions(t)}),[i,t]);const n=Et.useSyncExternalStore(Et.useCallback((t=>i.subscribe(he.batchCalls(t))),[i]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),r=Et.useCallback(((t,e)=>{i.mutate(t,e).catch(Qt)}),[i]);if(n.error&&Yt(i.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:r,mutateAsync:n.mutate}}export{ge as Q,We as a,Ae as b,Ee as c,Pe as u};
//# sourceMappingURL=query-BAB1p6IC.js.map
