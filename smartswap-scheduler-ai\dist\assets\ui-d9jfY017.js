import{r as e,R as t,a as n,b as r,c as o}from"./vendor-DH5OV8M2.js";var i={exports:{}},a={},s=e,l=Symbol.for("react.element"),c=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(e,t,n){var r,o={},i=null,a=null;for(r in void 0!==n&&(i=""+n),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(a=t.ref),t)u.call(t,r)&&!f.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:l,type:e,key:i,ref:a,props:o,_owner:d.current}}a.Fragment=c,a.jsx=p,a.jsxs=p,i.exports=a;var m=i.exports;function v(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function h(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function g(...e){return t=>{let n=!1;const r=e.map((e=>{const r=h(e,t);return n||"function"!=typeof r||(n=!0),r}));if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():h(e[t],null)}}}}function y(...t){return e.useCallback(g(...t),t)}function w(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const i=e.createContext(o),a=r.length;r=[...r,o];const s=n=>{var r;const{scope:o,children:s,...l}=n,c=(null==(r=null==o?void 0:o[t])?void 0:r[a])||i,u=e.useMemo((()=>l),Object.values(l));return m.jsx(c.Provider,{value:u,children:s})};return s.displayName=n+"Provider",[s,function(r,s){var l;const c=(null==(l=null==s?void 0:s[t])?void 0:l[a])||i,u=e.useContext(c);if(u)return u;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},x(o,...n)]}function x(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function b(t){const n=S(t),r=e.forwardRef(((t,r)=>{const{children:o,...i}=t,a=e.Children.toArray(o),s=a.find(T);if(s){const t=s.props.children,o=a.map((n=>n===s?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n));return m.jsx(n,{...i,ref:r,children:e.isValidElement(t)?e.cloneElement(t,void 0,o):null})}return m.jsx(n,{...i,ref:r,children:o})}));return r.displayName=`${t}.Slot`,r}var E=b("Slot");function S(t){const n=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(r),i=function(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{const t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,r.props);return r.type!==e.Fragment&&(i.ref=n?g(n,t):t),e.cloneElement(r,i)}return e.Children.count(r)>1?e.Children.only(null):null}));return n.displayName=`${t}.SlotClone`,n}var C=Symbol("radix.slottable");function R(e){const t=({children:e})=>m.jsx(m.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=C,t}function T(t){return e.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===C}function P(e){const n=e+"CollectionProvider",[r,o]=w(n),[i,a]=r(n,{collectionRef:{current:null},itemMap:new Map}),s=e=>{const{scope:n,children:r}=e,o=t.useRef(null),a=t.useRef(new Map).current;return m.jsx(i,{scope:n,itemMap:a,collectionRef:o,children:r})};s.displayName=n;const l=e+"CollectionSlot",c=b(l),u=t.forwardRef(((e,t)=>{const{scope:n,children:r}=e,o=y(t,a(l,n).collectionRef);return m.jsx(c,{ref:o,children:r})}));u.displayName=l;const d=e+"CollectionItemSlot",f="data-radix-collection-item",p=b(d),v=t.forwardRef(((e,n)=>{const{scope:r,children:o,...i}=e,s=t.useRef(null),l=y(n,s),c=a(d,r);return t.useEffect((()=>(c.itemMap.set(s,{ref:s,...i}),()=>{c.itemMap.delete(s)}))),m.jsx(p,{[f]:"",ref:l,children:o})}));return v.displayName=d,[{Provider:s,Slot:u,ItemSlot:v},function(n){const r=a(e+"CollectionConsumer",n);return t.useCallback((()=>{const e=r.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(r.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[r.collectionRef,r.itemMap])},o]}var D=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce(((t,n)=>{const r=b(`Primitive.${n}`),o=e.forwardRef(((e,t)=>{const{asChild:o,...i}=e,a=o?r:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),m.jsx(a,{...i,ref:t})}));return o.displayName=`Primitive.${n}`,{...t,[n]:o}}),{});function N(e,t){e&&n.flushSync((()=>e.dispatchEvent(t)))}function _(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>{var t;return null==(t=n.current)?void 0:t.call(n,...e)}),[])}var A,O="dismissableLayer.update",j="dismissableLayer.pointerDownOutside",L="dismissableLayer.focusOutside",k=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),M=e.forwardRef(((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:s,onDismiss:l,...c}=t,u=e.useContext(k),[d,f]=e.useState(null),p=(null==d?void 0:d.ownerDocument)??(null==globalThis?void 0:globalThis.document),[,h]=e.useState({}),g=y(n,(e=>f(e))),w=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),b=w.indexOf(x),E=d?w.indexOf(d):-1,S=u.layersWithOutsidePointerEventsDisabled.size>0,C=E>=b,R=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=_(t),o=e.useRef(!1),i=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){W(j,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...u.branches].some((e=>e.contains(t)));C&&!n&&(null==i||i(e),null==s||s(e),e.defaultPrevented||null==l||l())}),p),T=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=_(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){W(L,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...u.branches].some((e=>e.contains(t)))||(null==a||a(e),null==s||s(e),e.defaultPrevented||null==l||l())}),p);return function(t,n=(null==globalThis?void 0:globalThis.document)){const r=_(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{E===u.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&l&&(e.preventDefault(),l()))}),p),e.useEffect((()=>{if(d)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(A=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),F(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=A)}}),[d,p,r,u]),e.useEffect((()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),F())}),[d,u]),e.useEffect((()=>{const e=()=>h({});return document.addEventListener(O,e),()=>document.removeEventListener(O,e)}),[]),m.jsx(D.div,{...c,ref:g,style:{pointerEvents:S?C?"auto":"none":void 0,...t.style},onFocusCapture:v(t.onFocusCapture,T.onFocusCapture),onBlurCapture:v(t.onBlurCapture,T.onBlurCapture),onPointerDownCapture:v(t.onPointerDownCapture,R.onPointerDownCapture)})}));M.displayName="DismissableLayer";var I=e.forwardRef(((t,n)=>{const r=e.useContext(k),o=e.useRef(null),i=y(n,o);return e.useEffect((()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}}),[r.branches]),m.jsx(D.div,{...t,ref:i})}));function F(){const e=new CustomEvent(O);document.dispatchEvent(e)}function W(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?N(o,i):o.dispatchEvent(i)}I.displayName="DismissableLayerBranch";var H=M,B=I,V=(null==globalThis?void 0:globalThis.document)?e.useLayoutEffect:()=>{},K=e.forwardRef(((t,n)=>{var o;const{container:i,...a}=t,[s,l]=e.useState(!1);V((()=>l(!0)),[]);const c=i||s&&(null==(o=null==globalThis?void 0:globalThis.document)?void 0:o.body);return c?r.createPortal(m.jsx(D.div,{...a,ref:n}),c):null}));K.displayName="Portal";var $=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef(null),i=e.useRef(t),a=e.useRef("none"),s=t?"mounted":"unmounted",[l,c]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=z(o.current);a.current="mounted"===l?e:"none"}),[l]),V((()=>{const e=o.current,n=i.current;if(n!==t){const r=a.current,o=z(e);if(t)c("MOUNT");else if("none"===o||"none"===(null==e?void 0:e.display))c("UNMOUNT");else{c(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}}),[t,c]),V((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=z(o.current).includes(r.animationName);if(r.target===n&&a&&(c("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},s=e=>{e.target===n&&(a.current=z(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}c("ANIMATION_END")}),[n,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:e.useCallback((e=>{o.current=e?getComputedStyle(e):null,r(e)}),[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=y(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:a}):null};function z(e){return(null==e?void 0:e.animationName)||"none"}$.displayName="Presence";var U=o[" useInsertionEffect ".trim().toString()]||V;function Y({prop:t,defaultProp:n,onChange:r=()=>{},caller:o}){const[i,a,s]=function({defaultProp:t,onChange:n}){const[r,o]=e.useState(t),i=e.useRef(r),a=e.useRef(n);return U((()=>{a.current=n}),[n]),e.useEffect((()=>{var e;i.current!==r&&(null==(e=a.current)||e.call(a,r),i.current=r)}),[r,i]),[r,o,a]}({defaultProp:n,onChange:r}),l=void 0!==t,c=l?t:i;{const n=e.useRef(void 0!==t);e.useEffect((()=>{const e=n.current;if(e!==l){}n.current=l}),[l,o])}const u=e.useCallback((e=>{var n;if(l){const r=function(e){return"function"==typeof e}(e)?e(t):e;r!==t&&(null==(n=s.current)||n.call(s,r))}else a(e)}),[l,t,a,s]);return[c,u]}var X=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),q=e.forwardRef(((e,t)=>m.jsx(D.span,{...e,ref:t,style:{...X,...e.style}})));q.displayName="VisuallyHidden";var Z=q,G="ToastProvider",[J,Q,ee]=P("Toast"),[te,ne]=w("Toast",[ee]),[re,oe]=te(G),ie=t=>{const{__scopeToast:n,label:r="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:s}=t,[l,c]=e.useState(null),[u,d]=e.useState(0),f=e.useRef(!1),p=e.useRef(!1);return r.trim(),m.jsx(J.Provider,{scope:n,children:m.jsx(re,{scope:n,label:r,duration:o,swipeDirection:i,swipeThreshold:a,toastCount:u,viewport:l,onViewportChange:c,onToastAdd:e.useCallback((()=>d((e=>e+1))),[]),onToastRemove:e.useCallback((()=>d((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:s})})};ie.displayName=G;var ae="ToastViewport",se=["F8"],le="toast.viewportPause",ce="toast.viewportResume",ue=e.forwardRef(((t,n)=>{const{__scopeToast:r,hotkey:o=se,label:i="Notifications ({hotkey})",...a}=t,s=oe(ae,r),l=Q(r),c=e.useRef(null),u=e.useRef(null),d=e.useRef(null),f=e.useRef(null),p=y(n,f,s.onViewportChange),v=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),h=s.toastCount>0;e.useEffect((()=>{const e=e=>{var t;0!==o.length&&o.every((t=>e[t]||e.code===t))&&(null==(t=f.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[o]),e.useEffect((()=>{const e=c.current,t=f.current;if(h&&e&&t){const n=()=>{if(!s.isClosePausedRef.current){const e=new CustomEvent(le);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},r=()=>{if(s.isClosePausedRef.current){const e=new CustomEvent(ce);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{!e.contains(t.relatedTarget)&&r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",i),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}}),[h,s.isClosePausedRef]);const g=e.useCallback((({tabbingDirection:e})=>{const t=l().map((t=>{const n=t.ref.current,r=[n,...De(n)];return"forwards"===e?r:r.reverse()}));return("forwards"===e?t.reverse():t).flat()}),[l]);return e.useEffect((()=>{const e=f.current;if(e){const t=t=>{var n,r,o;const i=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!i){const i=document.activeElement,a=t.shiftKey;if(t.target===e&&a)return void(null==(n=u.current)||n.focus());const s=g({tabbingDirection:a?"backwards":"forwards"}),l=s.findIndex((e=>e===i));Ne(s.slice(l+1))?t.preventDefault():a?null==(r=u.current)||r.focus():null==(o=d.current)||o.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[l,g]),m.jsxs(B,{ref:c,role:"region","aria-label":i.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:h?void 0:"none"},children:[h&&m.jsx(fe,{ref:u,onFocusFromOutsideViewport:()=>{Ne(g({tabbingDirection:"forwards"}))}}),m.jsx(J.Slot,{scope:r,children:m.jsx(D.ol,{tabIndex:-1,...a,ref:p})}),h&&m.jsx(fe,{ref:d,onFocusFromOutsideViewport:()=>{Ne(g({tabbingDirection:"backwards"}))}})]})}));ue.displayName=ae;var de="ToastFocusProxy",fe=e.forwardRef(((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=oe(de,n);return m.jsx(q,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;const n=e.relatedTarget;!(null==(t=i.viewport)?void 0:t.contains(n))&&r()}})}));fe.displayName=de;var pe="Toast",me=e.forwardRef(((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...a}=e,[s,l]=Y({prop:r,defaultProp:o??!0,onChange:i,caller:pe});return m.jsx($,{present:n||s,children:m.jsx(ge,{open:s,...a,ref:t,onClose:()=>l(!1),onPause:_(e.onPause),onResume:_(e.onResume),onSwipeStart:v(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:v(e.onSwipeMove,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)})),onSwipeCancel:v(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:v(e.onSwipeEnd,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),l(!1)}))})})}));me.displayName=pe;var[ve,he]=te(pe,{onClose(){}}),ge=e.forwardRef(((t,r)=>{const{__scopeToast:o,type:i="foreground",duration:a,open:s,onClose:l,onEscapeKeyDown:c,onPause:u,onResume:d,onSwipeStart:f,onSwipeMove:p,onSwipeCancel:h,onSwipeEnd:g,...w}=t,x=oe(pe,o),[b,E]=e.useState(null),S=y(r,(e=>E(e))),C=e.useRef(null),R=e.useRef(null),T=a||x.duration,P=e.useRef(0),N=e.useRef(T),A=e.useRef(0),{onToastAdd:O,onToastRemove:j}=x,L=_((()=>{var e;(null==b?void 0:b.contains(document.activeElement))&&(null==(e=x.viewport)||e.focus()),l()})),k=e.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(A.current),P.current=(new Date).getTime(),A.current=window.setTimeout(L,e))}),[L]);e.useEffect((()=>{const e=x.viewport;if(e){const t=()=>{k(N.current),null==d||d()},n=()=>{const e=(new Date).getTime()-P.current;N.current=N.current-e,window.clearTimeout(A.current),null==u||u()};return e.addEventListener(le,n),e.addEventListener(ce,t),()=>{e.removeEventListener(le,n),e.removeEventListener(ce,t)}}}),[x.viewport,T,u,d,k]),e.useEffect((()=>{s&&!x.isClosePausedRef.current&&k(T)}),[s,T,x.isClosePausedRef,k]),e.useEffect((()=>(O(),()=>j())),[O,j]);const M=e.useMemo((()=>b?Re(b):null),[b]);return x.viewport?m.jsxs(m.Fragment,{children:[M&&m.jsx(ye,{__scopeToast:o,role:"status","aria-live":"foreground"===i?"assertive":"polite","aria-atomic":!0,children:M}),m.jsx(ve,{scope:o,onClose:L,children:n.createPortal(m.jsx(J.ItemSlot,{scope:o,children:m.jsx(H,{asChild:!0,onEscapeKeyDown:v(c,(()=>{x.isFocusedToastEscapeKeyDownRef.current||L(),x.isFocusedToastEscapeKeyDownRef.current=!1})),children:m.jsx(D.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":x.swipeDirection,...w,ref:S,style:{userSelect:"none",touchAction:"none",...t.style},onKeyDown:v(t.onKeyDown,(e=>{"Escape"===e.key&&(null==c||c(e.nativeEvent),e.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,L()))})),onPointerDown:v(t.onPointerDown,(e=>{0===e.button&&(C.current={x:e.clientX,y:e.clientY})})),onPointerMove:v(t.onPointerMove,(e=>{if(!C.current)return;const t=e.clientX-C.current.x,n=e.clientY-C.current.y,r=Boolean(R.current),o=["left","right"].includes(x.swipeDirection),i=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,s=o?0:i(0,n),l="touch"===e.pointerType?10:2,c={x:a,y:s},u={originalEvent:e,delta:c};r?(R.current=c,Te("toast.swipeMove",p,u,{discrete:!1})):Pe(c,x.swipeDirection,l)?(R.current=c,Te("toast.swipeStart",f,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(C.current=null)})),onPointerUp:v(t.onPointerUp,(e=>{const t=R.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),R.current=null,C.current=null,t){const n=e.currentTarget,r={originalEvent:e,delta:t};Pe(t,x.swipeDirection,x.swipeThreshold)?Te("toast.swipeEnd",g,r,{discrete:!0}):Te("toast.swipeCancel",h,r,{discrete:!0}),n.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))})})}),x.viewport)})]}):null})),ye=t=>{const{__scopeToast:n,children:r,...o}=t,i=oe(pe,n),[a,s]=e.useState(!1),[l,c]=e.useState(!1);return function(e=()=>{}){const t=_(e);V((()=>{let e=0,n=0;return e=window.requestAnimationFrame((()=>n=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}}),[t])}((()=>s(!0))),e.useEffect((()=>{const e=window.setTimeout((()=>c(!0)),1e3);return()=>window.clearTimeout(e)}),[]),l?null:m.jsx(K,{asChild:!0,children:m.jsx(q,{...o,children:a&&m.jsxs(m.Fragment,{children:[i.label," ",r]})})})},we=e.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e;return m.jsx(D.div,{...r,ref:t})}));we.displayName="ToastTitle";var xe=e.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e;return m.jsx(D.div,{...r,ref:t})}));xe.displayName="ToastDescription";var be=e.forwardRef(((e,t)=>{const{altText:n,...r}=e;return n.trim()?m.jsx(Ce,{altText:n,asChild:!0,children:m.jsx(Se,{...r,ref:t})}):null}));be.displayName="ToastAction";var Ee="ToastClose",Se=e.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e,o=he(Ee,n);return m.jsx(Ce,{asChild:!0,children:m.jsx(D.button,{type:"button",...r,ref:t,onClick:v(e.onClick,o.onClose)})})}));Se.displayName=Ee;var Ce=e.forwardRef(((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return m.jsx(D.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})}));function Re(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const n=e.ariaHidden||e.hidden||"none"===e.style.display,r=""===e.dataset.radixToastAnnounceExclude;if(!n)if(r){const n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...Re(e))}})),t}function Te(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?N(o,i):o.dispatchEvent(i)}var Pe=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n};function De(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ne(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var _e=ie,Ae=ue,Oe=me,je=we,Le=xe,ke=be,Me=Se,Ie=o[" useId ".trim().toString()]||(()=>{}),Fe=0;function We(t){const[n,r]=e.useState(Ie());return V((()=>{r((e=>e??String(Fe++)))}),[t]),n?`radix-${n}`:""}const He=["top","right","bottom","left"],Be=Math.min,Ve=Math.max,Ke=Math.round,$e=Math.floor,ze=e=>({x:e,y:e}),Ue={left:"right",right:"left",bottom:"top",top:"bottom"},Ye={start:"end",end:"start"};function Xe(e,t,n){return Ve(e,Be(t,n))}function qe(e,t){return"function"==typeof e?e(t):e}function Ze(e){return e.split("-")[0]}function Ge(e){return e.split("-")[1]}function Je(e){return"x"===e?"y":"x"}function Qe(e){return"y"===e?"height":"width"}function et(e){return["top","bottom"].includes(Ze(e))?"y":"x"}function tt(e){return Je(et(e))}function nt(e){return e.replace(/start|end/g,(e=>Ye[e]))}function rt(e){return e.replace(/left|right|bottom|top/g,(e=>Ue[e]))}function ot(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function it(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function at(e,t,n){let{reference:r,floating:o}=e;const i=et(t),a=tt(t),s=Qe(a),l=Ze(t),c="y"===i,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[s]/2-o[s]/2;let p;switch(l){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Ge(t)){case"start":p[a]-=f*(n&&c?-1:1);break;case"end":p[a]+=f*(n&&c?-1:1)}return p}async function st(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=qe(t,e),m=ot(p),v=s[f?"floating"===d?"reference":"floating":d],h=it(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:c,rootBoundary:u,strategy:l})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=it(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:y,strategy:l}):g);return{top:(h.top-x.top+m.top)/w.y,bottom:(x.bottom-h.bottom+m.bottom)/w.y,left:(h.left-x.left+m.left)/w.x,right:(x.right-h.right+m.right)/w.x}}function lt(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ct(e){return He.some((t=>e[t]>=0))}function ut(){return"undefined"!=typeof window}function dt(e){return mt(e)?(e.nodeName||"").toLowerCase():"#document"}function ft(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function pt(e){var t;return null==(t=(mt(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function mt(e){return!!ut()&&(e instanceof Node||e instanceof ft(e).Node)}function vt(e){return!!ut()&&(e instanceof Element||e instanceof ft(e).Element)}function ht(e){return!!ut()&&(e instanceof HTMLElement||e instanceof ft(e).HTMLElement)}function gt(e){return!(!ut()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ft(e).ShadowRoot)}function yt(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ct(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function wt(e){return["table","td","th"].includes(dt(e))}function xt(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function bt(e){const t=Et(),n=vt(e)?Ct(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Et(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function St(e){return["html","body","#document"].includes(dt(e))}function Ct(e){return ft(e).getComputedStyle(e)}function Rt(e){return vt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Tt(e){if("html"===dt(e))return e;const t=e.assignedSlot||e.parentNode||gt(e)&&e.host||pt(e);return gt(t)?t.host:t}function Pt(e){const t=Tt(e);return St(t)?e.ownerDocument?e.ownerDocument.body:e.body:ht(t)&&yt(t)?t:Pt(t)}function Dt(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Pt(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ft(o);if(i){const e=Nt(a);return t.concat(a,a.visualViewport||[],yt(o)?o:[],e&&n?Dt(e):[])}return t.concat(o,Dt(o,[],n))}function Nt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function _t(e){const t=Ct(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ht(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=Ke(n)!==i||Ke(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function At(e){return vt(e)?e:e.contextElement}function Ot(e){const t=At(e);if(!ht(t))return ze(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=_t(t);let a=(i?Ke(n.width):n.width)/r,s=(i?Ke(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const jt=ze(0);function Lt(e){const t=ft(e);return Et()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:jt}function kt(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=At(e);let a=ze(1);t&&(r?vt(r)&&(a=Ot(r)):a=Ot(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ft(e))&&t}(i,n,r)?Lt(i):ze(0);let l=(o.left+s.x)/a.x,c=(o.top+s.y)/a.y,u=o.width/a.x,d=o.height/a.y;if(i){const e=ft(i),t=r&&vt(r)?ft(r):r;let n=e,o=Nt(n);for(;o&&r&&t!==n;){const e=Ot(o),t=o.getBoundingClientRect(),r=Ct(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,c*=e.y,u*=e.x,d*=e.y,l+=i,c+=a,n=ft(o),o=Nt(n)}}return it({width:u,height:d,x:l,y:c})}function Mt(e,t){const n=Rt(e).scrollLeft;return t?t.left+n:kt(pt(e)).left+n}function It(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Mt(e,r)),y:r.top+t.scrollTop}}function Ft(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ft(e),r=pt(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;const e=Et();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}(e,n);else if("document"===t)r=function(e){const t=pt(e),n=Rt(e),r=e.ownerDocument.body,o=Ve(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Ve(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Mt(e);const s=-n.scrollTop;return"rtl"===Ct(r).direction&&(a+=Ve(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(pt(e));else if(vt(t))r=function(e,t){const n=kt(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ht(e)?Ot(e):ze(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=Lt(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return it(r)}function Wt(e,t){const n=Tt(e);return!(n===t||!vt(n)||St(n))&&("fixed"===Ct(n).position||Wt(n,t))}function Ht(e,t,n){const r=ht(t),o=pt(t),i="fixed"===n,a=kt(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const l=ze(0);function c(){l.x=Mt(o)}if(r||!r&&!i)if(("body"!==dt(t)||yt(o))&&(s=Rt(t)),r){const e=kt(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&c();i&&!r&&o&&c();const u=!o||r||i?ze(0):It(o,s);return{x:a.left+s.scrollLeft-l.x-u.x,y:a.top+s.scrollTop-l.y-u.y,width:a.width,height:a.height}}function Bt(e){return"static"===Ct(e).position}function Vt(e,t){if(!ht(e)||"fixed"===Ct(e).position)return null;if(t)return t(e);let n=e.offsetParent;return pt(e)===n&&(n=n.ownerDocument.body),n}function Kt(e,t){const n=ft(e);if(xt(e))return n;if(!ht(e)){let t=Tt(e);for(;t&&!St(t);){if(vt(t)&&!Bt(t))return t;t=Tt(t)}return n}let r=Vt(e,t);for(;r&&wt(r)&&Bt(r);)r=Vt(r,t);return r&&St(r)&&Bt(r)&&!bt(r)?n:r||function(e){let t=Tt(e);for(;ht(t)&&!St(t);){if(bt(t))return t;if(xt(t))return null;t=Tt(t)}return null}(e)||n}const $t={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=pt(r),s=!!t&&xt(t.floating);if(r===a||s&&i)return n;let l={scrollLeft:0,scrollTop:0},c=ze(1);const u=ze(0),d=ht(r);if((d||!d&&!i)&&(("body"!==dt(r)||yt(a))&&(l=Rt(r)),ht(r))){const e=kt(r);c=Ot(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}const f=!a||d||i?ze(0):It(a,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-l.scrollTop*c.y+u.y+f.y}},getDocumentElement:pt,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?xt(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=Dt(e,[],!1).filter((e=>vt(e)&&"body"!==dt(e))),o=null;const i="fixed"===Ct(e).position;let a=i?Tt(e):e;for(;vt(a)&&!St(a);){const t=Ct(a),n=bt(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||yt(a)&&!n&&Wt(e,a))?r=r.filter((e=>e!==a)):o=t,a=Tt(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],s=i.reduce(((e,n)=>{const r=Ft(t,n,o);return e.top=Ve(r.top,e.top),e.right=Be(r.right,e.right),e.bottom=Be(r.bottom,e.bottom),e.left=Ve(r.left,e.left),e}),Ft(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:Kt,getElementRects:async function(e){const t=this.getOffsetParent||Kt,n=this.getDimensions,r=await n(e.floating);return{reference:Ht(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=_t(e);return{width:t,height:n}},getScale:Ot,isElement:vt,isRTL:function(e){return"rtl"===Ct(e).direction}};function zt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Ut(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,c=At(e),u=o||i?[...c?Dt(c):[],...Dt(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=c&&s?function(e,t){let n,r=null;const o=pt(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),i();const c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(s||t(),!f||!p)return;const m={rootMargin:-$e(d)+"px "+-$e(o.clientWidth-(u+f))+"px "+-$e(o.clientHeight-(d+p))+"px "+-$e(u)+"px",threshold:Ve(0,Be(1,l))||1};let v=!0;function h(t){const r=t[0].intersectionRatio;if(r!==l){if(!v)return a();r?a(!1,r):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}1!==r||zt(c,e.getBoundingClientRect())||a(),v=!1}try{r=new IntersectionObserver(h,{...m,root:o.ownerDocument})}catch(g){r=new IntersectionObserver(h,m)}r.observe(e)}(!0),i}(c,n):null;let f,p=-1,m=null;a&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),c&&!l&&m.observe(c),m.observe(t));let v=l?kt(e):null;return l&&function t(){const r=kt(e);v&&!zt(v,r)&&n();v=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,l&&cancelAnimationFrame(f)}}const Yt=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:s}=t,l=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=Ze(n),s=Ge(n),l="y"===et(n),c=["left","top"].includes(a)?-1:1,u=i&&l?-1:1,d=qe(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof m&&(p="end"===s?-1*m:m),l?{x:p*u,y:f*c}:{x:f*c,y:p*u}}(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}},Xt=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=qe(e,t),c={x:n,y:r},u=await st(t,l),d=et(Ze(o)),f=Je(d);let p=c[f],m=c[d];if(i){const e="y"===f?"bottom":"right";p=Xe(p+u["y"===f?"top":"left"],p,p-u[e])}if(a){const e="y"===d?"bottom":"right";m=Xe(m+u["y"===d?"top":"left"],m,m-u[e])}const v=s.fn({...t,[f]:p,[d]:m});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[d]:a}}}}}},qt=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:s,platform:l,elements:c}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:v=!0,...h}=qe(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=Ze(o),y=et(s),w=Ze(s)===s,x=await(null==l.isRTL?void 0:l.isRTL(c.floating)),b=f||(w||!v?[rt(s)]:function(e){const t=rt(e);return[nt(e),t,nt(t)]}(s)),E="none"!==m;!f&&E&&b.push(...function(e,t,n,r){const o=Ge(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}(Ze(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(nt)))),i}(s,v,m,x));const S=[s,...b],C=await st(t,h),R=[];let T=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&R.push(C[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=Ge(e),o=tt(e),i=Qe(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=rt(a)),[a,rt(a)]}(o,a,x);R.push(C[e[0]],C[e[1]])}if(T=[...T,{placement:o,overflows:R}],!R.every((e=>e<=0))){var P,D;const e=((null==(P=i.flip)?void 0:P.index)||0)+1,t=S[e];if(t){var N;const n="alignment"===d&&y!==et(t),r=(null==(N=T[0])?void 0:N.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:T},reset:{placement:t}}}let n=null==(D=T.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:D.placement;if(!n)switch(p){case"bestFit":{var _;const e=null==(_=T.filter((e=>{if(E){const t=et(e.placement);return t===y||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:_[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},Zt=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:a,elements:s}=t,{apply:l=()=>{},...c}=qe(e,t),u=await st(t,c),d=Ze(o),f=Ge(o),p="y"===et(o),{width:m,height:v}=i.floating;let h,g;"top"===d||"bottom"===d?(h=d,g=f===(await(null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(g=d,h="end"===f?"top":"bottom");const y=v-u.top-u.bottom,w=m-u.left-u.right,x=Be(v-u[h],y),b=Be(m-u[g],w),E=!t.middlewareData.shift;let S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!f){const e=Ve(u.left,0),t=Ve(u.right,0),n=Ve(u.top,0),r=Ve(u.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:Ve(u.left,u.right)):S=v-2*(0!==n||0!==r?n+r:Ve(u.top,u.bottom))}await l({...t,availableWidth:C,availableHeight:S});const R=await a.getDimensions(s.floating);return m!==R.width||v!==R.height?{reset:{rects:!0}}:{}}}},Gt=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=qe(e,t);switch(r){case"referenceHidden":{const e=lt(await st(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:ct(e)}}}case"escaped":{const e=lt(await st(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:ct(e)}}}default:return{}}}}},Jt=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:a,elements:s,middlewareData:l}=t,{element:c,padding:u=0}=qe(e,t)||{};if(null==c)return{};const d=ot(u),f={x:n,y:r},p=tt(o),m=Qe(p),v=await a.getDimensions(c),h="y"===p,g=h?"top":"left",y=h?"bottom":"right",w=h?"clientHeight":"clientWidth",x=i.reference[m]+i.reference[p]-f[p]-i.floating[m],b=f[p]-i.reference[p],E=await(null==a.getOffsetParent?void 0:a.getOffsetParent(c));let S=E?E[w]:0;S&&await(null==a.isElement?void 0:a.isElement(E))||(S=s.floating[w]||i.floating[m]);const C=x/2-b/2,R=S/2-v[m]/2-1,T=Be(d[g],R),P=Be(d[y],R),D=T,N=S-v[m]-P,_=S/2-v[m]/2+C,A=Xe(D,_,N),O=!l.arrow&&null!=Ge(o)&&_!==A&&i.reference[m]/2-(_<D?T:P)-v[m]/2<0,j=O?_<D?_-D:_-N:0;return{[p]:f[p]+j,data:{[p]:A,centerOffset:_-A-j,...O&&{alignmentOffset:j}},reset:O}}}),Qt=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:c=!0}=qe(e,t),u={x:n,y:r},d=et(o),f=Je(d);let p=u[f],m=u[d];const v=qe(s,t),h="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(l){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+h.mainAxis,n=i.reference[f]+i.reference[e]-h.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;const e="y"===f?"width":"height",t=["top","left"].includes(Ze(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:h.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?h.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},en=(e,t,n)=>{const r=new Map,o={platform:$t,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),l=await(null==a.isRTL?void 0:a.isRTL(t));let c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=at(c,r,l),f=r,p={},m=0;for(let v=0;v<s.length;v++){const{name:n,fn:i}=s[v],{x:h,y:g,data:y,reset:w}=await i({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=h?h:u,d=null!=g?g:d,p={...p,[n]:{...p[n],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:u,y:d}=at(c,f,l))),v=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var tn="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function nn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!nn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!nn(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function rn(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function on(e,t){const n=rn(e);return Math.round(t*n)/n}function an(t){const n=e.useRef(t);return tn((()=>{n.current=t})),n}const sn=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Jt({element:n.current,padding:r}).fn(t):{}:n?Jt({element:n,padding:r}).fn(t):{};var o}}),ln=(e,t)=>({...Yt(e),options:[e,t]}),cn=(e,t)=>({...Xt(e),options:[e,t]}),un=(e,t)=>({...Qt(e),options:[e,t]}),dn=(e,t)=>({...qt(e),options:[e,t]}),fn=(e,t)=>({...Zt(e),options:[e,t]}),pn=(e,t)=>({...Gt(e),options:[e,t]}),mn=(e,t)=>({...sn(e),options:[e,t]});var vn=e.forwardRef(((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return m.jsx(D.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})}));vn.displayName="Arrow";var hn=vn;function gn(t){const[n,r]=e.useState(void 0);return V((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}var yn="Popper",[wn,xn]=w(yn),[bn,En]=wn(yn),Sn=t=>{const{__scopePopper:n,children:r}=t,[o,i]=e.useState(null);return m.jsx(bn,{scope:n,anchor:o,onAnchorChange:i,children:r})};Sn.displayName=yn;var Cn="PopperAnchor",Rn=e.forwardRef(((t,n)=>{const{__scopePopper:r,virtualRef:o,...i}=t,a=En(Cn,r),s=e.useRef(null),l=y(n,s);return e.useEffect((()=>{a.onAnchorChange((null==o?void 0:o.current)||s.current)})),o?null:m.jsx(D.div,{...i,ref:l})}));Rn.displayName=Cn;var Tn="PopperContent",[Pn,Dn]=wn(Tn),Nn=e.forwardRef(((t,r)=>{var o,i,a,s,l,c;const{__scopePopper:u,side:d="bottom",sideOffset:f=0,align:p="center",alignOffset:v=0,arrowPadding:h=0,avoidCollisions:g=!0,collisionBoundary:w=[],collisionPadding:x=0,sticky:b="partial",hideWhenDetached:E=!1,updatePositionStrategy:S="optimized",onPlaced:C,...R}=t,T=En(Tn,u),[P,N]=e.useState(null),A=y(r,(e=>N(e))),[O,j]=e.useState(null),L=gn(O),k=(null==L?void 0:L.width)??0,M=(null==L?void 0:L.height)??0,I=d+("center"!==p?"-"+p:""),F="number"==typeof x?x:{top:0,right:0,bottom:0,left:0,...x},W=Array.isArray(w)?w:[w],H=W.length>0,B={padding:F,boundary:W.filter(jn),altBoundary:H},{refs:K,floatingStyles:$,placement:z,isPositioned:U,middlewareData:Y}=function(t){void 0===t&&(t={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a,elements:{reference:s,floating:l}={},transform:c=!0,whileElementsMounted:u,open:d}=t,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[m,v]=e.useState(i);nn(m,i)||v(i);const[h,g]=e.useState(null),[y,w]=e.useState(null),x=e.useCallback((e=>{e!==C.current&&(C.current=e,g(e))}),[]),b=e.useCallback((e=>{e!==R.current&&(R.current=e,w(e))}),[]),E=s||h,S=l||y,C=e.useRef(null),R=e.useRef(null),T=e.useRef(f),P=null!=u,D=an(u),N=an(a),_=an(d),A=e.useCallback((()=>{if(!C.current||!R.current)return;const e={placement:r,strategy:o,middleware:m};N.current&&(e.platform=N.current),en(C.current,R.current,e).then((e=>{const t={...e,isPositioned:!1!==_.current};O.current&&!nn(T.current,t)&&(T.current=t,n.flushSync((()=>{p(t)})))}))}),[m,r,o,N,_]);tn((()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const O=e.useRef(!1);tn((()=>(O.current=!0,()=>{O.current=!1})),[]),tn((()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(D.current)return D.current(E,S,A);A()}}),[E,S,A,D,P]);const j=e.useMemo((()=>({reference:C,floating:R,setReference:x,setFloating:b})),[x,b]),L=e.useMemo((()=>({reference:E,floating:S})),[E,S]),k=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!L.floating)return e;const t=on(L.floating,f.x),n=on(L.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+n+"px)",...rn(L.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,c,L.floating,f.x,f.y]);return e.useMemo((()=>({...f,update:A,refs:j,elements:L,floatingStyles:k})),[f,A,j,L,k])}({strategy:"fixed",placement:I,whileElementsMounted:(...e)=>Ut(...e,{animationFrame:"always"===S}),elements:{reference:T.anchor},middleware:[ln({mainAxis:f+M,alignmentAxis:v}),g&&cn({mainAxis:!0,crossAxis:!1,limiter:"partial"===b?un():void 0,...B}),g&&dn({...B}),fn({...B,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),O&&mn({element:O,padding:h}),Ln({arrowWidth:k,arrowHeight:M}),E&&pn({strategy:"referenceHidden",...B})]}),[X,q]=kn(z),Z=_(C);V((()=>{U&&(null==Z||Z())}),[U,Z]);const G=null==(o=Y.arrow)?void 0:o.x,J=null==(i=Y.arrow)?void 0:i.y,Q=0!==(null==(a=Y.arrow)?void 0:a.centerOffset),[ee,te]=e.useState();return V((()=>{P&&te(window.getComputedStyle(P).zIndex)}),[P]),m.jsx("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...$,transform:U?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[null==(s=Y.transformOrigin)?void 0:s.x,null==(l=Y.transformOrigin)?void 0:l.y].join(" "),...(null==(c=Y.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:m.jsx(Pn,{scope:u,placedSide:X,onArrowChange:j,arrowX:G,arrowY:J,shouldHideArrow:Q,children:m.jsx(D.div,{"data-side":X,"data-align":q,...R,ref:A,style:{...R.style,animation:U?void 0:"none"}})})})}));Nn.displayName=Tn;var _n="PopperArrow",An={top:"bottom",right:"left",bottom:"top",left:"right"},On=e.forwardRef((function(e,t){const{__scopePopper:n,...r}=e,o=Dn(_n,n),i=An[o.placedSide];return m.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:m.jsx(hn,{...r,ref:t,style:{...r.style,display:"block"}})})}));function jn(e){return null!==e}On.displayName=_n;var Ln=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o;const{placement:i,rects:a,middlewareData:s}=t,l=0!==(null==(n=s.arrow)?void 0:n.centerOffset),c=l?0:e.arrowWidth,u=l?0:e.arrowHeight,[d,f]=kn(i),p={start:"0%",center:"50%",end:"100%"}[f],m=((null==(r=s.arrow)?void 0:r.x)??0)+c/2,v=((null==(o=s.arrow)?void 0:o.y)??0)+u/2;let h="",g="";return"bottom"===d?(h=l?p:`${m}px`,g=-u+"px"):"top"===d?(h=l?p:`${m}px`,g=`${a.floating.height+u}px`):"right"===d?(h=-u+"px",g=l?p:`${v}px`):"left"===d&&(h=`${a.floating.width+u}px`,g=l?p:`${v}px`),{data:{x:h,y:g}}}});function kn(e){const[t,n="center"]=e.split("-");return[t,n]}var Mn=Sn,In=Rn,Fn=Nn,Wn=On;function Hn(e,[t,n]){return Math.min(n,Math.max(t,e))}var Bn=e.createContext(void 0);function Vn(t){const n=e.useContext(Bn);return t||n||"ltr"}var Kn=0;function $n(){e.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??zn()),document.body.insertAdjacentElement("beforeend",e[1]??zn()),Kn++,()=>{1===Kn&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),Kn--}}),[])}function zn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Un="focusScope.autoFocusOnMount",Yn="focusScope.autoFocusOnUnmount",Xn={bubbles:!1,cancelable:!0},qn=e.forwardRef(((t,n)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...s}=t,[l,c]=e.useState(null),u=_(i),d=_(a),f=e.useRef(null),p=y(n,(e=>c(e))),v=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect((()=>{if(o){let e=function(e){if(v.paused||!l)return;const t=e.target;l.contains(t)?f.current=t:Qn(f.current,{select:!0})},t=function(e){if(v.paused||!l)return;const t=e.relatedTarget;null!==t&&(l.contains(t)||Qn(f.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&Qn(l)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return l&&r.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[o,l,v.paused]),e.useEffect((()=>{if(l){er.add(v);const t=document.activeElement;if(!l.contains(t)){const n=new CustomEvent(Un,Xn);l.addEventListener(Un,u),l.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Qn(r,{select:t}),document.activeElement!==n)return}((e=Zn(l),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&Qn(l))}return()=>{l.removeEventListener(Un,u),setTimeout((()=>{const e=new CustomEvent(Yn,Xn);l.addEventListener(Yn,d),l.dispatchEvent(e),e.defaultPrevented||Qn(t??document.body,{select:!0}),l.removeEventListener(Yn,d),er.remove(v)}),0)}}var e}),[l,u,d,v]);const h=e.useCallback((e=>{if(!r&&!o)return;if(v.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[o,i]=function(e){const t=Zn(e),n=Gn(t,e),r=Gn(t.reverse(),e);return[n,r]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&Qn(i,{select:!0})):(e.preventDefault(),r&&Qn(o,{select:!0})):n===t&&e.preventDefault()}}),[r,o,v.paused]);return m.jsx(D.div,{tabIndex:-1,...s,ref:p,onKeyDown:h})}));function Zn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Gn(e,t){for(const n of e)if(!Jn(n,{upTo:t}))return n}function Jn(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function Qn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}qn.displayName="FocusScope";var er=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=tr(e,t),e.unshift(t)},remove(t){var n;e=tr(e,t),null==(n=e[0])||n.resume()}}}();function tr(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}function nr(t){const n=e.useRef({value:t,previous:t});return e.useMemo((()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous)),[t])}var rr=new WeakMap,or=new WeakMap,ir={},ar=0,sr=function(e){return e&&(e.host||sr(e.parentNode))},lr=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=sr(t);return n&&e.contains(n)?n:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);ir[n]||(ir[n]=new WeakMap);var i=ir[n],a=[],s=new Set,l=new Set(o),c=function(e){e&&!s.has(e)&&(s.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){e&&!l.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(rr.get(e)||0)+1,c=(i.get(e)||0)+1;rr.set(e,l),i.set(e,c),a.push(e),1===l&&o&&or.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}}))};return u(t),s.clear(),ar++,function(){a.forEach((function(e){var t=rr.get(e)-1,o=i.get(e)-1;rr.set(e,t),i.set(e,o),t||(or.has(e)||e.removeAttribute(r),or.delete(e)),o||e.removeAttribute(n)})),--ar||(rr=new WeakMap,rr=new WeakMap,or=new WeakMap,ir={})}},cr=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),lr(r,o,n,"aria-hidden")):function(){return null}},ur=function(){return ur=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ur.apply(this,arguments)};function dr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var fr="right-scroll-bar-position",pr="width-before-scroll-bar";function mr(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var vr="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,hr=new WeakMap;function gr(t,n){var r,o,i,a=(r=null,o=function(e){return t.forEach((function(t){return mr(t,e)}))},(i=e.useState((function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}}))[0]).callback=o,i.facade);return vr((function(){var e=hr.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach((function(e){r.has(e)||mr(e,null)})),r.forEach((function(e){n.has(e)||mr(e,o)}))}hr.set(a,t)}),[t]),a}function yr(e){return e}var wr=function(t){var n=t.sideCar,r=dr(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,ur({},r))};wr.isSideCarExport=!0;var xr=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=yr);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=ur({async:!0,ssr:!1},e),t}(),br=function(){},Er=e.forwardRef((function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:br,onWheelCapture:br,onTouchMoveCapture:br}),i=o[0],a=o[1],s=t.forwardProps,l=t.children,c=t.className,u=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noRelative,v=t.noIsolation,h=t.inert,g=t.allowPinchZoom,y=t.as,w=void 0===y?"div":y,x=t.gapMode,b=dr(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=p,S=gr([r,n]),C=ur(ur({},b),i);return e.createElement(e.Fragment,null,d&&e.createElement(E,{sideCar:xr,removeScrollBar:u,shards:f,noRelative:m,noIsolation:v,inert:h,setCallbacks:a,allowPinchZoom:!!g,lockRef:r,gapMode:x}),s?e.cloneElement(e.Children.only(l),ur(ur({},C),{ref:S})):e.createElement(w,ur({},C,{className:c,ref:S}),l))}));Er.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Er.classNames={fullWidth:pr,zeroRight:fr};function Sr(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var Cr=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=Sr())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Rr=function(){var t,n=(t=Cr(),function(n,r){e.useEffect((function(){return t.add(n),function(){t.remove()}}),[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},Tr={left:0,top:0,right:0,gap:0},Pr=function(e){return parseInt(e||"",10)||0},Dr=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Tr;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[Pr(n),Pr(r),Pr(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Nr=Rr(),_r="data-scroll-locked",Ar=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(_r,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(fr," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(pr," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(fr," .").concat(fr," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(pr," .").concat(pr," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(_r,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},Or=function(){var e=parseInt(document.body.getAttribute(_r)||"0",10);return isFinite(e)?e:0},jr=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,i=void 0===o?"margin":o;e.useEffect((function(){return document.body.setAttribute(_r,(Or()+1).toString()),function(){var e=Or()-1;e<=0?document.body.removeAttribute(_r):document.body.setAttribute(_r,e.toString())}}),[]);var a=e.useMemo((function(){return Dr(i)}),[i]);return e.createElement(Nr,{styles:Ar(a,!n,i,r?"":"!important")})},Lr=!1;if("undefined"!=typeof window)try{var kr=Object.defineProperty({},"passive",{get:function(){return Lr=!0,!0}});window.addEventListener("test",kr,kr),window.removeEventListener("test",kr,kr)}catch(ea){Lr=!1}var Mr=!!Lr&&{passive:!1},Ir=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},Fr=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Wr(e,r)){var o=Hr(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Wr=function(e,t){return"v"===e?function(e){return Ir(e,"overflowY")}(t):function(e){return Ir(e,"overflowX")}(t)},Hr=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Br=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Vr=function(e){return[e.deltaX,e.deltaY]},Kr=function(e){return e&&"current"in e?e.current:e},$r=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},zr=0,Ur=[];function Yr(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Xr=(qr=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),i=e.useState(zr++)[0],a=e.useState(Rr)[0],s=e.useRef(t);e.useEffect((function(){s.current=t}),[t]),e.useEffect((function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(Kr),!0).filter(Boolean);return e.forEach((function(e){return e.classList.add("allow-interactivity-".concat(i))})),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(i))}))}}}),[t.inert,t.lockRef.current,t.shards]);var l=e.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var n,i=Br(e),a=r.current,l="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],u=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=Fr(d,u);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=Fr(d,u)),!f)return!1;if(!o.current&&"changedTouches"in e&&(l||c)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r){var o=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,s=t.contains(a),l=!1,c=i>0,u=0,d=0;do{var f=Hr(e,a),p=f[0],m=f[1]-f[2]-o*p;(p||m)&&Wr(e,a)&&(u+=m,d+=p),a=a.parentNode.host||a.parentNode}while(!s&&a!==document.body||s&&(t.contains(a)||t===a));return(c&&Math.abs(u)<1||!c&&Math.abs(d)<1)&&(l=!0),l}(p,t,e,"h"===p?l:c)}),[]),c=e.useCallback((function(e){var t=e;if(Ur.length&&Ur[Ur.length-1]===a){var r="deltaY"in t?Vr(t):Br(t),o=n.current.filter((function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o}))[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var i=(s.current.shards||[]).map(Kr).filter(Boolean).filter((function(e){return e.contains(t.target)}));(i.length>0?l(t,i[0]):!s.current.noIsolation)&&t.cancelable&&t.preventDefault()}}}),[]),u=e.useCallback((function(e,t,r,o){var i={name:e,delta:t,target:r,should:o,shadowParent:Yr(r)};n.current.push(i),setTimeout((function(){n.current=n.current.filter((function(e){return e!==i}))}),1)}),[]),d=e.useCallback((function(e){r.current=Br(e),o.current=void 0}),[]),f=e.useCallback((function(e){u(e.type,Vr(e),e.target,l(e,t.lockRef.current))}),[]),p=e.useCallback((function(e){u(e.type,Br(e),e.target,l(e,t.lockRef.current))}),[]);e.useEffect((function(){return Ur.push(a),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,Mr),document.addEventListener("touchmove",c,Mr),document.addEventListener("touchstart",d,Mr),function(){Ur=Ur.filter((function(e){return e!==a})),document.removeEventListener("wheel",c,Mr),document.removeEventListener("touchmove",c,Mr),document.removeEventListener("touchstart",d,Mr)}}),[]);var m=t.removeScrollBar,v=t.inert;return e.createElement(e.Fragment,null,v?e.createElement(a,{styles:$r(i)}):null,m?e.createElement(jr,{noRelative:t.noRelative,gapMode:t.gapMode}):null)},xr.useMedium(qr),wr);var qr,Zr=e.forwardRef((function(t,n){return e.createElement(Er,ur({},t,{ref:n,sideCar:Xr}))}));Zr.classNames=Er.classNames;var Gr=[" ","Enter","ArrowUp","ArrowDown"],Jr=[" ","Enter"],Qr="Select",[eo,to,no]=P(Qr),[ro,oo]=w(Qr,[no,xn]),io=xn(),[ao,so]=ro(Qr),[lo,co]=ro(Qr),uo=t=>{const{__scopeSelect:n,children:r,open:o,defaultOpen:i,onOpenChange:a,value:s,defaultValue:l,onValueChange:c,dir:u,name:d,autoComplete:f,disabled:p,required:v,form:h}=t,g=io(n),[y,w]=e.useState(null),[x,b]=e.useState(null),[E,S]=e.useState(!1),C=Vn(u),[R,T]=Y({prop:o,defaultProp:i??!1,onChange:a,caller:Qr}),[P,D]=Y({prop:s,defaultProp:l,onChange:c,caller:Qr}),N=e.useRef(null),_=!y||(h||!!y.closest("form")),[A,O]=e.useState(new Set),j=Array.from(A).map((e=>e.props.value)).join(";");return m.jsx(Mn,{...g,children:m.jsxs(ao,{required:v,scope:n,trigger:y,onTriggerChange:w,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:E,onValueNodeHasChildrenChange:S,contentId:We(),value:P,onValueChange:D,open:R,onOpenChange:T,dir:C,triggerPointerDownPosRef:N,disabled:p,children:[m.jsx(eo.Provider,{scope:n,children:m.jsx(lo,{scope:t.__scopeSelect,onNativeOptionAdd:e.useCallback((e=>{O((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:e.useCallback((e=>{O((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:r})}),_?m.jsxs(Go,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:f,value:P,onChange:e=>D(e.target.value),disabled:p,form:h,children:[void 0===P?m.jsx("option",{value:""}):null,Array.from(A)]},j):null]})})};uo.displayName=Qr;var fo="SelectTrigger",po=e.forwardRef(((t,n)=>{const{__scopeSelect:r,disabled:o=!1,...i}=t,a=io(r),s=so(fo,r),l=s.disabled||o,c=y(n,s.onTriggerChange),u=to(r),d=e.useRef("touch"),[f,p,h]=Qo((e=>{const t=u().filter((e=>!e.disabled)),n=t.find((e=>e.value===s.value)),r=ei(t,e,n);void 0!==r&&s.onValueChange(r.value)})),g=e=>{l||(s.onOpenChange(!0),h()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return m.jsx(In,{asChild:!0,...a,children:m.jsx(D.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Jo(s.value)?"":void 0,...i,ref:c,onClick:v(i.onClick,(e=>{e.currentTarget.focus(),"mouse"!==d.current&&g(e)})),onPointerDown:v(i.onPointerDown,(e=>{d.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())})),onKeyDown:v(i.onKeyDown,(e=>{const t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),t&&" "===e.key||Gr.includes(e.key)&&(g(),e.preventDefault())}))})})}));po.displayName=fo;var mo="SelectValue",vo=e.forwardRef(((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...s}=e,l=so(mo,n),{onValueNodeHasChildrenChange:c}=l,u=void 0!==i,d=y(t,l.onValueNodeChange);return V((()=>{c(u)}),[c,u]),m.jsx(D.span,{...s,ref:d,style:{pointerEvents:"none"},children:Jo(l.value)?m.jsx(m.Fragment,{children:a}):i})}));vo.displayName=mo;var ho=e.forwardRef(((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return m.jsx(D.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})}));ho.displayName="SelectIcon";var go=e=>m.jsx(K,{asChild:!0,...e});go.displayName="SelectPortal";var yo="SelectContent",wo=e.forwardRef(((t,r)=>{const o=so(yo,t.__scopeSelect),[i,a]=e.useState();if(V((()=>{a(new DocumentFragment)}),[]),!o.open){const e=i;return e?n.createPortal(m.jsx(bo,{scope:t.__scopeSelect,children:m.jsx(eo.Slot,{scope:t.__scopeSelect,children:m.jsx("div",{children:t.children})})}),e):null}return m.jsx(Co,{...t,ref:r})}));wo.displayName=yo;var xo=10,[bo,Eo]=ro(yo),So=b("SelectContent.RemoveScroll"),Co=e.forwardRef(((t,n)=>{const{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:l,sideOffset:c,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:h,sticky:g,hideWhenDetached:w,avoidCollisions:x,...b}=t,E=so(yo,r),[S,C]=e.useState(null),[R,T]=e.useState(null),P=y(n,(e=>C(e))),[D,N]=e.useState(null),[_,A]=e.useState(null),O=to(r),[j,L]=e.useState(!1),k=e.useRef(!1);e.useEffect((()=>{if(S)return cr(S)}),[S]),$n();const I=e.useCallback((e=>{const[t,...n]=O().map((e=>e.ref.current)),[r]=n.slice(-1),o=document.activeElement;for(const i of e){if(i===o)return;if(null==i||i.scrollIntoView({block:"nearest"}),i===t&&R&&(R.scrollTop=0),i===r&&R&&(R.scrollTop=R.scrollHeight),null==i||i.focus(),document.activeElement!==o)return}}),[O,R]),F=e.useCallback((()=>I([D,S])),[I,D,S]);e.useEffect((()=>{j&&F()}),[j,F]);const{onOpenChange:W,triggerPointerDownPosRef:H}=E;e.useEffect((()=>{if(S){let e={x:0,y:0};const t=t=>{var n,r;e={x:Math.abs(Math.round(t.pageX)-((null==(n=H.current)?void 0:n.x)??0)),y:Math.abs(Math.round(t.pageY)-((null==(r=H.current)?void 0:r.y)??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||W(!1),document.removeEventListener("pointermove",t),H.current=null};return null!==H.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[S,W,H]),e.useEffect((()=>{const e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[W]);const[B,V]=Qo((e=>{const t=O().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=ei(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),K=e.useCallback(((e,t,n)=>{const r=!k.current&&!n;(void 0!==E.value&&E.value===t||r)&&(N(e),r&&(k.current=!0))}),[E.value]),$=e.useCallback((()=>null==S?void 0:S.focus()),[S]),z=e.useCallback(((e,t,n)=>{const r=!k.current&&!n;(void 0!==E.value&&E.value===t||r)&&A(e)}),[E.value]),U="popper"===o?To:Ro,Y=U===To?{side:l,sideOffset:c,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:h,sticky:g,hideWhenDetached:w,avoidCollisions:x}:{};return m.jsx(bo,{scope:r,content:S,viewport:R,onViewportChange:T,itemRefCallback:K,selectedItem:D,onItemLeave:$,itemTextRefCallback:z,focusSelectedItem:F,selectedItemText:_,position:o,isPositioned:j,searchRef:B,children:m.jsx(Zr,{as:So,allowPinchZoom:!0,children:m.jsx(qn,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:v(i,(e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()})),children:m.jsx(M,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:m.jsx(U,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...Y,onPlaced:()=>L(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:v(b.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>I(t))),e.preventDefault()}}))})})})})})}));Co.displayName="SelectContentImpl";var Ro=e.forwardRef(((t,n)=>{const{__scopeSelect:r,onPlaced:o,...i}=t,a=so(yo,r),s=Eo(yo,r),[l,c]=e.useState(null),[u,d]=e.useState(null),f=y(n,(e=>d(e))),p=to(r),v=e.useRef(!1),h=e.useRef(!0),{viewport:g,selectedItem:w,selectedItemText:x,focusSelectedItem:b}=s,E=e.useCallback((()=>{if(a.trigger&&a.valueNode&&l&&u&&g&&w&&x){const e=a.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){const o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,c=Math.max(s,t.width),u=window.innerWidth-xo,d=Hn(i,[xo,Math.max(xo,u-c)]);l.style.minWidth=s+"px",l.style.left=d+"px"}else{const o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,c=Math.max(s,t.width),u=window.innerWidth-xo,d=Hn(i,[xo,Math.max(xo,u-c)]);l.style.minWidth=s+"px",l.style.right=d+"px"}const i=p(),s=window.innerHeight-2*xo,c=g.scrollHeight,d=window.getComputedStyle(u),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),h=parseInt(d.borderBottomWidth,10),y=f+m+c+parseInt(d.paddingBottom,10)+h,b=Math.min(5*w.offsetHeight,y),E=window.getComputedStyle(g),S=parseInt(E.paddingTop,10),C=parseInt(E.paddingBottom,10),R=e.top+e.height/2-xo,T=s-R,P=w.offsetHeight/2,D=f+m+(w.offsetTop+P),N=y-D;if(D<=R){const e=i.length>0&&w===i[i.length-1].ref.current;l.style.bottom="0px";const t=u.clientHeight-g.offsetTop-g.offsetHeight,n=D+Math.max(T,P+(e?C:0)+t+h);l.style.height=n+"px"}else{const e=i.length>0&&w===i[0].ref.current;l.style.top="0px";const t=Math.max(R,f+g.offsetTop+(e?S:0)+P)+N;l.style.height=t+"px",g.scrollTop=D-R+g.offsetTop}l.style.margin=`${xo}px 0`,l.style.minHeight=b+"px",l.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame((()=>v.current=!0))}}),[p,a.trigger,a.valueNode,l,u,g,w,x,a.dir,o]);V((()=>E()),[E]);const[S,C]=e.useState();V((()=>{u&&C(window.getComputedStyle(u).zIndex)}),[u]);const R=e.useCallback((e=>{e&&!0===h.current&&(E(),null==b||b(),h.current=!1)}),[E,b]);return m.jsx(Po,{scope:r,contentWrapper:l,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:m.jsx("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:m.jsx(D.div,{...i,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})}));Ro.displayName="SelectItemAlignedPosition";var To=e.forwardRef(((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=xo,...i}=e,a=io(n);return m.jsx(Fn,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));To.displayName="SelectPopperPosition";var[Po,Do]=ro(yo,{}),No="SelectViewport",_o=e.forwardRef(((t,n)=>{const{__scopeSelect:r,nonce:o,...i}=t,a=Eo(No,r),s=Do(No,r),l=y(n,a.onViewportChange),c=e.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),m.jsx(eo.Slot,{scope:r,children:m.jsx(D.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:v(i.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){const e=Math.abs(c.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*xo,o=parseFloat(n.style.minHeight),i=parseFloat(n.style.height),a=Math.max(o,i);if(a<r){const o=a+e,i=Math.min(r,o),s=o-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop}))})})]})}));_o.displayName=No;var Ao="SelectGroup",[Oo,jo]=ro(Ao);e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=We();return m.jsx(Oo,{scope:n,id:o,children:m.jsx(D.div,{role:"group","aria-labelledby":o,...r,ref:t})})})).displayName=Ao;var Lo="SelectLabel",ko=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=jo(Lo,n);return m.jsx(D.div,{id:o.id,...r,ref:t})}));ko.displayName=Lo;var Mo="SelectItem",[Io,Fo]=ro(Mo),Wo=e.forwardRef(((t,n)=>{const{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...s}=t,l=so(Mo,r),c=Eo(Mo,r),u=l.value===o,[d,f]=e.useState(a??""),[p,h]=e.useState(!1),g=y(n,(e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,o,i)})),w=We(),x=e.useRef("touch"),b=()=>{i||(l.onValueChange(o),l.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(Io,{scope:r,value:o,disabled:i,textId:w,isSelected:u,onItemTextChange:e.useCallback((e=>{f((t=>t||((null==e?void 0:e.textContent)??"").trim()))}),[]),children:m.jsx(eo.ItemSlot,{scope:r,value:o,disabled:i,textValue:d,children:m.jsx(D.div,{role:"option","aria-labelledby":w,"data-highlighted":p?"":void 0,"aria-selected":u&&p,"data-state":u?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:g,onFocus:v(s.onFocus,(()=>h(!0))),onBlur:v(s.onBlur,(()=>h(!1))),onClick:v(s.onClick,(()=>{"mouse"!==x.current&&b()})),onPointerUp:v(s.onPointerUp,(()=>{"mouse"===x.current&&b()})),onPointerDown:v(s.onPointerDown,(e=>{x.current=e.pointerType})),onPointerMove:v(s.onPointerMove,(e=>{var t;x.current=e.pointerType,i?null==(t=c.onItemLeave)||t.call(c):"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:v(s.onPointerLeave,(e=>{var t;e.currentTarget===document.activeElement&&(null==(t=c.onItemLeave)||t.call(c))})),onKeyDown:v(s.onKeyDown,(e=>{var t;""!==(null==(t=c.searchRef)?void 0:t.current)&&" "===e.key||(Jr.includes(e.key)&&b()," "===e.key&&e.preventDefault())}))})})})}));Wo.displayName=Mo;var Ho="SelectItemText",Bo=e.forwardRef(((t,r)=>{const{__scopeSelect:o,className:i,style:a,...s}=t,l=so(Ho,o),c=Eo(Ho,o),u=Fo(Ho,o),d=co(Ho,o),[f,p]=e.useState(null),v=y(r,(e=>p(e)),u.onItemTextChange,(e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,u.value,u.disabled)})),h=null==f?void 0:f.textContent,g=e.useMemo((()=>m.jsx("option",{value:u.value,disabled:u.disabled,children:h},u.value)),[u.disabled,u.value,h]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return V((()=>(w(g),()=>x(g))),[w,x,g]),m.jsxs(m.Fragment,{children:[m.jsx(D.span,{id:u.textId,...s,ref:v}),u.isSelected&&l.valueNode&&!l.valueNodeHasChildren?n.createPortal(s.children,l.valueNode):null]})}));Bo.displayName=Ho;var Vo="SelectItemIndicator",Ko=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return Fo(Vo,n).isSelected?m.jsx(D.span,{"aria-hidden":!0,...r,ref:t}):null}));Ko.displayName=Vo;var $o="SelectScrollUpButton",zo=e.forwardRef(((t,n)=>{const r=Eo($o,t.__scopeSelect),o=Do($o,t.__scopeSelect),[i,a]=e.useState(!1),s=y(n,o.onScrollButtonChange);return V((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollTop>0;a(e)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),i?m.jsx(Xo,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));zo.displayName=$o;var Uo="SelectScrollDownButton",Yo=e.forwardRef(((t,n)=>{const r=Eo(Uo,t.__scopeSelect),o=Do(Uo,t.__scopeSelect),[i,a]=e.useState(!1),s=y(n,o.onScrollButtonChange);return V((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;a(n)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),i?m.jsx(Xo,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));Yo.displayName=Uo;var Xo=e.forwardRef(((t,n)=>{const{__scopeSelect:r,onAutoScroll:o,...i}=t,a=Eo("SelectScrollButton",r),s=e.useRef(null),l=to(r),c=e.useCallback((()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)}),[]);return e.useEffect((()=>()=>c()),[c]),V((()=>{var e;const t=l().find((e=>e.ref.current===document.activeElement));null==(e=null==t?void 0:t.ref.current)||e.scrollIntoView({block:"nearest"})}),[l]),m.jsx(D.div,{"aria-hidden":!0,...i,ref:n,style:{flexShrink:0,...i.style},onPointerDown:v(i.onPointerDown,(()=>{null===s.current&&(s.current=window.setInterval(o,50))})),onPointerMove:v(i.onPointerMove,(()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))})),onPointerLeave:v(i.onPointerLeave,(()=>{c()}))})})),qo=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return m.jsx(D.div,{"aria-hidden":!0,...r,ref:t})}));qo.displayName="SelectSeparator";var Zo="SelectArrow";e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=io(n),i=so(Zo,n),a=Eo(Zo,n);return i.open&&"popper"===a.position?m.jsx(Wn,{...o,...r,ref:t}):null})).displayName=Zo;var Go=e.forwardRef((({__scopeSelect:t,value:n,...r},o)=>{const i=e.useRef(null),a=y(o,i),s=nr(n);return e.useEffect((()=>{const e=i.current;if(!e)return;const t=window.HTMLSelectElement.prototype,r=Object.getOwnPropertyDescriptor(t,"value").set;if(s!==n&&r){const t=new Event("change",{bubbles:!0});r.call(e,n),e.dispatchEvent(t)}}),[s,n]),m.jsx(D.select,{...r,style:{...X,...r.style},ref:a,defaultValue:n})}));function Jo(e){return""===e||void 0===e}function Qo(t){const n=_(t),r=e.useRef(""),o=e.useRef(0),i=e.useCallback((e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),a=e.useCallback((()=>{r.current="",window.clearTimeout(o.current)}),[]);return e.useEffect((()=>()=>window.clearTimeout(o.current)),[]),[r,i,a]}function ei(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let i=(a=e,s=Math.max(o,0),a.map(((e,t)=>a[(s+t)%a.length])));var a,s;1===r.length&&(i=i.filter((e=>e!==n)));const l=i.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return l!==n?l:void 0}Go.displayName="SelectBubbleInput";var ti=uo,ni=po,ri=vo,oi=ho,ii=go,ai=wo,si=_o,li=ko,ci=Wo,ui=Bo,di=Ko,fi=zo,pi=Yo,mi=qo,vi="Dialog",[hi,gi]=w(vi),[yi,wi]=hi(vi),xi=t=>{const{__scopeDialog:n,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:s=!0}=t,l=e.useRef(null),c=e.useRef(null),[u,d]=Y({prop:o,defaultProp:i??!1,onChange:a,caller:vi});return m.jsx(yi,{scope:n,triggerRef:l,contentRef:c,contentId:We(),titleId:We(),descriptionId:We(),open:u,onOpenChange:d,onOpenToggle:e.useCallback((()=>d((e=>!e))),[d]),modal:s,children:r})};xi.displayName=vi;var bi="DialogTrigger";e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=wi(bi,n),i=y(t,o.triggerRef);return m.jsx(D.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Bi(o.open),...r,ref:i,onClick:v(e.onClick,o.onOpenToggle)})})).displayName=bi;var Ei="DialogPortal",[Si,Ci]=hi(Ei,{forceMount:void 0}),Ri=t=>{const{__scopeDialog:n,forceMount:r,children:o,container:i}=t,a=wi(Ei,n);return m.jsx(Si,{scope:n,forceMount:r,children:e.Children.map(o,(e=>m.jsx($,{present:r||a.open,children:m.jsx(K,{asChild:!0,container:i,children:e})})))})};Ri.displayName=Ei;var Ti="DialogOverlay",Pi=e.forwardRef(((e,t)=>{const n=Ci(Ti,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=wi(Ti,e.__scopeDialog);return i.modal?m.jsx($,{present:r||i.open,children:m.jsx(Ni,{...o,ref:t})}):null}));Pi.displayName=Ti;var Di=b("DialogOverlay.RemoveScroll"),Ni=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=wi(Ti,n);return m.jsx(Zr,{as:Di,allowPinchZoom:!0,shards:[o.contentRef],children:m.jsx(D.div,{"data-state":Bi(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})})),_i="DialogContent",Ai=e.forwardRef(((e,t)=>{const n=Ci(_i,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=wi(_i,e.__scopeDialog);return m.jsx($,{present:r||i.open,children:i.modal?m.jsx(Oi,{...o,ref:t}):m.jsx(ji,{...o,ref:t})})}));Ai.displayName=_i;var Oi=e.forwardRef(((t,n)=>{const r=wi(_i,t.__scopeDialog),o=e.useRef(null),i=y(n,r.contentRef,o);return e.useEffect((()=>{const e=o.current;if(e)return cr(e)}),[]),m.jsx(Li,{...t,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:v(t.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()})),onPointerDownOutside:v(t.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:v(t.onFocusOutside,(e=>e.preventDefault()))})})),ji=e.forwardRef(((t,n)=>{const r=wi(_i,t.__scopeDialog),o=e.useRef(!1),i=e.useRef(!1);return m.jsx(Li,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{var n,a;null==(n=t.onCloseAutoFocus)||n.call(t,e),e.defaultPrevented||(o.current||null==(a=r.triggerRef.current)||a.focus(),e.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:e=>{var n,a;null==(n=t.onInteractOutside)||n.call(t,e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(i.current=!0));const s=e.target;(null==(a=r.triggerRef.current)?void 0:a.contains(s))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})})),Li=e.forwardRef(((t,n)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=t,l=wi(_i,r),c=e.useRef(null),u=y(n,c);return $n(),m.jsxs(m.Fragment,{children:[m.jsx(qn,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:m.jsx(M,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":Bi(l.open),...s,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(zi,{titleId:l.titleId}),m.jsx(Ui,{contentRef:c,descriptionId:l.descriptionId})]})]})})),ki="DialogTitle",Mi=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=wi(ki,n);return m.jsx(D.h2,{id:o.titleId,...r,ref:t})}));Mi.displayName=ki;var Ii="DialogDescription",Fi=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=wi(Ii,n);return m.jsx(D.p,{id:o.descriptionId,...r,ref:t})}));Fi.displayName=Ii;var Wi="DialogClose",Hi=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=wi(Wi,n);return m.jsx(D.button,{type:"button",...r,ref:t,onClick:v(e.onClick,(()=>o.onOpenChange(!1)))})}));function Bi(e){return e?"open":"closed"}Hi.displayName=Wi;var Vi="DialogTitleWarning",[Ki,$i]=function(t,n){const r=e.createContext(n),o=t=>{const{children:n,...o}=t,i=e.useMemo((()=>o),Object.values(o));return m.jsx(r.Provider,{value:i,children:n})};return o.displayName=t+"Provider",[o,function(o){const i=e.useContext(r);if(i)return i;if(void 0!==n)return n;throw new Error(`\`${o}\` must be used within \`${t}\``)}]}(Vi,{contentName:_i,titleName:ki,docsSlug:"dialog"}),zi=({titleId:t})=>{const n=$i(Vi),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect((()=>{if(t){document.getElementById(t)}}),[r,t]),null},Ui=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${$i("DialogDescriptionWarning").contentName}}.`;return e.useEffect((()=>{var e;const r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");if(n&&r){document.getElementById(n)}}),[r,t,n]),null},Yi=xi,Xi=Ri,qi=Pi,Zi=Ai,Gi=Mi,Ji=Fi,Qi=Hi;export{Xi as $,ke as A,nr as B,Me as C,Le as D,gn as E,_ as F,V as G,P as H,oi as I,We as J,Vn as K,li as L,g as M,K as N,$n as O,_e as P,Zr as Q,Oe as R,E as S,je as T,b as U,Ae as V,qn as W,cr as X,N as Y,Mn as Z,qi as _,$ as a,Zi as a0,Qi as a1,Gi as a2,Ji as a3,Yi as a4,xn as b,w as c,M as d,Fn as e,R as f,Z as g,In as h,D as i,m as j,v as k,Wn as l,ni as m,fi as n,pi as o,ii as p,ai as q,si as r,ci as s,di as t,y as u,ui as v,mi as w,ti as x,ri as y,Y as z};
//# sourceMappingURL=ui-d9jfY017.js.map
