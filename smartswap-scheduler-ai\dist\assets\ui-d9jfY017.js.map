{"version": 3, "file": "ui-d9jfY017.js", "sources": ["../../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../../node_modules/react/jsx-runtime.js", "../../../node_modules/@radix-ui/primitive/dist/index.mjs", "../../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "../../../node_modules/@radix-ui/react-context/dist/index.mjs", "../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "../../../node_modules/@radix-ui/react-collection/dist/index.mjs", "../../../node_modules/@radix-ui/react-primitive/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "../../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "../../../node_modules/@radix-ui/react-portal/dist/index.mjs", "../../../node_modules/@radix-ui/react-presence/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "../../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "../../../node_modules/@radix-ui/react-id/dist/index.mjs", "../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../../node_modules/@radix-ui/react-arrow/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-size/dist/index.mjs", "../../../node_modules/@radix-ui/react-popper/dist/index.mjs", "../../../node_modules/@radix-ui/number/dist/index.mjs", "../../../node_modules/@radix-ui/react-direction/dist/index.mjs", "../../../node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "../../../node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "../../../node_modules/@radix-ui/react-use-previous/dist/index.mjs", "../../../node_modules/aria-hidden/dist/es2015/index.js", "../../../node_modules/tslib/tslib.es6.mjs", "../../../node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "../../../node_modules/use-callback-ref/dist/es2015/assignRef.js", "../../../node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "../../../node_modules/use-callback-ref/dist/es2015/useRef.js", "../../../node_modules/use-sidecar/dist/es2015/medium.js", "../../../node_modules/use-sidecar/dist/es2015/exports.js", "../../../node_modules/react-remove-scroll/dist/es2015/medium.js", "../../../node_modules/react-remove-scroll/dist/es2015/UI.js", "../../../node_modules/react-style-singleton/dist/es2015/singleton.js", "../../../node_modules/get-nonce/dist/es2015/index.js", "../../../node_modules/react-style-singleton/dist/es2015/component.js", "../../../node_modules/react-style-singleton/dist/es2015/hook.js", "../../../node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "../../../node_modules/react-remove-scroll-bar/dist/es2015/component.js", "../../../node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../../node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "../../../node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "../../../node_modules/react-remove-scroll/dist/es2015/sidecar.js", "../../../node_modules/react-remove-scroll/dist/es2015/Combination.js", "../../../node_modules/@radix-ui/react-select/dist/index.mjs", "../../../node_modules/@radix-ui/react-dialog/dist/index.mjs"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/compose-refs/src/compose-refs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ jsx(Fragment2, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nexport {\n  Slot as Root,\n  Slot,\n  Slottable,\n  createSlot,\n  createSlottable\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ jsx2(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(\n      null\n    );\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ jsx2(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx2(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React2.useRef(null);\n      const [element, setElement] = React2.useState(null);\n      const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = React2.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      React2.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ jsx2(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport {\n  createCollection,\n  createCollection2 as unstable_createCollection\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/portal.tsx\nimport * as React from \"react\";\nimport ReactDOM from \"react-dom\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PORTAL_NAME = \"Portal\";\nvar Portal = React.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? ReactDOM.createPortal(/* @__PURE__ */ jsx(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\nexport {\n  Portal,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/presence.tsx\nimport * as React2 from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\n\n// src/use-state-machine.tsx\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : React2.Children.only(children);\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? React2.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = React2.useState();\n  const stylesRef = React2.useRef(null);\n  const prevPresentRef = React2.useRef(present);\n  const prevAnimationNameRef = React2.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  React2.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: React2.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\nexport {\n  Presence,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport {\n  Root,\n  VISUALLY_HIDDEN_STYLES,\n  VisuallyHidden\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/toast.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport * as DismissableLayer from \"@radix-ui/react-dismissable-layer\";\nimport { Portal } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { VisuallyHidden } from \"@radix-ui/react-visually-hidden\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(\"Toast\");\nvar [createToastContext, createToastScope] = createContextScope(\"Toast\", [createCollectionScope]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props) => {\n  const {\n    __scopeToast,\n    label = \"Notification\",\n    duration = 5e3,\n    swipeDirection = \"right\",\n    swipeThreshold = 50,\n    children\n  } = props;\n  const [viewport, setViewport] = React.useState(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n  return /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n    ToastProviderProvider,\n    {\n      scope: __scopeToast,\n      label,\n      duration,\n      swipeDirection,\n      swipeThreshold,\n      toastCount,\n      viewport,\n      onViewportChange: setViewport,\n      onToastAdd: React.useCallback(() => setToastCount((prevCount) => prevCount + 1), []),\n      onToastRemove: React.useCallback(() => setToastCount((prevCount) => prevCount - 1), []),\n      isFocusedToastEscapeKeyDownRef,\n      isClosePausedRef,\n      children\n    }\n  ) });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\"F8\"];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = \"Notifications ({hotkey})\",\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef(null);\n    const headFocusProxyRef = React.useRef(null);\n    const tailFocusProxyRef = React.useRef(null);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    React.useEffect(() => {\n      const handleKeyDown = (event) => {\n        const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key) => event[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener(\"keydown\", handleKeyDown);\n      return () => document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [hotkey]);\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n        const handleFocusOutResume = (event) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n          if (isFocusMovingOutside) handleResume();\n        };\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n        wrapper.addEventListener(\"focusin\", handlePause);\n        wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n        wrapper.addEventListener(\"pointermove\", handlePause);\n        wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n        window.addEventListener(\"blur\", handlePause);\n        window.addEventListener(\"focus\", handleResume);\n        return () => {\n          wrapper.removeEventListener(\"focusin\", handlePause);\n          wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n          wrapper.removeEventListener(\"pointermove\", handlePause);\n          wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n          window.removeEventListener(\"blur\", handlePause);\n          window.removeEventListener(\"focus\", handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n      },\n      [getItems]\n    );\n    React.useEffect(() => {\n      const viewport = ref.current;\n      if (viewport) {\n        const handleKeyDown = (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === \"Tab\" && !isMetaKey;\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n        viewport.addEventListener(\"keydown\", handleKeyDown);\n        return () => viewport.removeEventListener(\"keydown\", handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n    return /* @__PURE__ */ jsxs(\n      DismissableLayer.Branch,\n      {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: { pointerEvents: hasToasts ? void 0 : \"none\" },\n        children: [\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: headFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"forwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(Primitive.ol, { tabIndex: -1, ...viewportProps, ref: composedRefs }) }),\n          hasToasts && /* @__PURE__ */ jsx(\n            FocusProxy,\n            {\n              ref: tailFocusProxyRef,\n              onFocusFromOutsideViewport: () => {\n                const tabbableCandidates = getSortedTabbableCandidates({\n                  tabbingDirection: \"backwards\"\n                });\n                focusFirst(tabbableCandidates);\n              }\n            }\n          )\n        ]\n      }\n    );\n  }\n);\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(\n      VisuallyHidden,\n      {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: { position: \"fixed\" },\n        onFocus: (event) => {\n          const prevFocusedElement = event.relatedTarget;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n      }\n    );\n  }\n);\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = React.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? true,\n      onChange: onOpenChange,\n      caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || open, children: /* @__PURE__ */ jsx(\n      ToastImpl,\n      {\n        open,\n        ...toastProps,\n        ref: forwardedRef,\n        onClose: () => setOpen(false),\n        onPause: useCallbackRef(props.onPause),\n        onResume: useCallbackRef(props.onResume),\n        onSwipeStart: composeEventHandlers(props.onSwipeStart, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n        }),\n        onSwipeMove: composeEventHandlers(props.onSwipeMove, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n        }),\n        onSwipeCancel: composeEventHandlers(props.onSwipeCancel, (event) => {\n          event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n        }),\n        onSwipeEnd: composeEventHandlers(props.onSwipeEnd, (event) => {\n          const { x, y } = event.detail.delta;\n          event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n          event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n          event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n          setOpen(false);\n        })\n      }\n    ) });\n  }\n);\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {\n  }\n});\nvar ToastImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = \"foreground\",\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const pointerStartRef = React.useRef(null);\n    const swipeDeltaRef = React.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n    const startTimer = React.useCallback(\n      (duration2) => {\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = (/* @__PURE__ */ new Date()).getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n      },\n      [handleClose]\n    );\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      announceTextContent && /* @__PURE__ */ jsx(\n        ToastAnnounce,\n        {\n          __scopeToast,\n          role: \"status\",\n          \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n          \"aria-atomic\": true,\n          children: announceTextContent\n        }\n      ),\n      /* @__PURE__ */ jsx(ToastInteractiveProvider, { scope: __scopeToast, onClose: handleClose, children: ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(Collection.ItemSlot, { scope: __scopeToast, children: /* @__PURE__ */ jsx(\n          DismissableLayer.Root,\n          {\n            asChild: true,\n            onEscapeKeyDown: composeEventHandlers(onEscapeKeyDown, () => {\n              if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n              context.isFocusedToastEscapeKeyDownRef.current = false;\n            }),\n            children: /* @__PURE__ */ jsx(\n              Primitive.li,\n              {\n                role: \"status\",\n                \"aria-live\": \"off\",\n                \"aria-atomic\": true,\n                tabIndex: 0,\n                \"data-state\": open ? \"open\" : \"closed\",\n                \"data-swipe-direction\": context.swipeDirection,\n                ...toastProps,\n                ref: composedRefs,\n                style: { userSelect: \"none\", touchAction: \"none\", ...props.style },\n                onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n                  if (event.key !== \"Escape\") return;\n                  onEscapeKeyDown?.(event.nativeEvent);\n                  if (!event.nativeEvent.defaultPrevented) {\n                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                    handleClose();\n                  }\n                }),\n                onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n                  if (event.button !== 0) return;\n                  pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                }),\n                onPointerMove: composeEventHandlers(props.onPointerMove, (event) => {\n                  if (!pointerStartRef.current) return;\n                  const x = event.clientX - pointerStartRef.current.x;\n                  const y = event.clientY - pointerStartRef.current.y;\n                  const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                  const isHorizontalSwipe = [\"left\", \"right\"].includes(context.swipeDirection);\n                  const clamp = [\"left\", \"up\"].includes(context.swipeDirection) ? Math.min : Math.max;\n                  const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                  const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                  const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                  const delta = { x: clampedX, y: clampedY };\n                  const eventDetail = { originalEvent: event, delta };\n                  if (hasSwipeMoveStarted) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                      discrete: false\n                    });\n                  } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                    swipeDeltaRef.current = delta;\n                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                      discrete: false\n                    });\n                    event.target.setPointerCapture(event.pointerId);\n                  } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                    pointerStartRef.current = null;\n                  }\n                }),\n                onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n                  const delta = swipeDeltaRef.current;\n                  const target = event.target;\n                  if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                  }\n                  swipeDeltaRef.current = null;\n                  pointerStartRef.current = null;\n                  if (delta) {\n                    const toast = event.currentTarget;\n                    const eventDetail = { originalEvent: event, delta };\n                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                        discrete: true\n                      });\n                    } else {\n                      handleAndDispatchCustomEvent(\n                        TOAST_SWIPE_CANCEL,\n                        onSwipeCancel,\n                        eventDetail,\n                        {\n                          discrete: true\n                        }\n                      );\n                    }\n                    toast.addEventListener(\"click\", (event2) => event2.preventDefault(), {\n                      once: true\n                    });\n                  }\n                })\n              }\n            )\n          }\n        ) }),\n        context.viewport\n      ) })\n    ] });\n  }\n);\nvar ToastAnnounce = (props) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n  useNextFrame(() => setRenderAnnounceText(true));\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1e3);\n    return () => window.clearTimeout(timer);\n  }, []);\n  return isAnnounced ? null : /* @__PURE__ */ jsx(Portal, { asChild: true, children: /* @__PURE__ */ jsx(VisuallyHidden, { ...announceProps, children: renderAnnounceText && /* @__PURE__ */ jsxs(Fragment, { children: [\n    context.label,\n    \" \",\n    children\n  ] }) }) });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...titleProps, ref: forwardedRef });\n  }\n);\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...descriptionProps, ref: forwardedRef });\n  }\n);\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { altText, asChild: true, children: /* @__PURE__ */ jsx(ToastClose, { ...actionProps, ref: forwardedRef }) });\n  }\n);\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ jsx(ToastAnnounceExclude, { asChild: true, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, interactiveContext.onClose)\n      }\n    ) });\n  }\n);\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = React.forwardRef((props, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"data-radix-toast-announce-exclude\": \"\",\n      \"data-radix-toast-announce-alt\": altText || void 0,\n      ...announceExcludeProps,\n      ref: forwardedRef\n    }\n  );\n});\nfunction getAnnounceTextContent(container) {\n  const textContent = [];\n  const childNodes = Array.from(container.childNodes);\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n      const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n  return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const currentTarget = detail.originalEvent.currentTarget;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === \"left\" || direction === \"right\") {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\nfunction useNextFrame(callback = () => {\n}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => raf2 = window.requestAnimationFrame(fn));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\nfunction isHTMLElement(node) {\n  return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction focusFirst(candidates) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\nexport {\n  Action,\n  Close,\n  Description,\n  Provider,\n  Root2 as Root,\n  Title,\n  Toast,\n  ToastAction,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n  Viewport,\n  createToastScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/id/src/id.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useReactId = React[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = React.useState(useReactId());\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\nexport {\n  useId\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "// src/arrow.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Arrow\";\nvar Arrow = React.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ jsx(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\nexport {\n  Arrow,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/popper.tsx\nimport * as React from \"react\";\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size\n} from \"@floating-ui/react-dom\";\nimport * as ArrowPrimitive from \"@radix-ui/react-arrow\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SIDE_OPTIONS = [\"top\", \"right\", \"bottom\", \"left\"];\nvar ALIGN_OPTIONS = [\"start\", \"center\", \"end\"];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState(null);\n  return /* @__PURE__ */ jsx(PopperProvider, { scope: __scopePopper, anchor, onAnchorChange: setAnchor, children });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    React.useEffect(() => {\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ jsx(Primitive.div, { ...anchorProps, ref: composedRefs });\n  }\n);\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = \"bottom\",\n      sideOffset = 0,\n      align = \"center\",\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = \"partial\",\n      hideWhenDetached = false,\n      updatePositionStrategy = \"optimized\",\n      onPlaced,\n      ...contentProps\n    } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [arrow, setArrow] = React.useState(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: \"fixed\",\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === \"always\"\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions && shift({\n          mainAxis: true,\n          crossAxis: false,\n          limiter: sticky === \"partial\" ? limitShift() : void 0,\n          ...detectOverflowOptions\n        }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n          }\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: \"referenceHidden\", ...detectOverflowOptions })\n      ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = React.useState();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n          // keep off the page when measuring\n          minWidth: \"max-content\",\n          zIndex: contentZIndex,\n          [\"--radix-popper-transform-origin\"]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y\n          ].join(\" \"),\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...middlewareData.hide?.referenceHidden && {\n            visibility: \"hidden\",\n            pointerEvents: \"none\"\n          }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ jsx(\n          PopperContentProvider,\n          {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                  ...contentProps.style,\n                  // if the PopperContent hasn't been placed yet (not all measurements done)\n                  // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                  animation: !isPositioned ? \"none\" : void 0\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nvar PopperArrow = React.forwardRef(function PopperArrow2(props, forwardedRef) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ jsx(\n      \"span\",\n      {\n        ref: contentContext.onArrowChange,\n        style: {\n          position: \"absolute\",\n          left: contentContext.arrowX,\n          top: contentContext.arrowY,\n          [baseSide]: 0,\n          transformOrigin: {\n            top: \"\",\n            right: \"0 0\",\n            bottom: \"center 0\",\n            left: \"100% 0\"\n          }[contentContext.placedSide],\n          transform: {\n            top: \"translateY(100%)\",\n            right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n            bottom: `rotate(180deg)`,\n            left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n          }[contentContext.placedSide],\n          visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ jsx(\n          ArrowPrimitive.Root,\n          {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n              ...arrowProps.style,\n              // ensures the element can be measured correctly (mostly for if SVG)\n              display: \"block\"\n            }\n          }\n        )\n      }\n    )\n  );\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n  return value !== null;\n}\nvar transformOrigin = (options) => ({\n  name: \"transformOrigin\",\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: \"0%\", center: \"50%\", end: \"100%\" }[placedAlign];\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n    let x = \"\";\n    let y = \"\";\n    if (placedSide === \"bottom\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === \"top\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === \"right\") {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === \"left\") {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  }\n});\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\nexport {\n  ALIGN_OPTIONS,\n  Anchor,\n  Arrow,\n  Content,\n  Popper,\n  PopperAnchor,\n  PopperArrow,\n  PopperContent,\n  Root2 as Root,\n  SIDE_OPTIONS,\n  createPopperScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// packages/react/focus-guards/src/focus-guards.tsx\nimport * as React from \"react\";\nvar count = 0;\nfunction FocusGuards(props) {\n  useFocusGuards();\n  return props.children;\n}\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n    document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n    count++;\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\nfunction createFocusGuard() {\n  const element = document.createElement(\"span\");\n  element.setAttribute(\"data-radix-focus-guard\", \"\");\n  element.tabIndex = 0;\n  element.style.outline = \"none\";\n  element.style.opacity = \"0\";\n  element.style.position = \"fixed\";\n  element.style.pointerEvents = \"none\";\n  return element;\n}\nvar Root = FocusGuards;\nexport {\n  FocusGuards,\n  Root,\n  useFocusGuards\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/focus-scope.tsx\nimport * as React from \"react\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { jsx } from \"react/jsx-runtime\";\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = React.forwardRef((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    }\n  }).current;\n  React.useEffect(() => {\n    if (trapped) {\n      let handleFocusIn2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const target = event.target;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleFocusOut2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget;\n        if (relatedTarget === null) return;\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleMutations2 = function(mutations) {\n        const focusedElement = document.activeElement;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      };\n      var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n      document.addEventListener(\"focusin\", handleFocusIn2);\n      document.addEventListener(\"focusout\", handleFocusOut2);\n      const mutationObserver = new MutationObserver(handleMutations2);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n      return () => {\n        document.removeEventListener(\"focusin\", handleFocusIn2);\n        document.removeEventListener(\"focusout\", handleFocusOut2);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n  const handleKeyDown = React.useCallback(\n    (event) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n      const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement;\n      if (isTabKey && focusedElement) {\n        const container2 = event.currentTarget;\n        const [first, last] = getTabbableEdges(container2);\n        const hasTabbableElementsInside = first && last;\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container2) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n  return /* @__PURE__ */ jsx(Primitive.div, { tabIndex: -1, ...scopeProps, ref: composedRefs, onKeyDown: handleKeyDown });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\nfunction getTabbableEdges(container) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last];\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction findVisible(elements, container) {\n  for (const element of elements) {\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\nfunction isHidden(node, { upTo }) {\n  if (getComputedStyle(node).visibility === \"hidden\") return true;\n  while (node) {\n    if (upTo !== void 0 && node === upTo) return false;\n    if (getComputedStyle(node).display === \"none\") return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nfunction isSelectableInput(element) {\n  return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    element.focus({ preventScroll: true });\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n  let stack = [];\n  return {\n    add(focusScope) {\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n    remove(focusScope) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    }\n  };\n}\nfunction arrayRemove(array, item) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\nfunction removeLinks(items) {\n  return items.filter((item) => item.tagName !== \"A\");\n}\nvar Root = FocusScope;\nexport {\n  FocusScope,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (target.parentNode.host || target.parentNode);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "\"use client\";\n\n// src/select.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { VISUALLY_HIDDEN_STYLES } from \"@radix-ui/react-visually-hidden\";\nimport { hideOthers } from \"aria-hidden\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar OPEN_KEYS = [\" \", \"Enter\", \"ArrowUp\", \"ArrowDown\"];\nvar SELECTION_KEYS = [\" \", \"Enter\"];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState(null);\n  const [valueNode, setValueNode] = React.useState(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n    caller: SELECT_NAME\n  });\n  const triggerPointerDownPosRef = React.useRef(null);\n  const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(/* @__PURE__ */ new Set());\n  const nativeSelectKey = Array.from(nativeOptionsSet).map((option) => option.props.value).join(\";\");\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsxs(\n    SelectProvider,\n    {\n      required,\n      scope: __scopeSelect,\n      trigger,\n      onTriggerChange: setTrigger,\n      valueNode,\n      onValueNodeChange: setValueNode,\n      valueNodeHasChildren,\n      onValueNodeHasChildrenChange: setValueNodeHasChildren,\n      contentId: useId(),\n      value,\n      onValueChange: setValue,\n      open,\n      onOpenChange: setOpen,\n      dir: direction,\n      triggerPointerDownPosRef,\n      disabled,\n      children: [\n        /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeSelect, children: /* @__PURE__ */ jsx(\n          SelectNativeOptionsProvider,\n          {\n            scope: props.__scopeSelect,\n            onNativeOptionAdd: React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, []),\n            onNativeOptionRemove: React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, []),\n            children\n          }\n        ) }),\n        isFormControl ? /* @__PURE__ */ jsxs(\n          SelectBubbleInput,\n          {\n            \"aria-hidden\": true,\n            required,\n            tabIndex: -1,\n            name,\n            autoComplete,\n            value,\n            onChange: (event) => setValue(event.target.value),\n            disabled,\n            form,\n            children: [\n              value === void 0 ? /* @__PURE__ */ jsx(\"option\", { value: \"\" }) : null,\n              Array.from(nativeOptionsSet)\n            ]\n          },\n          nativeSelectKey\n        ) : null\n      ]\n    }\n  ) });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== void 0) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n    const handleOpen = (pointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        resetTypeahead();\n      }\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY)\n        };\n      }\n    };\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        role: \"combobox\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open,\n        \"aria-required\": context.required,\n        \"aria-autocomplete\": \"none\",\n        dir: context.dir,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        disabled: isDisabled,\n        \"data-disabled\": isDisabled ? \"\" : void 0,\n        \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n        ...triggerProps,\n        ref: composedRefs,\n        onClick: composeEventHandlers(triggerProps.onClick, (event) => {\n          event.currentTarget.focus();\n          if (pointerTypeRef.current !== \"mouse\") {\n            handleOpen(event);\n          }\n        }),\n        onPointerDown: composeEventHandlers(triggerProps.onPointerDown, (event) => {\n          pointerTypeRef.current = event.pointerType;\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n          }\n          if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n            handleOpen(event);\n            event.preventDefault();\n          }\n        }),\n        onKeyDown: composeEventHandlers(triggerProps.onKeyDown, (event) => {\n          const isTypingAhead = searchRef.current !== \"\";\n          const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n          if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n          if (isTypingAhead && event.key === \" \") return;\n          if (OPEN_KEYS.includes(event.key)) {\n            handleOpen();\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...valueProps,\n        ref: composedRefs,\n        style: { pointerEvents: \"none\" },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ jsx(Fragment, { children: placeholder }) : children\n      }\n    );\n  }\n);\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.span, { \"aria-hidden\": true, ...iconProps, ref: forwardedRef, children: children || \"\\u25BC\" });\n  }\n);\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props) => {\n  return /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, ...props });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState();\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n      const frag = fragment;\n      return frag ? ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(SelectContentProvider, { scope: props.__scopeSelect, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeSelect, children: /* @__PURE__ */ jsx(\"div\", { children: props.children }) }) }),\n        frag\n      ) : null;\n    }\n    return /* @__PURE__ */ jsx(SelectContentImpl, { ...props, ref: forwardedRef });\n  }\n);\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = createSlot(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = \"item-aligned\",\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState(null);\n    const [viewport, setViewport] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState(null);\n    const [selectedItemText, setSelectedItemText] = React.useState(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n    useFocusGuards();\n    const focusFirst = React.useCallback(\n      (candidates) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: \"nearest\" });\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n        const handlePointerMove = (event) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n          };\n        };\n        const handlePointerUp = (event) => {\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            if (!content.contains(event.target)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener(\"pointermove\", handlePointerMove);\n          document.addEventListener(\"pointerup\", handlePointerUp, { capture: true, once: true });\n        }\n        return () => {\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          document.removeEventListener(\"pointerup\", handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener(\"blur\", close);\n      window.addEventListener(\"resize\", close);\n      return () => {\n        window.removeEventListener(\"blur\", close);\n        window.removeEventListener(\"resize\", close);\n      };\n    }, [onOpenChange]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        setTimeout(() => nextItem.ref.current.focus());\n      }\n    });\n    const itemRefCallback = React.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions\n    } : {};\n    return /* @__PURE__ */ jsx(\n      SelectContentProvider,\n      {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, children: /* @__PURE__ */ jsx(\n          FocusScope,\n          {\n            asChild: true,\n            trapped: context.open,\n            onMountAutoFocus: (event) => {\n              event.preventDefault();\n            },\n            onUnmountAutoFocus: composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            }),\n            children: /* @__PURE__ */ jsx(\n              DismissableLayer,\n              {\n                asChild: true,\n                disableOutsidePointerEvents: true,\n                onEscapeKeyDown,\n                onPointerDownOutside,\n                onFocusOutside: (event) => event.preventDefault(),\n                onDismiss: () => context.onOpenChange(false),\n                children: /* @__PURE__ */ jsx(\n                  SelectPosition,\n                  {\n                    role: \"listbox\",\n                    id: context.contentId,\n                    \"data-state\": context.open ? \"open\" : \"closed\",\n                    dir: context.dir,\n                    onContextMenu: (event) => event.preventDefault(),\n                    ...contentProps,\n                    ...popperContentProps,\n                    onPlaced: () => setIsPositioned(true),\n                    ref: composedRefs,\n                    style: {\n                      // flex layout so we can place the scroll buttons properly\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      // reset the outline by default as the content MAY get focused\n                      outline: \"none\",\n                      ...contentProps.style\n                    },\n                    onKeyDown: composeEventHandlers(contentProps.onKeyDown, (event) => {\n                      const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                      if (event.key === \"Tab\") event.preventDefault();\n                      if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                      if ([\"ArrowUp\", \"ArrowDown\", \"Home\", \"End\"].includes(event.key)) {\n                        const items = getItems().filter((item) => !item.disabled);\n                        let candidateNodes = items.map((item) => item.ref.current);\n                        if ([\"ArrowUp\", \"End\"].includes(event.key)) {\n                          candidateNodes = candidateNodes.slice().reverse();\n                        }\n                        if ([\"ArrowUp\", \"ArrowDown\"].includes(event.key)) {\n                          const currentElement = event.target;\n                          const currentIndex = candidateNodes.indexOf(currentElement);\n                          candidateNodes = candidateNodes.slice(currentIndex + 1);\n                        }\n                        setTimeout(() => focusFirst(candidateNodes));\n                        event.preventDefault();\n                      }\n                    })\n                  }\n                )\n              }\n            )\n          }\n        ) })\n      }\n    );\n  }\n);\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = React.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState(null);\n  const [content, setContent] = React.useState(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n      if (context.dir !== \"rtl\") {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.left = clampedLeft + \"px\";\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.right = clampedRight + \"px\";\n      }\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = \"0px\";\n        const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n          (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + \"px\";\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n        contentWrapper.style.top = \"0px\";\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n          (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + \"px\";\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + \"px\";\n      contentWrapper.style.maxHeight = availableHeight + \"px\";\n      onPlaced?.();\n      requestAnimationFrame(() => shouldExpandOnScrollRef.current = true);\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced\n  ]);\n  useLayoutEffect(() => position(), [position]);\n  const [contentZIndex, setContentZIndex] = React.useState();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  const handleScrollButtonChange = React.useCallback(\n    (node) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n  return /* @__PURE__ */ jsx(\n    SelectViewportProvider,\n    {\n      scope: __scopeSelect,\n      contentWrapper,\n      shouldExpandOnScrollRef,\n      onScrollButtonChange: handleScrollButtonChange,\n      children: /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          ref: setContentWrapper,\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"fixed\",\n            zIndex: contentZIndex\n          },\n          children: /* @__PURE__ */ jsx(\n            Primitive.div,\n            {\n              ...popperProps,\n              ref: composedRefs,\n              style: {\n                // When we get the height of the content, it includes borders. If we were to set\n                // the height without having `boxSizing: 'border-box'` it would be too big.\n                boxSizing: \"border-box\",\n                // We need to ensure the content doesn't get taller than the wrapper\n                maxHeight: \"100%\",\n                ...popperProps.style\n              }\n            }\n          )\n        }\n      )\n    }\n  );\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = \"start\",\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  return /* @__PURE__ */ jsx(\n    PopperPrimitive.Content,\n    {\n      ...popperScope,\n      ...popperProps,\n      ref: forwardedRef,\n      align,\n      collisionPadding,\n      style: {\n        // Ensure border-box for floating-ui calculations\n        boxSizing: \"border-box\",\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeSelect, children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-radix-select-viewport\": \"\",\n          role: \"presentation\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            // we use position: 'relative' here on the `viewport` so that when we call\n            // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n            // (independent of the scrollUpButton).\n            position: \"relative\",\n            flex: 1,\n            // Viewport should only be scrollable in the vertical direction.\n            // This won't work in vertical writing modes, so we'll need to\n            // revisit this if/when that is supported\n            // https://developer.chrome.com/blog/vertical-form-controls\n            overflow: \"hidden auto\",\n            ...viewportProps.style\n          },\n          onScroll: composeEventHandlers(viewportProps.onScroll, (event) => {\n            const viewport = event.currentTarget;\n            const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n            if (shouldExpandOnScrollRef?.current && contentWrapper) {\n              const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n              if (scrolledBy > 0) {\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                const cssHeight = parseFloat(contentWrapper.style.height);\n                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                if (prevHeight < availableHeight) {\n                  const nextHeight = prevHeight + scrolledBy;\n                  const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                  const heightDiff = nextHeight - clampedNextHeight;\n                  contentWrapper.style.height = clampedNextHeight + \"px\";\n                  if (contentWrapper.style.bottom === \"0px\") {\n                    viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                    contentWrapper.style.justifyContent = \"flex-end\";\n                  }\n                }\n              }\n            }\n            prevScrollTopRef.current = viewport.scrollTop;\n          })\n        }\n      ) })\n    ] });\n  }\n);\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return /* @__PURE__ */ jsx(SelectGroupContextProvider, { scope: __scopeSelect, id: groupId, children: /* @__PURE__ */ jsx(Primitive.div, { role: \"group\", \"aria-labelledby\": groupId, ...groupProps, ref: forwardedRef }) });\n  }\n);\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ jsx(Primitive.div, { id: groupContext.id, ...labelProps, ref: forwardedRef });\n  }\n);\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef(\"touch\");\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n    if (value === \"\") {\n      throw new Error(\n        \"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\"\n      );\n    }\n    return /* @__PURE__ */ jsx(\n      SelectItemContextProvider,\n      {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ jsx(\n          Collection.ItemSlot,\n          {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: composeEventHandlers(itemProps.onFocus, () => setIsFocused(true)),\n                onBlur: composeEventHandlers(itemProps.onBlur, () => setIsFocused(false)),\n                onClick: composeEventHandlers(itemProps.onClick, () => {\n                  if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: composeEventHandlers(itemProps.onPointerUp, () => {\n                  if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: composeEventHandlers(itemProps.onPointerDown, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: composeEventHandlers(itemProps.onPointerMove, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                  if (disabled) {\n                    contentContext.onItemLeave?.();\n                  } else if (pointerTypeRef.current === \"mouse\") {\n                    event.currentTarget.focus({ preventScroll: true });\n                  }\n                }),\n                onPointerLeave: composeEventHandlers(itemProps.onPointerLeave, (event) => {\n                  if (event.currentTarget === document.activeElement) {\n                    contentContext.onItemLeave?.();\n                  }\n                }),\n                onKeyDown: composeEventHandlers(itemProps.onKeyDown, (event) => {\n                  const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                  if (isTypingAhead && event.key === \" \") return;\n                  if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                  if (event.key === \" \") event.preventDefault();\n                })\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => /* @__PURE__ */ jsx(\"option\", { value: itemContext.value, disabled: itemContext.disabled, children: textContent }, itemContext.value),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(Primitive.span, { id: itemContext.textId, ...itemTextProps, ref: composedRefs }),\n      itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? ReactDOM.createPortal(itemTextProps.children, context.valueNode) : null\n    ] });\n  }\n);\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ jsx(Primitive.span, { \"aria-hidden\": true, ...itemIndicatorProps, ref: forwardedRef }) : null;\n  }\n);\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = React.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const canScrollUp2 = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollUp ? /* @__PURE__ */ jsx(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = React.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollDown ? /* @__PURE__ */ jsx(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n  const autoScrollTimerRef = React.useRef(null);\n  const getItems = useCollection(__scopeSelect);\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: \"nearest\" });\n  }, [getItems]);\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"aria-hidden\": true,\n      ...scrollIndicatorProps,\n      ref: forwardedRef,\n      style: { flexShrink: 0, ...scrollIndicatorProps.style },\n      onPointerDown: composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerMove: composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerLeave: composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })\n    }\n  );\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { \"aria-hidden\": true, ...separatorProps, ref: forwardedRef });\n  }\n);\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef }) : null;\n  }\n);\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = React.forwardRef(\n  ({ __scopeSelect, value, ...props }, forwardedRef) => {\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        \"value\"\n      );\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"change\", { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ jsx(\n      Primitive.select,\n      {\n        ...props,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style },\n        ref: composedRefs,\n        defaultValue: value\n      }\n    );\n  }\n);\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n  return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef(\"\");\n  const timerRef = React.useRef(0);\n  const handleTypeaheadSearch = React.useCallback(\n    (key) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n      (function updateSearch(value) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== \"\") timerRef.current = window.setTimeout(() => updateSearch(\"\"), 1e3);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = \"\";\n    window.clearTimeout(timerRef.current);\n  }, []);\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n  return [searchRef, handleTypeaheadSearch, resetTypeahead];\n}\nfunction findNextItem(items, search, currentItem) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find(\n    (item) => item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  Group,\n  Icon,\n  Item,\n  ItemIndicator,\n  ItemText,\n  Label,\n  Portal,\n  Root2 as Root,\n  ScrollDownButton,\n  ScrollUpButton,\n  Select,\n  SelectArrow,\n  SelectContent,\n  SelectGroup,\n  SelectIcon,\n  SelectItem,\n  SelectItemIndicator,\n  SelectItemText,\n  SelectLabel,\n  SelectPortal,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n  SelectViewport,\n  Separator,\n  Trigger,\n  Value,\n  Viewport,\n  createSelectScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dialog.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContext, createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { hideOthers } from \"aria-hidden\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true\n  } = props;\n  const triggerRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME\n  });\n  return /* @__PURE__ */ jsx(\n    DialogProvider,\n    {\n      scope: __scopeDialog,\n      triggerRef,\n      contentRef,\n      contentId: useId(),\n      titleId: useId(),\n      descriptionId: useId(),\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      modal,\n      children\n    }\n  );\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: composeEventHandlers(props.onClick, context.onOpenToggle)\n      }\n    );\n  }\n);\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar DialogPortal = (props) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeDialog, forceMount, children: React.Children.map(children, (child) => /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children: child }) })) });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(DialogOverlayImpl, { ...overlayProps, ref: forwardedRef }) }) : null;\n  }\n);\nDialogOverlay.displayName = OVERLAY_NAME;\nvar Slot = createSlot(\"DialogOverlay.RemoveScroll\");\nvar DialogOverlayImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, shards: [context.contentRef], children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-state\": getState(context.open),\n          ...overlayProps,\n          ref: forwardedRef,\n          style: { pointerEvents: \"auto\", ...overlayProps.style }\n        }\n      ) })\n    );\n  }\n);\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: context.modal ? /* @__PURE__ */ jsx(DialogContentModal, { ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(DialogContentNonModal, { ...contentProps, ref: forwardedRef }) });\n  }\n);\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      DialogContentImpl,\n      {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault()\n        )\n      }\n    );\n  }\n);\nvar DialogContentNonModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      DialogContentImpl,\n      {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event) => {\n          props.onCloseAutoFocus?.(event);\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            event.preventDefault();\n          }\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event) => {\n          props.onInteractOutside?.(event);\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === \"pointerdown\") {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n          const target = event.target;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n          if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }\n      }\n    );\n  }\n);\nvar DialogContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    useFocusGuards();\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        FocusScope,\n        {\n          asChild: true,\n          loop: true,\n          trapped: trapFocus,\n          onMountAutoFocus: onOpenAutoFocus,\n          onUnmountAutoFocus: onCloseAutoFocus,\n          children: /* @__PURE__ */ jsx(\n            DismissableLayer,\n            {\n              role: \"dialog\",\n              id: context.contentId,\n              \"aria-describedby\": context.descriptionId,\n              \"aria-labelledby\": context.titleId,\n              \"data-state\": getState(context.open),\n              ...contentProps,\n              ref: composedRefs,\n              onDismiss: () => context.onOpenChange(false)\n            }\n          )\n        }\n      ),\n      /* @__PURE__ */ jsxs(Fragment, { children: [\n        /* @__PURE__ */ jsx(TitleWarning, { titleId: context.titleId }),\n        /* @__PURE__ */ jsx(DescriptionWarning, { contentRef, descriptionId: context.descriptionId })\n      ] })\n    ] });\n  }\n);\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(Primitive.h2, { id: context.titleId, ...titleProps, ref: forwardedRef });\n  }\n);\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(Primitive.p, { id: context.descriptionId, ...descriptionProps, ref: forwardedRef });\n  }\n);\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: composeEventHandlers(props.onClick, () => context.onOpenChange(false))\n      }\n    );\n  }\n);\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n  return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n  return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n  return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\nexport {\n  Close,\n  Content,\n  Description,\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n  Overlay,\n  Portal,\n  Root,\n  Title,\n  Trigger,\n  WarningProvider,\n  createDialogScope\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["f", "require$$0", "k", "Symbol", "for", "l", "m", "Object", "prototype", "hasOwnProperty", "n", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCurrentOwner", "p", "key", "ref", "__self", "__source", "q", "c", "a", "g", "b", "d", "e", "h", "call", "defaultProps", "$$typeof", "type", "props", "_owner", "current", "reactJsxRuntime_production_min", "jsx", "jsxs", "jsxRuntimeModule", "exports", "composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "event", "defaultPrevented", "setRef", "value", "composeRefs", "refs", "node", "hasCleanup", "cleanups", "map", "cleanup", "i", "length", "useComposedRefs", "React.useCallback", "useCallback", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createScope", "scopeContexts", "defaultContext", "React.createContext", "scope", "contexts", "React.useMemo", "useMemo", "rootComponentName", "BaseContext", "createContext", "index", "Provider", "children", "context", "Context", "_a", "values", "displayName", "consumerName", "React.useContext", "useContext", "Error", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "createScope2", "useScope", "overrideScopes", "nextScopes", "reduce", "nextScopes2", "createSlot", "ownerName", "SlotClone", "Slot2", "React.forwardRef", "forwardedRef", "slotProps", "childrenA<PERSON>y", "React.Children", "toArray", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "Children", "count", "only", "React.isValidElement", "isValidElement", "React.cloneElement", "Slot", "createSlotClone", "childrenRef", "element", "getter", "getOwnPropertyDescriptor", "get", "<PERSON><PERSON><PERSON><PERSON>", "isReactWarning", "_b", "getElementRef", "props2", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "test", "args", "result", "filter", "Boolean", "join", "mergeProps", "React.Fragment", "cloneElement", "SLOTTABLE_IDENTIFIER", "createSlottable", "Slottable2", "Fragment2", "__radixId", "createCollection", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "itemMap", "Map", "CollectionProvider", "React", "useRef", "COLLECTION_SLOT_NAME", "CollectionSlotImpl", "CollectionSlot", "forwardRef", "composedRefs", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlotImpl", "CollectionItemSlot", "itemData", "useEffect", "set", "delete", "ItemSlot", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "sort", "indexOf", "Primitive", "primitive", "Node", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "window", "dispatchDiscreteCustomEvent", "target", "ReactDOM.flushSync", "dispatchEvent", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "React.useRef", "React.useEffect", "originalBodyPointerEvents", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "DismissableLayerContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "setNode", "React.useState", "ownerDocument", "globalThis", "document", "force", "useState", "node2", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "handlePointerDownOutside", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "handleAndDispatchPointerDownOutsideEvent2", "handleAndDispatchCustomEvent", "eventDetail", "discrete", "originalEvent", "pointerType", "removeEventListener", "addEventListener", "once", "timerId", "setTimeout", "clearTimeout", "onPointerDownCapture", "usePointerDownOutside", "isPointerDownOnBranch", "some", "branch", "contains", "focusOutside", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "handleAndDispatchCustomEvent$1", "onFocusCapture", "onBlurCapture", "useFocusOutside", "onEscapeKeyDownProp", "handleKeyDown", "capture", "useEscapeKeydown", "preventDefault", "body", "style", "pointerEvents", "add", "dispatchUpdate", "handleUpdate", "jsxRuntimeExports", "div", "DismissableLayerBranch", "CustomEvent", "handler", "detail", "bubbles", "cancelable", "Root", "Branch", "useLayoutEffect2", "React.useLayoutEffect", "Portal", "container", "containerProp", "portalProps", "mounted", "setMounted", "useLayoutEffect", "ReactDOM", "createPortal", "Presence", "present", "presence", "React2.useState", "stylesRef", "React2.useRef", "prevPresentRef", "prevAnimationNameRef", "initialState", "state", "send", "machine", "React.useReducer", "useReducer", "useStateMachine", "UNMOUNT", "ANIMATION_OUT", "unmountSuspended", "MOUNT", "ANIMATION_END", "unmounted", "React2.useEffect", "currentAnimationName", "getAnimationName", "styles", "wasPresent", "prevAnimationName", "display", "timeoutId", "ownerWindow", "defaultView", "handleAnimationEnd", "isCurrentAnimation", "includes", "animationName", "currentFillMode", "animationFillMode", "handleAnimationStart", "isPresent", "React2.useCallback", "getComputedStyle", "usePresence", "React2.Children", "React2.cloneElement", "useInsertionEffect", "trim", "toString", "useControllableState", "prop", "defaultProp", "onChange", "caller", "uncontrolledProp", "setUncontrolledProp", "onChangeRef", "setValue", "prevValueRef", "useUncontrolledState", "isControlled", "isControlledRef", "wasControlled", "nextValue", "value2", "isFunction", "VISUALLY_HIDDEN_STYLES", "freeze", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "VisuallyHidden", "span", "Collection", "useCollection", "createToastContext", "createToastScope", "ToastProviderProvider", "useToastProviderContext", "ToastProvider", "__scopeToast", "label", "duration", "swipeDirection", "swipe<PERSON><PERSON><PERSON><PERSON>", "viewport", "setViewport", "toastCount", "setToastCount", "isFocusedToastEscapeKeyDownRef", "isClosePausedRef", "onViewportChange", "onToastAdd", "prevCount", "onToastRemove", "VIEWPORT_NAME", "VIEWPORT_DEFAULT_HOTKEY", "VIEWPORT_PAUSE", "VIEWPORT_RESUME", "ToastViewport", "hotkey", "viewportProps", "getItems", "wrapperRef", "headFocusProxyRef", "tailFocusProxyRef", "hotkeyLabel", "replace", "hasToasts", "every", "code", "focus", "wrapper", "handlePause", "pauseEvent", "handleResume", "resumeEvent", "handleFocusOutResume", "relatedTarget", "handlePointerLeaveResume", "activeElement", "getSortedTabbableCandidates", "tabbingDirection", "tabbableCandidates", "toastItem", "toastNode", "toastTabbableCandidates", "getTabbableCandidates", "reverse", "flat", "isMetaKey", "altKey", "ctrl<PERSON>ey", "metaKey", "focusedElement", "isTabbingBackwards", "shift<PERSON>ey", "sortedCandidates", "findIndex", "candidate", "focusFirst", "_c", "DismissableLayer.Branch", "role", "tabIndex", "FocusProxy", "onFocusFromOutsideViewport", "ol", "FOCUS_PROXY_NAME", "proxyProps", "onFocus", "prevFocusedElement", "TOAST_NAME", "Toast", "forceMount", "open", "openProp", "defaultOpen", "onOpenChange", "toastProps", "<PERSON><PERSON><PERSON>", "ToastImpl", "onClose", "onPause", "onResume", "onSwipeStart", "currentTarget", "setAttribute", "onSwipeMove", "x", "y", "delta", "setProperty", "onSwipeCancel", "removeProperty", "onSwipeEnd", "ToastInteractiveProvider", "useToastInteractiveContext", "durationProp", "pointerStartRef", "swipeDeltaRef", "closeTimerStartTimeRef", "closeTimerRemainingTimeRef", "closeTimerRef", "handleClose", "startTimer", "duration2", "Infinity", "Date", "getTime", "elapsedTime", "announceTextContent", "getAnnounceTextContent", "Fragment", "ToastAnnounce", "ReactDOM.createPortal", "DismissableLayer.Root", "li", "userSelect", "touchAction", "onKeyDown", "nativeEvent", "onPointerDown", "button", "clientX", "clientY", "onPointerMove", "hasSwipeMoveStarted", "isHorizontalSwipe", "clamp", "Math", "min", "max", "clampedX", "clampedY", "moveStartBuffer", "isDeltaInDirection", "setPointerCapture", "pointerId", "abs", "onPointerUp", "hasPointerCapture", "releasePointerCapture", "toast", "event2", "announceProps", "renderAnnounceText", "setRenderAnnounceText", "isAnnounced", "set<PERSON>s<PERSON>nn<PERSON>", "fn", "raf1", "raf2", "requestAnimationFrame", "cancelAnimationFrame", "useNextFrame", "timer", "ToastTitle", "titleProps", "ToastDescription", "descriptionProps", "ToastAction", "altText", "actionProps", "ToastAnnounceExclude", "ToastClose", "CLOSE_NAME", "closeProps", "interactiveContext", "onClick", "announceExcludeProps", "textContent", "childNodes", "for<PERSON>ach", "isHTMLElement", "nodeType", "TEXT_NODE", "push", "ELEMENT_NODE", "isHidden", "ariaHidden", "hidden", "isExcluded", "dataset", "radixToastAnnounceExclude", "radixToastAnnounceAlt", "direction", "threshold", "deltaX", "deltaY", "isDeltaX", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "disabled", "FILTER_SKIP", "FILTER_ACCEPT", "nextNode", "currentNode", "candidates", "previouslyFocusedElement", "Viewport", "Root2", "Title", "Description", "Action", "Close", "useReactId", "useId", "deterministicId", "id", "setId", "reactId", "String", "sides", "round", "floor", "createCoords", "v", "oppositeSideMap", "left", "right", "bottom", "top", "oppositeAlignmentMap", "start", "end", "evaluate", "param", "getSide", "placement", "split", "getAlignment", "getOppositeAxis", "axis", "getAxisLength", "getSideAxis", "getAlignmentAxis", "getOppositeAlignmentPlacement", "alignment", "getOppositePlacement", "side", "getPaddingObject", "expandPaddingObject", "rectToClientRect", "rect", "computeCoordsFromPlacement", "_ref", "rtl", "reference", "floating", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "isVertical", "commonX", "commonY", "commonAlign", "coords", "async", "detectOverflow", "options", "_await$platform$isEle", "platform", "rects", "elements", "strategy", "boundary", "rootBoundary", "elementContext", "altBoundary", "paddingObject", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "getSideOffsets", "isAnySideFullyClipped", "hasW<PERSON>ow", "getNodeName", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "documentElement", "Element", "HTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "overflowX", "overflowY", "isTableElement", "isTop<PERSON><PERSON>er", "selector", "matches", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "containerType", "<PERSON><PERSON>ilter", "<PERSON><PERSON><PERSON><PERSON>", "contain", "CSS", "supports", "isLastTraversableNode", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "getParentNode", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "concat", "visualViewport", "parent", "getPrototypeOf", "getCssDimensions", "parseFloat", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "dom<PERSON>lement", "getBoundingClientRect", "Number", "isFinite", "noOffsets", "getVisualOffsets", "offsetLeft", "offsetTop", "includeScale", "isFixedStrategy", "clientRect", "scale", "visualOffsets", "isFixed", "floatingOffsetParent", "shouldAddVisualOffsets", "offsetWin", "currentWin", "currentIFrame", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "getWindowScrollBarX", "leftScroll", "getHTMLOffset", "scroll", "ignoreScrollbarX", "htmlRect", "getClientRectFromClippingAncestor", "clippingAncestor", "html", "clientWidth", "clientHeight", "visualViewportBased", "getViewportRect", "scrollWidth", "scrollHeight", "getDocumentRect", "getInnerBoundingClientRect", "hasFixedPositionAncestor", "stopNode", "getRectRelativeToOffsetParent", "isOffsetParentAnElement", "offsets", "setLeftRTLScrollbarOffset", "offsetRect", "htmlOffset", "isStaticPositioned", "getTrueOffsetParent", "polyfill", "rawOffsetParent", "svgOffsetParent", "getContainingBlock", "topLayer", "clippingAncestors", "cache", "cachedResult", "el", "currentContainingBlockComputedStyle", "elementIsFixed", "computedStyle", "currentNodeIsContaining", "ancestor", "getClippingElementAncestors", "this", "firstClippingAncestor", "clippingRect", "accRect", "getElementRects", "data", "getOffsetParentFn", "getDimensionsFn", "getDimensions", "floatingDimensions", "getClientRects", "isRTL", "rectsAreEqual", "autoUpdate", "update", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "IntersectionObserver", "animationFrame", "referenceEl", "ancestors", "passive", "cleanupIo", "onMove", "io", "root", "_io", "disconnect", "refresh", "skip", "elementRectForRootMargin", "rootMargin", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "_e", "observe", "<PERSON><PERSON><PERSON>", "frameId", "reobserveFrame", "resizeObserver", "firstEntry", "unobserve", "_resizeObserver", "prevRefRect", "frameLoop", "nextRefRect", "_resizeObserver2", "offset", "_middlewareData$offse", "_middlewareData$arrow", "middlewareData", "diffCoords", "mainAxisMulti", "crossAxisMulti", "rawValue", "mainAxis", "crossAxis", "convertValueToCoords", "arrow", "alignmentOffset", "shift", "checkMainAxis", "checkCrossAxis", "limiter", "detectOverflowOptions", "mainAxisCoord", "crossAxisCoord", "maxSide", "limitedCoords", "enabled", "flip", "_middlewareData$flip", "initialPlacement", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "flipAlignment", "initialSideAxis", "isBasePlacement", "oppositePlacement", "getExpandedPlacements", "hasFallbackAxisSideDirection", "isStart", "lr", "rl", "tb", "bt", "getSideList", "getOppositeAxisPlacements", "placements", "overflows", "overflowsData", "mainAlignmentSide", "getAlignmentSides", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "nextPlacement", "_overflowsData$", "ignoreCrossAxisOverflow", "hasInitialMainAxisOverflow", "reset", "resetPlacement", "_overflowsData$filter2", "currentSideAxis", "acc", "_state$middlewareData", "_state$middlewareData2", "apply", "isYAxis", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions", "hide", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "arrowDimensions", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "shouldAddOffset", "centerOffset", "limitShift", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "computePosition", "mergedOptions", "platformWithCache", "config", "middleware", "validMiddleware", "statefulPlacement", "resetCount", "nextX", "nextY", "computePosition$1", "deepEqual", "keys", "isArray", "getDPR", "devicePixelRatio", "roundByDPR", "dpr", "useLatestRef", "arrow$1", "arrow$2", "deps", "offset$1", "shift$1", "limitShift$1", "flip$1", "size$1", "hide$1", "Arrow", "arrowProps", "svg", "viewBox", "preserveAspectRatio", "points", "useSize", "setSize", "entry", "borderSizeEntry", "borderSize", "box", "POPPER_NAME", "createPopperContext", "createPopperScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "<PERSON><PERSON>", "__scope<PERSON>opper", "anchor", "setAnchor", "onAnchorChange", "ANCHOR_NAME", "PopperA<PERSON><PERSON>", "virtualRef", "anchorProps", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sideOffset", "align", "alignOffset", "arrowPadding", "avoidCollisions", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "updatePositionStrategy", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "setArrow", "arrowSize", "arrow<PERSON>idth", "arrowHeight", "desiredPlacement", "hasExplicitBoundaries", "isNotNull", "floatingStyles", "isPositioned", "externalReference", "externalFloating", "transform", "whileElementsMounted", "setData", "latestMiddleware", "setLatestMiddleware", "_reference", "_setReference", "_floating", "_setFloating", "setReference", "referenceRef", "setFloating", "floatingRef", "floatingEl", "dataRef", "hasWhileElementsMounted", "whileElementsMountedRef", "platformRef", "openRef", "then", "fullData", "isMountedRef", "initialStyles", "useFloating", "anchorWidth", "anchorHeight", "contentStyle", "floatingUIarrow", "transform<PERSON><PERSON>in", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "arrowX", "arrowY", "cannotCenterArrow", "contentZIndex", "setContentZIndex", "zIndex", "min<PERSON><PERSON><PERSON>", "_d", "_f", "visibility", "dir", "onArrowChange", "shouldHideArrow", "animation", "ARROW_NAME", "OPPOSITE_SIDE", "PopperArrow", "contentContext", "baseSide", "ArrowPrimitive.Root", "isArrowHidden", "noArrowAlign", "arrowXCenter", "arrowYCenter", "<PERSON><PERSON>", "Content", "DirectionContext", "useDirection", "localDir", "globalDir", "useFocusGuards", "edgeGuards", "insertAdjacentElement", "createFocusGuard", "remove", "createElement", "outline", "opacity", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "FocusScope", "loop", "trapped", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "<PERSON><PERSON><PERSON><PERSON>", "lastFocusedElementRef", "focusScope", "paused", "pause", "resume", "handleFocusIn2", "select", "handleFocusOut2", "handleMutations2", "mutations", "mutation", "removedNodes", "mutationObserver", "MutationObserver", "childList", "subtree", "focusScopesStack", "mountEvent", "items", "item", "unmountEvent", "isTabKey", "container2", "first", "last", "findVisible", "getTabbableEdges", "upTo", "parentElement", "preventScroll", "HTMLInputElement", "isSelectableInput", "stack", "activeFocusScope", "arrayRemove", "unshift", "createFocusScopesStack", "array", "updatedArray", "splice", "usePrevious", "previous", "counterMap", "WeakMap", "uncontrolledNodes", "markerMap", "lockCount", "unwrapHost", "applyAttributeToOthers", "originalTarget", "markerName", "controlAttribute", "targets", "<PERSON><PERSON><PERSON><PERSON>", "correctTargets", "markerCounter", "hiddenNodes", "elementsToKeep", "elementsToStop", "keep", "has", "deep", "attr", "getAttribute", "alreadyHidden", "counterValue", "markerValue", "clear", "removeAttribute", "hideOthers", "activeParentNode", "getDefaultParent", "__assign", "assign", "t", "s", "arguments", "__rest", "getOwnPropertySymbols", "propertyIsEnumerable", "SuppressedError", "zeroRightClassName", "fullWidthClassName", "assignRef", "useIsomorphicLayoutEffect", "currentV<PERSON>ues", "useMergeRefs", "defaultValue", "initialValue", "newValue", "facade", "oldValue", "prevRefs_1", "nextRefs_1", "current_1", "ItoI", "SideCar", "sideCar", "rest", "Target", "read", "React.createElement", "isSideCarExport", "effectCar", "medium", "defaults", "buffer", "assigned", "useMedium", "assignSyncMedium", "cb", "cbs", "assignMedium", "pendingQueue", "executeQueue", "cycle", "Promise", "resolve", "innerCreateMedium", "ssr", "createSidecarMedium", "nothing", "RemoveScroll", "parentRef", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "callbacks", "setCallbacks", "forwardProps", "className", "removeScrollBar", "shards", "noRelative", "noIsolation", "inert", "allowPinchZoom", "as", "Container", "gapMode", "containerRef", "containerProps", "lockRef", "classNames", "fullWidth", "zeroRight", "makeStyleTag", "tag", "nonce", "__webpack_nonce__", "getNonce", "stylesheetSingleton", "counter", "stylesheet", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "getElementsByTagName", "insertStyleTag", "<PERSON><PERSON><PERSON><PERSON>", "styleSingleton", "sheet", "useStyle", "isDynamic", "dynamic", "zeroGap", "gap", "parse", "parseInt", "getGapWidth", "cs", "getOffset", "documentWidth", "windowWidth", "innerWidth", "Style", "lockAttribute", "getStyles", "allowRelative", "important", "getCurrentUseCounter", "RemoveScrollBar", "noImportant", "newCounter", "passiveSupported", "defineProperty", "err", "nonPassive", "elementCanBeScrolled", "alwaysContainsScroll", "locationCouldBeScrolled", "elementCouldBeScrolled", "getScrollVariables", "elementCouldBeVScrolled", "elementCouldBeHScrolled", "getHScrollVariables", "getTouchXY", "changedTouches", "getDeltaXY", "extractRef", "generateStyle", "idCounter", "lockStack", "getOutermostShadowParent", "shadowParent", "exported", "shouldPreventQueue", "touchStartRef", "activeAxis", "lastProps", "classList", "allow_1", "to", "pack", "ar", "__spread<PERSON><PERSON>y", "shouldCancelEvent", "touches", "currentAxis", "touch", "touchStart", "moveDirection", "canBeScrolledInMainDirection", "cancelingAxis", "end<PERSON>ar<PERSON>", "sourceDelta", "directionFactor", "getDirectionFactor", "targetInLock", "shouldCancelScroll", "isDeltaPositive", "availableScroll", "availableScrollTop", "elementScroll", "handleScroll", "shouldPrevent", "_event", "sourceEvent", "should", "shardNodes", "shouldCancel", "scrollTouchStart", "scrollWheel", "scrollTouchMove", "inst", "ReactRemoveScroll", "OPEN_KEYS", "SELECTION_KEYS", "SELECT_NAME", "createSelectContext", "createSelectScope", "usePopperScope", "SelectProvider", "useSelectContext", "SelectNativeOptionsProvider", "useSelectNativeOptionsContext", "Select", "__scopeSelect", "valueProp", "onValueChange", "autoComplete", "required", "form", "popperScope", "trigger", "setTrigger", "valueNode", "setValueNode", "valueNodeHasChildren", "setValueNodeHasChildren", "triggerPointerDownPosRef", "isFormControl", "closest", "nativeOptionsSet", "setNativeOptionsSet", "nativeSelectKey", "option", "PopperPrimitive.Root", "onTriggerChange", "onValueNodeChange", "onValueNodeHasChildrenChange", "contentId", "onNativeOptionAdd", "prev", "onNativeOptionRemove", "optionsSet", "SelectBubbleInput", "TRIGGER_NAME", "SelectTrigger", "triggerProps", "isDisabled", "pointerTypeRef", "searchRef", "handleTypeaheadSearch", "resetTypeahead", "useTypeaheadSearch", "search", "enabledItems", "currentItem", "nextItem", "findNextItem", "handleOpen", "pointerEvent", "pageX", "pageY", "PopperPrimitive.Anchor", "shouldShowPlaceholder", "isTypingAhead", "VALUE_NAME", "SelectValue", "placeholder", "valueProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SelectIcon", "iconProps", "SelectPortal", "PortalPrimitive", "SelectContent", "fragment", "setFragment", "DocumentFragment", "frag", "SelectContentProvider", "SelectContentImpl", "CONTENT_MARGIN", "useSelectContentContext", "onCloseAutoFocus", "selectedItem", "setSelectedItem", "selectedItemText", "setSelectedItemText", "setIsPositioned", "firstValidItemFoundRef", "firstItem", "restItems", "lastItem", "PREVIOUSLY_FOCUSED_ELEMENT", "scrollIntoView", "block", "focusSelectedItem", "pointerMoveDel<PERSON>", "handlePointerMove", "handlePointerUp", "close", "itemRefCallback", "isFirstValidItem", "handleItemLeave", "itemTextRefCallback", "SelectPosition", "SelectPopperPosition", "SelectItemAlignedPosition", "popperContentProps", "onItemLeave", "onContextMenu", "flexDirection", "isModifierKey", "candidateNodes", "currentElement", "currentIndex", "popperProps", "contentWrapper", "setContentWrapper", "shouldExpandOnScrollRef", "shouldRepositionRef", "triggerRect", "contentRect", "valueNodeRect", "itemTextRect", "itemTextOffset", "leftDelta", "minC<PERSON>nt<PERSON>id<PERSON>", "contentWidth", "rightEdge", "clampedLeft", "<PERSON><PERSON><PERSON><PERSON>", "leftEdge", "clampedRight", "innerHeight", "itemsHeight", "contentStyles", "contentBorderTopWidth", "borderTopWidth", "contentPaddingTop", "contentBorderBottomWidth", "borderBottomWidth", "fullContentHeight", "paddingBottom", "minContentHeight", "viewportStyles", "viewportPaddingTop", "viewportPaddingBottom", "topEdgeToTriggerMiddle", "triggerMiddleToBottomEdge", "selectedItemHalfHeight", "contentTopToItemMiddle", "itemMiddleToContentBottom", "isLastItem", "viewportOffsetBottom", "isFirstItem", "minHeight", "maxHeight", "handleScrollButtonChange", "SelectViewportProvider", "onScrollButtonChange", "boxSizing", "PopperPrimitive.Content", "useSelectViewportContext", "SelectViewport", "viewportContext", "prevScrollTopRef", "dangerouslySetInnerHTML", "__html", "flex", "onScroll", "scrolledBy", "cssMinHeight", "cssHeight", "prevHeight", "nextHeight", "clampedNextHeight", "heightDiff", "justifyContent", "GROUP_NAME", "SelectGroupContextProvider", "useSelectGroupContext", "groupProps", "groupId", "LABEL_NAME", "SelectLabel", "labelProps", "groupContext", "ITEM_NAME", "SelectItemContextProvider", "useSelectItemContext", "SelectItem", "textValue", "textValueProp", "itemProps", "isSelected", "setTextValue", "isFocused", "setIsFocused", "textId", "handleSelect", "onItemTextChange", "prevTextValue", "onBlur", "onPointerLeave", "ITEM_TEXT_NAME", "SelectItemText", "itemTextProps", "itemContext", "nativeOptionsContext", "itemTextNode", "setItemTextNode", "nativeOption", "ITEM_INDICATOR_NAME", "SelectItemIndicator", "itemIndicatorProps", "SCROLL_UP_BUTTON_NAME", "SelectScrollUpButton", "canScrollUp", "setCanScrollUp", "handleScroll2", "canScrollUp2", "SelectScrollButtonImpl", "onAutoScroll", "SCROLL_DOWN_BUTTON_NAME", "SelectScrollDownButton", "canScrollDown", "setCanScrollDown", "maxScroll", "canScrollDown2", "ceil", "scrollIndicatorProps", "autoScrollTimerRef", "clearAutoScrollTimer", "clearInterval", "activeItem", "flexShrink", "setInterval", "SelectSeparator", "separatorProps", "PopperPrimitive.Arrow", "prevValue", "selectProto", "HTMLSelectElement", "Event", "onSearchChange", "handleSearchChange", "timerRef", "updateSearch", "normalizedSearch", "char", "currentItemIndex", "wrappedItems", "startIndex", "_", "startsWith", "<PERSON><PERSON>", "Value", "Icon", "Content2", "Label", "<PERSON><PERSON>", "ItemText", "ItemIndicator", "ScrollUpButton", "ScrollDownButton", "Separator", "DIALOG_NAME", "createDialogContext", "createDialogScope", "Dialog<PERSON><PERSON>", "useDialogContext", "Dialog", "__scopeDialog", "modal", "triggerRef", "contentRef", "titleId", "descriptionId", "onOpenToggle", "prevOpen", "composedTriggerRef", "getState", "PORTAL_NAME", "PortalProvider", "usePortalContext", "DialogPortal", "OVERLAY_NAME", "DialogOverlay", "portalContext", "overlayProps", "DialogOverlayImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentModal", "DialogContentNonModal", "DialogContentImpl", "trapFocus", "ctrlLeftClick", "hasInteractedOutsideRef", "hasPointerDownOutsideRef", "onOpenAutoFocus", "TitleWarning", "DescriptionWarning", "TITLE_NAME", "DialogTitle", "h2", "DESCRIPTION_NAME", "DialogDescription", "DialogClose", "TITLE_WARNING_NAME", "WarningProvider", "useWarningContext", "contentName", "<PERSON><PERSON><PERSON>", "docs<PERSON>lug", "titleWarningContext", "MESSAGE", "getElementById", "describedById", "Overlay"], "mappings": "6FASiBA,EAAEC,EAAiBC,EAAEC,OAAOC,IAAI,iBAAiBC,EAAEF,OAAOC,IAAI,kBAAkBE,EAAEC,OAAOC,UAAUC,eAAeC,EAAEV,EAAEW,mDAAmDC,kBAAkBC,EAAE,CAACC,KAAI,EAAGC,KAAI,EAAGC,QAAO,EAAGC,UAAS,GAChP,SAASC,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAKC,EAAE,KAAiF,IAAIH,UAAvE,IAAAD,IAAIG,EAAE,GAAGH,QAAG,IAASD,EAAEN,MAAMU,EAAE,GAAGJ,EAAEN,UAAc,IAAAM,EAAEL,MAAMU,EAAEL,EAAEL,KAAcK,EAAEd,EAAEoB,KAAKN,EAAEE,KAAKT,EAAEJ,eAAea,KAAKC,EAAED,GAAGF,EAAEE,IAAI,GAAGH,GAAGA,EAAEQ,aAAiB,IAAAL,KAAKF,EAAED,EAAEQ,sBAAwBJ,EAAED,KAAKC,EAAED,GAAGF,EAAEE,IAAI,MAAM,CAACM,SAAS1B,EAAE2B,KAAKV,EAAEL,IAAIU,EAAET,IAAIU,EAAEK,MAAMP,EAAEQ,OAAOrB,EAAEsB,QAAQ,YAAkB3B,EAAa4B,EAAAC,IAAChB,EAAEe,EAAAE,KAAajB,ECPjWkB,EAAAC,QAAUpC,kBCFnB,SAASqC,EAAqBC,EAAsBC,GAAiBC,yBAAEA,GAA2B,GAAS,IAClG,OAAA,SAAqBC,GAE1B,GADuB,MAAAH,GAAAA,EAAAG,IACU,IAA7BD,IAAuCC,EAAMC,iBAC/C,OAAyB,MAAlBH,OAAkB,EAAAA,EAAAE,EAE5B,CACH,CCNA,SAASE,EAAO7B,EAAK8B,GACf,GAAe,mBAAR9B,EACT,OAAOA,EAAI8B,GACF9B,UACTA,EAAIiB,QAAUa,EAElB,CACA,SAASC,KAAeC,GACtB,OAAQC,IACN,IAAIC,GAAa,EACjB,MAAMC,EAAWH,EAAKI,KAAKpC,IACnB,MAAAqC,EAAUR,EAAO7B,EAAKiC,GAIrB,OAHFC,GAAgC,mBAAXG,IACXH,GAAA,GAERG,KAET,GAAIH,EACF,MAAO,KACL,IAAA,IAASI,EAAI,EAAGA,EAAIH,EAASI,OAAQD,IAAK,CAClC,MAAAD,EAAUF,EAASG,GACH,mBAAXD,EACAA,IAEFR,EAAAG,EAAKM,GAAI,KAE5B,GAIA,CACA,SAASE,KAAmBR,GAC1B,OAAOS,EAAiBC,YAACX,KAAeC,GAAOA,EACjD,CChBA,SAASW,EAAmBC,EAAWC,EAAyB,IAC9D,IAAIC,EAAkB,GAqBtB,MAAMC,EAAc,KAClB,MAAMC,EAAgBF,EAAgBV,KAAKa,GAClCC,EAAAA,cAAoBD,KAEtB,OAAA,SAAkBE,GACjB,MAAAC,SAAWD,WAAQP,KAAcI,EACvC,OAAOK,EAAaC,SAClB,KAAO,CAAE,CAAC,UAAUV,KAAc,IAAKO,EAAOP,CAACA,GAAYQ,MAC3D,CAACD,EAAOC,GAEX,GAGH,OADAL,EAAYH,UAAYA,EACjB,CAjCE,SAAeW,EAAmBN,GACnC,MAAAO,EAAcN,EAAmBO,cAACR,GAClCS,EAAQZ,EAAgBP,OACZO,EAAA,IAAIA,EAAiBG,GACjCU,MAAAA,EAAY5C,UAChB,MAAMoC,MAAEA,EAAAS,SAAOA,KAAaC,GAAY9C,EAClC+C,GAAU,OAAAC,EAAA,MAAAZ,OAAA,EAAAA,EAAQP,SAAR,EAAAmB,EAAqBL,KAAUF,EACzC1B,EAAQuB,EAAAA,SAAc,IAAMQ,GAASrE,OAAOwE,OAAOH,IACzD,SAAuB1C,IAAI2C,EAAQH,SAAU,CAAE7B,QAAO8B,cAUjD,OARPD,EAASM,YAAcV,EAAoB,WAQpC,CAACI,EAPC,SAAYO,EAAcf,SACjC,MAAMW,GAAU,OAAAC,EAAA,MAAAZ,OAAA,EAAAA,EAAQP,SAAR,EAAAmB,EAAqBL,KAAUF,EACzCK,EAAUM,EAAgBC,WAACN,GACjC,GAAID,EAAgB,OAAAA,EAChB,QAAmB,IAAnBZ,EAAkC,OAAAA,EACtC,MAAM,IAAIoB,MAAM,KAAKH,6BAAwCX,MACnE,EAEA,EAc0Be,EAAqBvB,KAAgBF,GAC/D,CACA,SAASyB,KAAwBC,GACzB,MAAAC,EAAYD,EAAO,GACrB,GAAkB,IAAlBA,EAAOhC,OAAqB,OAAAiC,EAChC,MAAMzB,EAAc,KAClB,MAAM0B,EAAaF,EAAOnC,KAAKsC,IAAkB,CAC/CC,SAAUD,IACV9B,UAAW8B,EAAa9B,cAEnB,OAAA,SAA2BgC,GAC1B,MAAAC,EAAaJ,EAAWK,QAAO,CAACC,GAAeJ,WAAU/B,gBAGtD,IAAKmC,KAFOJ,EAASC,GACI,UAAUhC,QAEzC,IACH,OAAOS,WAAc,KAAO,CAAE,CAAC,UAAUmB,EAAU5B,aAAciC,KAAe,CAACA,GAClF,GAGI,OADP9B,EAAYH,UAAY4B,EAAU5B,UAC3BG,CACT,CCtEA,SAASiC,EAAWC,GACZ,MAAAC,IAA4CD,GAC5CE,EAAQC,EAAAA,YAAiB,CAACrE,EAAOsE,KACrC,MAAMzB,SAAEA,KAAa0B,GAAcvE,EAC7BwE,EAAgBC,EAAAA,SAAeC,QAAQ7B,GACvC8B,EAAYH,EAAcI,KAAKC,GACrC,GAAIF,EAAW,CACP,MAAAG,EAAaH,EAAU3E,MAAM6C,SAC7BkC,EAAcP,EAAcnD,KAAK2D,GACjCA,IAAUL,EACRF,EAAcQ,SAACC,MAAMJ,GAAc,EAAUL,EAAcQ,SAACE,KAAK,MAC9DC,EAAAA,eAAqBN,GAAcA,EAAW9E,MAAM6C,SAAW,KAE/DmC,IAGY5E,OAAAA,EAAAA,IAAI+D,EAAW,IAAKI,EAAWtF,IAAKqF,EAAczB,SAAUuC,EAAoBC,eAACP,GAAcQ,EAAAA,aAAmBR,OAAY,EAAQC,GAAe,MAClL,CAC2B3E,OAAAA,EAAAA,IAAI+D,EAAW,IAAKI,EAAWtF,IAAKqF,EAAczB,gBAGpE,OADDuB,EAAAlB,YAAc,GAAGgB,SAChBE,CACT,CACG,IAACmB,IAAkC,QAEtC,SAASC,EAAgBtB,GACvB,MAAMC,EAAYE,EAAAA,YAAiB,CAACrE,EAAOsE,KACzC,MAAMzB,SAAEA,KAAa0B,GAAcvE,EAC/BoF,GAAAA,EAAAA,eAAqBvC,GAAW,CAC5B,MAAA4C,EAkDZ,SAAuBC,WACrB,IAAIC,EAAS,OAAA3C,EAAOvE,OAAAmH,yBAAyBF,EAAQ1F,MAAO,aAAQ,EAAAgD,EAAA6C,IAChEC,EAAUH,GAAU,mBAAoBA,GAAUA,EAAOI,eAC7D,GAAID,EACF,OAAOJ,EAAQzG,IAIjB,GAFA0G,EAAS,OAAAK,EAAOvH,OAAAmH,yBAAyBF,EAAS,aAAQ,EAAAM,EAAAH,IAChDC,EAAAH,GAAU,mBAAoBA,GAAUA,EAAOI,eACrDD,EACF,OAAOJ,EAAQ1F,MAAMf,IAEhB,OAAAyG,EAAQ1F,MAAMf,KAAOyG,EAAQzG,GACtC,CA9D0BgH,CAAcpD,GAC5BqD,EAyBZ,SAAoB3B,EAAW4B,GACvB,MAAAC,EAAgB,IAAKD,GAC3B,IAAA,MAAWE,KAAYF,EAAY,CAC3B,MAAAG,EAAgB/B,EAAU8B,GAC1BE,EAAiBJ,EAAWE,GAChB,WAAWG,KAAKH,GAE5BC,GAAiBC,EACLH,EAAAC,GAAY,IAAII,KACtB,MAAAC,EAASH,KAAkBE,GAE1B,OADPH,KAAiBG,GACVC,GAEAJ,IACTF,EAAcC,GAAYC,GAEN,UAAbD,EACTD,EAAcC,GAAY,IAAKC,KAAkBC,GAC3B,cAAbF,IACKD,EAAAC,GAAY,CAACC,EAAeC,GAAgBI,OAAOC,SAASC,KAAK,KAErF,CACE,MAAO,IAAKtC,KAAc6B,EAC5B,CAhDqBU,CAAWvC,EAAW1B,EAAS7C,OAIvCsF,OAHHzC,EAAS9C,OAASgH,aACpBb,EAAOjH,IAAMqF,EAAetD,EAAYsD,EAAcmB,GAAeA,GAEhEH,EAAkB0B,aAACnE,EAAUqD,EAC1C,CACWzB,OAAAA,EAAcQ,SAACC,MAAMrC,GAAY,EAAI4B,WAAeU,KAAK,MAAQ,QAGnE,OADGhB,EAAAjB,YAAc,GAAGgB,cACpBC,CACT,CACA,IAAI8C,EAAuB5I,OAAO,mBAElC,SAAS6I,EAAgBhD,GACvB,MAAMiD,EAAa,EAAGtE,oBACOuE,EAAAA,SAAW,CAAEvE,aAInC,OAFIsE,EAAAjE,YAAc,GAAGgB,cAC5BiD,EAAWE,UAAYJ,EAChBE,CACT,CAEA,SAAStC,EAAYG,GACnB,OAAOI,EAAoBC,eAACL,IAAgC,mBAAfA,EAAMjF,MAAuB,cAAeiF,EAAMjF,MAAQiF,EAAMjF,KAAKsH,YAAcJ,CAClI,CCnDA,SAASK,EAAiBC,GACxB,MAAMC,EAAgBD,EAAO,sBACtBE,EAAyBC,GAAyB9F,EAAmB4F,IACrEG,EAAwBC,GAAwBH,EACrDD,EACA,CAAEK,cAAe,CAAE3H,QAAS,MAAQ4H,QAA6B,IAAAC,MAE7DC,EAAsBhI,IACpB,MAAAoC,MAAEA,EAAOS,SAAAA,GAAa7C,EACtBf,EAAMgJ,EAAMC,OAAO,MACnBJ,EAAUG,EAAMC,OAA2B,IAAAH,KAAO7H,QACjCE,OAAAA,EAAGA,IAACuH,EAAwB,CAAEvF,QAAO0F,UAASD,cAAe5I,EAAK4D,cAE3FmF,EAAmB9E,YAAcsE,EACjC,MAAMW,EAAuBZ,EAAO,iBAC9Ba,IAAgCD,GAChCE,EAAiBJ,EAAMK,YAC3B,CAACtI,EAAOsE,KACA,MAAAlC,MAAEA,EAAOS,SAAAA,GAAa7C,EAEtBuI,EAAe9G,EAAgB6C,EADrBsD,EAAqBO,EAAsB/F,GACAyF,eAC3D,SAAuBzH,IAAIgI,EAAoB,CAAEnJ,IAAKsJ,EAAc1F,gBAGxEwF,EAAenF,YAAciF,EAC7B,MAAMK,EAAiBjB,EAAO,qBACxBkB,EAAiB,6BACjBC,IAAoCF,GACpCG,EAAqBV,EAAMK,YAC/B,CAACtI,EAAOsE,KACN,MAAMlC,MAAEA,EAAAS,SAAOA,KAAa+F,GAAa5I,EACnCf,EAAMgJ,EAAMC,OAAO,MACnBK,EAAe9G,EAAgB6C,EAAcrF,GAC7C6D,EAAU8E,EAAqBY,EAAgBpG,GAKrD,OAJA6F,EAAMY,WAAU,KACd/F,EAAQgF,QAAQgB,IAAI7J,EAAK,CAAEA,SAAQ2J,IAC5B,KAAW9F,EAAQgF,QAAQiB,OAAO9J,aAEhByJ,EAAwB,CAAOD,CAACA,GAAiB,GAAMxJ,IAAKsJ,EAAc1F,gBAkBlG,OAfP8F,EAAmBzF,YAAcsF,EAe1B,CACL,CAAE5F,SAAUoF,EAAoBzC,KAAM8C,EAAgBW,SAAUL,GAflE,SAAuBvG,GACrB,MAAMU,EAAU8E,EAAqBL,EAAO,qBAAsBnF,GAW3D,OAVU6F,EAAMtG,aAAY,KAC3B,MAAAsH,EAAiBnG,EAAQ+E,cAAc3H,QACzC,IAAC+I,EAAgB,MAAO,GACtB,MAAAC,EAAeC,MAAMC,KAAKH,EAAeI,iBAAiB,IAAIZ,OAK7D,OAJOU,MAAMC,KAAKtG,EAAQgF,QAAQ7E,UACdqG,MACzB,CAAChK,EAAGE,IAAM0J,EAAaK,QAAQjK,EAAEL,IAAIiB,SAAWgJ,EAAaK,QAAQ/J,EAAEP,IAAIiB,aAG5E,CAAC4C,EAAQ+E,cAAe/E,EAAQgF,SAEvC,EAIIJ,EAEJ,CChEA,IAmBI8B,EAnBQ,CACV,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,MAEoBzF,QAAO,CAAC0F,EAAWvI,KACvC,MAAMqE,EAAOtB,EAAW,aAAa/C,KAC/BwI,EAAOrF,EAAAA,YAAiB,CAACrE,EAAOsE,KACpC,MAAMqF,QAAEA,KAAYC,GAAmB5J,EACjC6J,EAAOF,EAAUpE,EAAOrE,EAIPd,MAHD,oBAAX0J,SACTA,OAAOzL,OAAOC,IAAI,cAAe,GAEZ8B,EAAAA,IAAIyJ,EAAM,IAAKD,EAAgB3K,IAAKqF,OAG7D,OADAoF,EAAKxG,YAAc,aAAahC,IACzB,IAAKuI,EAAWvI,CAACA,GAAOwI,KAC9B,CAAE,GACL,SAASK,EAA4BC,EAAQpJ,GACvCoJ,GAAQC,EAAAA,WAAmB,IAAMD,EAAOE,cAActJ,IAC5D,CCrCA,SAASuJ,EAAeC,GAChB,MAAAC,EAAcC,EAAYpC,OAACkC,GAIjC,OAHAG,EAAAA,WAAgB,KACdF,EAAYnK,QAAUkK,KAEjB9H,EAAaC,SAAC,IAAM,IAAIkE,WAAqB,OAAZ,OAAYzD,EAAAqH,EAAAnK,4BAAauG,KAAO,GAC1E,CCEA,IAII+D,EAHAC,EAAiB,0BACjBC,EAAuB,sCACvBC,EAAgB,gCAEhBC,EAA0BzI,EAAAA,cAAoB,CAChD0I,WAA4BC,IAC5BC,2CAA4DD,IAC5DE,aAA8BF,MAE5BG,EAAmB5G,EAAgBiE,YACrC,CAACtI,EAAOsE,KACA,MAAA4G,4BACJA,GAA8B,EAAAC,gBAC9BA,EAAAC,qBACAA,EAAAC,eACAA,EAAAC,kBACAA,EAAAC,UACAA,KACGC,GACDxL,EACE8C,EAAUM,EAAgBC,WAACuH,IAC1B1J,EAAMuK,GAAWC,EAAAA,SAAe,MACjCC,GAAsB,MAANzK,OAAM,EAAAA,EAAAyK,iBAA6B,MAAZC,gBAAY,EAAAA,WAAAC,YAChDC,GAASJ,EAAcK,SAAC,IAC3BxD,EAAe9G,EAAgB6C,GAAe0H,GAAUP,EAAQO,KAChEnB,EAAS1B,MAAMC,KAAKtG,EAAQ+H,SAC3BoB,GAAgD,IAAInJ,EAAQiI,wCAAwCmB,OAAQ,GAC7GC,EAAoDtB,EAAOtB,QAAQ0C,GACnEtJ,EAAQzB,EAAO2J,EAAOtB,QAAQrI,IAAQ,EACtCkL,EAA8BtJ,EAAQiI,uCAAuCsB,KAAO,EACpFC,EAAyB3J,GAASwJ,EAClCI,EA4FV,SAA+BnB,EAAsBO,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WACzE,MAAAW,EAA2BrC,EAAeiB,GAC1CqB,EAA8BnC,EAAYpC,QAAC,GAC3CwE,EAAiBpC,EAAAA,QAAa,SAoC7B,OAlCPC,EAAAA,WAAgB,KACR,MAAAoC,EAAqB/L,IACzB,GAAIA,EAAMoJ,SAAWyC,EAA4BvM,QAAS,CACxD,IAAI0M,EAA4C,WAC9CC,EACEnC,EACA8B,EACAM,EACA,CAAEC,UAAU,GAEf,EAEK,MAAAD,EAAc,CAAEE,cAAepM,GACX,UAAtBA,EAAMqM,aACMtB,EAAAuB,oBAAoB,QAASR,EAAexM,SAC1DwM,EAAexM,QAAU0M,EACzBjB,EAAcwB,iBAAiB,QAAST,EAAexM,QAAS,CAAEkN,MAAM,KAE7BR,GAErD,MACsBjB,EAAAuB,oBAAoB,QAASR,EAAexM,SAE5DuM,EAA4BvM,SAAU,GAElCmN,EAAUvD,OAAOwD,YAAW,KAClB3B,EAAAwB,iBAAiB,cAAeR,KAC7C,GACH,MAAO,KACL7C,OAAOyD,aAAaF,GACN1B,EAAAuB,oBAAoB,cAAeP,GACnChB,EAAAuB,oBAAoB,QAASR,EAAexM,YAE3D,CAACyL,EAAea,IACZ,CAELgB,qBAAsB,IAAMf,EAA4BvM,SAAU,EAEtE,CAvI+BuN,EAAuB7M,IAChD,MAAMoJ,EAASpJ,EAAMoJ,OACf0D,EAAwB,IAAI5K,EAAQkI,UAAU2C,MAAMC,GAAWA,EAAOC,SAAS7D,KAChFsC,IAA0BoB,IACR,MAAAtC,GAAAA,EAAAxK,GACH,MAAA0K,GAAAA,EAAA1K,GACfA,EAAMC,kBAAkB,MAAA0K,GAAAA,OAC5BI,GACGmC,EAgIV,SAAyBzC,EAAgBM,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WAC7D,MAAAkC,EAAqB5D,EAAekB,GACpC2C,EAA4B1D,EAAYpC,QAAC,GAaxC,OAZPqC,EAAAA,WAAgB,KACR,MAAA0D,EAAerN,IACnB,GAAIA,EAAMoJ,SAAWgE,EAA0B9N,QAAS,CAEzBgO,EAAAvD,EAAeoD,EADxB,CAAEf,cAAepM,GACwC,CAC3EmM,UAAU,GAEpB,GAGI,OADcpB,EAAAwB,iBAAiB,UAAWc,GACnC,IAAMtC,EAAcuB,oBAAoB,UAAWe,KACzD,CAACtC,EAAeoC,IACZ,CACLI,eAAgB,IAAMH,EAA0B9N,SAAU,EAC1DkO,cAAe,IAAMJ,EAA0B9N,SAAU,EAE7D,CAnJyBmO,EAAiBzN,IACpC,MAAMoJ,EAASpJ,EAAMoJ,OACG,IAAIlH,EAAQkI,UAAU2C,MAAMC,GAAWA,EAAOC,SAAS7D,OAE9D,MAAAqB,GAAAA,EAAAzK,GACG,MAAA0K,GAAAA,EAAA1K,GACfA,EAAMC,kBAAkB,MAAA0K,GAAAA,OAC5BI,GAwCH,OC9FJ,SAA0B2C,EAAqB3C,GAAgB,MAAAC,gBAAA,EAAAA,WAAYC,WACnE,MAAAV,EAAkBhB,EAAemE,GACvC/D,EAAAA,WAAgB,KACR,MAAAgE,EAAiB3N,IACH,WAAdA,EAAM5B,KACRmM,EAAgBvK,IAIb,OADP+K,EAAcwB,iBAAiB,UAAWoB,EAAe,CAAEC,SAAS,IAC7D,IAAM7C,EAAcuB,oBAAoB,UAAWqB,EAAe,CAAEC,SAAS,MACnF,CAACrD,EAAiBQ,GACvB,CD4CI8C,EAAkB7N,IACO+B,IAAUG,EAAQ+H,OAAOwB,KAAO,IAErC,MAAAlB,GAAAA,EAAAvK,IACbA,EAAMC,kBAAoB0K,IAC7B3K,EAAM8N,iBACKnD,QAEZI,GACHpB,EAAAA,WAAgB,KACd,GAAKrJ,EAUL,OATIgK,IAC0D,IAAxDpI,EAAQiI,uCAAuCsB,OACrB7B,EAAAmB,EAAcgD,KAAKC,MAAMC,cACvClD,EAAAgD,KAAKC,MAAMC,cAAgB,QAEnC/L,EAAAiI,uCAAuC+D,IAAI5N,IAE7C4B,EAAA+H,OAAOiE,IAAI5N,GACH6N,IACT,KACD7D,GAAuF,IAAxDpI,EAAQiI,uCAAuCsB,OAClEV,EAAAgD,KAAKC,MAAMC,cAAgBrE,MAG5C,CAACtJ,EAAMyK,EAAeT,EAA6BpI,IACtDyH,EAAAA,WAAgB,IACP,KACArJ,IACG4B,EAAA+H,OAAO9B,OAAO7H,GACd4B,EAAAiI,uCAAuChC,OAAO7H,GACtC6N,OAEjB,CAAC7N,EAAM4B,IACVyH,EAAAA,WAAgB,KACd,MAAMyE,EAAe,IAAMlD,EAAM,IAEjC,OADSD,SAAAsB,iBAAiB1C,EAAgBuE,GACnC,IAAMnD,SAASqB,oBAAoBzC,EAAgBuE,KACzD,IACuBC,EAAA7O,IACxBoJ,EAAU0F,IACV,IACK1D,EACHvM,IAAKsJ,EACLqG,MAAO,CACLC,cAAezC,EAA8BE,EAAyB,OAAS,YAAS,KACrFtM,EAAM4O,OAEXT,eAAgB3N,EAAqBR,EAAMmO,eAAgBL,EAAaK,gBACxEC,cAAe5N,EAAqBR,EAAMoO,cAAeN,EAAaM,eACtEZ,qBAAsBhN,EACpBR,EAAMwN,qBACNjB,EAAmBiB,2BAM7BvC,EAAiB/H,YA1GY,mBA2G7B,IACIiM,EAAyB9K,EAAgBiE,YAAC,CAACtI,EAAOsE,KAC9C,MAAAxB,EAAUM,EAAgBC,WAACuH,GAC3B3L,EAAMqL,EAAYpC,OAAC,MACnBK,EAAe9G,EAAgB6C,EAAcrF,GAU5BmB,OATvBmK,EAAAA,WAAgB,KACd,MAAMrJ,EAAOjC,EAAIiB,QACjB,GAAIgB,EAEF,OADQ4B,EAAAkI,SAAS8D,IAAI5N,GACd,KACG4B,EAAAkI,SAASjC,OAAO7H,MAG3B,CAAC4B,EAAQkI,WACW5K,EAAAA,IAAIoJ,EAAU0F,IAAK,IAAKlP,EAAOf,IAAKsJ,OAmE7D,SAASwG,IACD,MAAAnO,EAAQ,IAAIwO,YAAY3E,GAC9BoB,SAAS3B,cAActJ,EACzB,CACA,SAASiM,EAA6BtF,EAAM8H,EAASC,GAAQvC,SAAEA,IACvD,MAAA/C,EAASsF,EAAOtC,cAAchD,OAC9BpJ,EAAQ,IAAIwO,YAAY7H,EAAM,CAAEgI,SAAS,EAAOC,YAAY,EAAMF,WACpED,KAAgBlC,iBAAiB5F,EAAM8H,EAAS,CAAEjC,MAAM,IACxDL,EACFhD,EAA4BC,EAAQpJ,GAEpCoJ,EAAOE,cAActJ,EAEzB,CA9EAuO,EAAuBjM,YAhBL,yBA+FlB,IAAIuM,EAAOxE,EACPyE,EAASP,EEnNTQ,GAAmB,MAAA/D,gBAAA,EAAAA,WAAYC,UAAW+D,EAAAA,gBAAwB,OCOlEC,EAASxL,EAAgBiE,YAAC,CAACtI,EAAOsE,WACpC,MAAQwL,UAAWC,KAAkBC,GAAgBhQ,GAC9CiQ,EAASC,GAAcxE,EAAAA,UAAe,GAC7CyE,GAAgB,IAAMD,GAAW,IAAO,IACxC,MAAMJ,EAAYC,GAAiBE,IAAW,OAAAjN,EAAA,MAAA4I,gBAAA,EAAAA,WAAYC,eAAU,EAAA7I,EAAA2L,MACpE,OAAOmB,EAAYM,EAASC,aAA6BjQ,EAAGA,IAACoJ,EAAU0F,IAAK,IAAKc,EAAa/Q,IAAKqF,IAAiBwL,GAAa,QAEnID,EAAO3M,YARW,SCSf,IAACoN,EAAYtQ,IACR,MAAAuQ,QAAEA,EAAS1N,SAAAA,GAAa7C,EACxBwQ,EAOR,SAAqBD,GACnB,MAAOrP,EAAMuK,GAAWgF,aAClBC,EAAYC,EAAazI,OAAC,MAC1B0I,EAAiBD,EAAazI,OAACqI,GAC/BM,EAAuBF,EAAazI,OAAC,QACrC4I,EAAeP,EAAU,UAAY,aACpCQ,EAAOC,GAvBhB,SAAyBF,EAAcG,GACrC,OAAOC,EAAgBC,YAAC,CAACJ,EAAOnQ,IACZqQ,EAAQF,GAAOnQ,IACbmQ,GACnBD,EACL,CAkBwBM,CAAgBN,EAAc,CAClDb,QAAS,CACPoB,QAAS,YACTC,cAAe,oBAEjBC,iBAAkB,CAChBC,MAAO,UACPC,cAAe,aAEjBC,UAAW,CACTF,MAAO,aAmEJ,OAhEPG,EAAAA,WAAiB,KACT,MAAAC,EAAuBC,EAAiBnB,EAAUxQ,SACnC2Q,EAAA3Q,QAAoB,YAAV6Q,EAAsBa,EAAuB,SAC3E,CAACb,IACJZ,GAAgB,KACd,MAAM2B,EAASpB,EAAUxQ,QACnB6R,EAAanB,EAAe1Q,QAElC,GAD0B6R,IAAexB,EAClB,CACrB,MAAMyB,EAAoBnB,EAAqB3Q,QACzC0R,EAAuBC,EAAiBC,GAC9C,GAAIvB,EACFS,EAAK,cACI,GAAyB,SAAzBY,GAAuD,UAApB,MAAAE,OAAA,EAAAA,EAAQG,SACpDjB,EAAK,eACA,CAGHA,EADEe,GADgBC,IAAsBJ,EAEnC,gBAEA,UAEf,CACMhB,EAAe1Q,QAAUqQ,CAC/B,IACK,CAACA,EAASS,IACbb,GAAgB,KACd,GAAIjP,EAAM,CACJ,IAAAgR,EACE,MAAAC,EAAcjR,EAAKyK,cAAcyG,aAAetI,OAChDuI,EAAsBzR,IACpB,MACA0R,EADuBT,EAAiBnB,EAAUxQ,SACRqS,SAAS3R,EAAM4R,eAC3D,GAAA5R,EAAMoJ,SAAW9I,GAAQoR,IAC3BtB,EAAK,kBACAJ,EAAe1Q,SAAS,CACrB,MAAAuS,EAAkBvR,EAAK0N,MAAM8D,kBACnCxR,EAAK0N,MAAM8D,kBAAoB,WACnBR,EAAAC,EAAY7E,YAAW,KACI,aAAjCpM,EAAK0N,MAAM8D,oBACbxR,EAAK0N,MAAM8D,kBAAoBD,KAG/C,GAGYE,EAAwB/R,IACxBA,EAAMoJ,SAAW9I,IACE2P,EAAA3Q,QAAU2R,EAAiBnB,EAAUxQ,WAM9D,OAHKgB,EAAAiM,iBAAiB,iBAAkBwF,GACnCzR,EAAAiM,iBAAiB,kBAAmBkF,GACpCnR,EAAAiM,iBAAiB,eAAgBkF,GAC/B,KACLF,EAAY5E,aAAa2E,GACpBhR,EAAAgM,oBAAoB,iBAAkByF,GACtCzR,EAAAgM,oBAAoB,kBAAmBmF,GACvCnR,EAAAgM,oBAAoB,eAAgBmF,GAEjD,CACMrB,EAAK,mBAEN,CAAC9P,EAAM8P,IACH,CACL4B,UAAW,CAAC,UAAW,oBAAoBL,SAASxB,GACpD9R,IAAK4T,EAAAA,aAAoB7G,IACvB0E,EAAUxQ,QAAU8L,EAAQ8G,iBAAiB9G,GAAS,KACtDP,EAAQO,KACP,IAEP,CAjGmB+G,CAAYxC,GACvBvL,EAA4B,mBAAbnC,EAA0BA,EAAS,CAAE0N,QAASC,EAASoC,YAAeI,WAAgB7N,KAAKtC,GAC1G5D,EAAMwC,EAAgB+O,EAASvR,IAmGvC,SAAuByG,WACrB,IAAIC,EAAS,OAAA3C,EAAOvE,OAAAmH,yBAAyBF,EAAQ1F,MAAO,aAAQ,EAAAgD,EAAA6C,IAChEC,EAAUH,GAAU,mBAAoBA,GAAUA,EAAOI,eAC7D,GAAID,EACF,OAAOJ,EAAQzG,IAIjB,GAFA0G,EAAS,OAAAK,EAAOvH,OAAAmH,yBAAyBF,EAAS,aAAQ,EAAAM,EAAAH,IAChDC,EAAAH,GAAU,mBAAoBA,GAAUA,EAAOI,eACrDD,EACF,OAAOJ,EAAQ1F,MAAMf,IAEhB,OAAAyG,EAAQ1F,MAAMf,KAAOyG,EAAQzG,GACtC,CA/G4CgH,CAAcjB,IAEjD,MADgC,mBAAbnC,GACL2N,EAASoC,UAAYK,EAAAA,aAAoBjO,EAAO,CAAE/F,QAAS,MA8FlF,SAAS4S,EAAiBC,GACxB,aAAOA,WAAQU,gBAAiB,MAClC,CA9FAlC,EAASpN,YAAc,WCtBvB,IAAIgQ,EAAqBjL,EAAM,uBAAuBkL,OAAOC,aAAejD,EAC5E,SAASkD,GAAqBC,KAC5BA,EAAAC,YACAA,EAAAC,SACAA,EAAW,OACVC,OACDA,IAEA,MAAOC,EAAkBC,EAAqBC,GAmChD,UAA8BL,YAC5BA,EAAAC,SACAA,IAEA,MAAOzS,EAAO8S,GAAYnI,EAAAA,SAAe6H,GACnCO,EAAexJ,EAAYpC,OAACnH,GAC5B6S,EAActJ,EAAYpC,OAACsL,GAU1B,OATPN,GAAmB,KACjBU,EAAY1T,QAAUsT,IACrB,CAACA,IACJjJ,EAAAA,WAAgB,WACVuJ,EAAa5T,UAAYa,IAC3B,OAAAiC,EAAA4Q,EAAY1T,UAAU8C,EAAApD,KAAAgU,EAAA7S,GACtB+S,EAAa5T,QAAUa,KAExB,CAACA,EAAO+S,IACJ,CAAC/S,EAAO8S,EAAUD,EAC3B,CApD+DG,CAAqB,CAChFR,cACAC,aAEIQ,OAAwB,IAATV,EACfvS,EAAQiT,EAAeV,EAAOI,EAC1B,CACR,MAAMO,EAAkB3J,EAAAA,YAAsB,IAATgJ,GACrC/I,EAAAA,WAAgB,KACd,MAAM2J,EAAgBD,EAAgB/T,QACtC,GAAIgU,IAAkBF,EAAc,CAM1C,CACMC,EAAgB/T,QAAU8T,IACzB,CAACA,EAAcP,GACtB,CACE,MAAMI,EAAWnS,EAAiBC,aAC/BwS,UACC,GAAIH,EAAc,CAChB,MAAMI,EA8Bd,SAAoBrT,GAClB,MAAwB,mBAAVA,CAChB,CAhCuBsT,CAAWF,GAAaA,EAAUb,GAAQa,EACrDC,IAAWd,IACb,OAAAtQ,EAAA4Q,EAAY1T,UAAU8C,EAAApD,KAAAgU,EAAAQ,GAEhC,MACQT,EAAoBQ,KAGxB,CAACH,EAAcV,EAAMK,EAAqBC,IAErC,MAAA,CAAC7S,EAAO8S,EACjB,CCzCA,IAAIS,EAAyB7V,OAAO8V,OAAO,CAEzCC,SAAU,WACVC,OAAQ,EACRC,MAAO,EACPC,OAAQ,EACRC,QAAS,EACTC,QAAQ,EACRC,SAAU,SACVC,KAAM,mBACNC,WAAY,SACZC,SAAU,WAGRC,EAAiB7Q,EAAgBiE,YACnC,CAACtI,EAAOsE,IACoB2K,EAAA7O,IACxBoJ,EAAU2L,KACV,IACKnV,EACHf,IAAKqF,EACLsK,MAAO,IAAK0F,KAA2BtU,EAAM4O,WAKrDsG,EAAehS,YAbJ,iBAcR,IAACuM,EAAOyF,ECbP1N,EAAgB,iBACf4N,EAAYC,EAAe3N,IAAyBJ,EAAiB,UACrEgO,GAAoBC,IAAoB3T,EAAmB,QAAS,CAAC8F,MACrE8N,GAAuBC,IAA2BH,GAAmB9N,GACtEkO,GAAiB1V,IACb,MAAA2V,aACJA,EAAAC,MACAA,EAAQ,eAAAC,SACRA,EAAW,IAAAC,eACXA,EAAiB,QAAAC,eACjBA,EAAiB,GAAAlT,SACjBA,GACE7C,GACGgW,EAAUC,GAAevK,EAAAA,SAAe,OACxCwK,EAAYC,GAAiBzK,EAAAA,SAAe,GAC7C0K,EAAiC9L,EAAYpC,QAAC,GAC9CmO,EAAmB/L,EAAYpC,QAAC,GAMf9H,OALlBwV,EAAMzC,OAKY/S,EAAAA,IAAIgV,EAAWxS,SAAU,CAAER,MAAOuT,EAAc9S,SAA6BoM,EAAA7O,IAClGoV,GACA,CACEpT,MAAOuT,EACPC,QACAC,WACAC,iBACAC,iBACAG,aACAF,WACAM,iBAAkBL,EAClBM,WAAY7U,EAAAA,aAAkB,IAAMyU,GAAeK,GAAcA,EAAY,KAAI,IACjFC,cAAe/U,EAAAA,aAAkB,IAAMyU,GAAeK,GAAcA,EAAY,KAAI,IACpFJ,iCACAC,mBACAxT,gBAIN6S,GAAcxS,YAAcsE,EAC5B,IAAIkP,GAAgB,gBAChBC,GAA0B,CAAC,MAC3BC,GAAiB,sBACjBC,GAAkB,uBAClBC,GAAgBzS,EAAgBiE,YAClC,CAACtI,EAAOsE,KACA,MAAAqR,aACJA,EAAAoB,OACAA,EAASJ,GAAAf,MACTA,EAAQ,8BACLoB,GACDhX,EACE8C,EAAU2S,GAAwBiB,GAAef,GACjDsB,EAAW5B,EAAcM,GACzBuB,EAAa5M,EAAYpC,OAAC,MAC1BiP,EAAoB7M,EAAYpC,OAAC,MACjCkP,EAAoB9M,EAAYpC,OAAC,MACjCjJ,EAAMqL,EAAYpC,OAAC,MACnBK,EAAe9G,EAAgB6C,EAAcrF,EAAK6D,EAAQwT,kBAC1De,EAAcN,EAAOlQ,KAAK,KAAKyQ,QAAQ,OAAQ,IAAIA,QAAQ,SAAU,IACrEC,EAAYzU,EAAQoT,WAAa,EACvC3L,EAAAA,WAAgB,KACR,MAAAgE,EAAiB3N,UACqB,IAAlBmW,EAAOvV,QAAgBuV,EAAOS,OAAOxY,GAAQ4B,EAAM5B,IAAQ4B,EAAM6W,OAASzY,MAC7E,OAAIgE,EAAA/D,EAAAiB,UAAS8C,EAAA0U,UAGpC,OADS7L,SAAAsB,iBAAiB,UAAWoB,GAC9B,IAAM1C,SAASqB,oBAAoB,UAAWqB,KACpD,CAACwI,IACJxM,EAAAA,WAAgB,KACd,MAAMoN,EAAUT,EAAWhX,QACrB8V,EAAW/W,EAAIiB,QACjB,GAAAqX,GAAaI,GAAW3B,EAAU,CACpC,MAAM4B,EAAc,KACd,IAAC9U,EAAQuT,iBAAiBnW,QAAS,CAC/B,MAAA2X,EAAa,IAAIzI,YAAYwH,IACnCZ,EAAS9L,cAAc2N,GACvB/U,EAAQuT,iBAAiBnW,SAAU,CAC/C,GAEc4X,EAAe,KACf,GAAAhV,EAAQuT,iBAAiBnW,QAAS,CAC9B,MAAA6X,EAAc,IAAI3I,YAAYyH,IACpCb,EAAS9L,cAAc6N,GACvBjV,EAAQuT,iBAAiBnW,SAAU,CAC/C,GAEc8X,EAAwBpX,KACE+W,EAAQ9J,SAASjN,EAAMqX,gBACbH,KAEpCI,EAA2B,KACTP,EAAQ9J,SAAShC,SAASsM,gBACdL,KAQpC,OANQH,EAAAxK,iBAAiB,UAAWyK,GAC5BD,EAAAxK,iBAAiB,WAAY6K,GAC7BL,EAAAxK,iBAAiB,cAAeyK,GAChCD,EAAAxK,iBAAiB,eAAgB+K,GAClCpO,OAAAqD,iBAAiB,OAAQyK,GACzB9N,OAAAqD,iBAAiB,QAAS2K,GAC1B,KACGH,EAAAzK,oBAAoB,UAAW0K,GAC/BD,EAAAzK,oBAAoB,WAAY8K,GAChCL,EAAAzK,oBAAoB,cAAe0K,GACnCD,EAAAzK,oBAAoB,eAAgBgL,GACrCpO,OAAAoD,oBAAoB,OAAQ0K,GAC5B9N,OAAAoD,oBAAoB,QAAS4K,GAE9C,IACO,CAACP,EAAWzU,EAAQuT,mBACvB,MAAM+B,EAA8B1W,EAAiBC,aACnD,EAAG0W,uBACD,MACMC,EADarB,IACmB5V,KAAKkX,IACnC,MAAAC,EAAYD,EAAUtZ,IAAIiB,QAC1BuY,EAA0B,CAACD,KAAcE,GAAsBF,IACrE,MAA4B,aAArBH,EAAkCI,EAA0BA,EAAwBE,aAE7F,OAA6B,aAArBN,EAAkCC,EAAmBK,UAAYL,GAAoBM,SAE/F,CAAC3B,IA8BH,OA5BA1M,EAAAA,WAAgB,KACd,MAAMyL,EAAW/W,EAAIiB,QACrB,GAAI8V,EAAU,CACN,MAAAzH,EAAiB3N,cACrB,MAAMiY,EAAYjY,EAAMkY,QAAUlY,EAAMmY,SAAWnY,EAAMoY,QAEzD,GAD+B,QAAdpY,EAAM5B,MAAkB6Z,EAC3B,CACZ,MAAMI,EAAiBpN,SAASsM,cAC1Be,EAAqBtY,EAAMuY,SAEjC,GADyBvY,EAAMoJ,SAAWgM,GAClBkD,EAEtB,YADA,OAAAlW,EAAAmU,EAAkBjX,UAAS8C,EAAA0U,SAGvB,MACA0B,EAAmBhB,EAA4B,CAAEC,iBAD9Ba,EAAqB,YAAc,aAEtDvW,EAAQyW,EAAiBC,WAAWC,GAAcA,IAAcL,IAClEM,GAAWH,EAAiBlN,MAAMvJ,EAAQ,IAC5C/B,EAAM8N,iBAENwK,EAAqB,OAAAlT,IAAkB9F,UAAlB8F,EAA2B0R,QAAU,OAAA8B,EAAApC,EAAkBlX,UAASsZ,EAAA9B,OAEnG,GAGQ,OADS1B,EAAA7I,iBAAiB,UAAWoB,GAC9B,IAAMyH,EAAS9I,oBAAoB,UAAWqB,EAC7D,IACO,CAAC0I,EAAUmB,IACanJ,EAAA5O,KACzBoZ,EACA,CACExa,IAAKiY,EACLwC,KAAM,SACN,aAAc9D,EAAM0B,QAAQ,WAAYD,GACxCsC,UAAU,EACV/K,MAAO,CAAEC,cAAe0I,OAAY,EAAS,QAC7C1U,SAAU,CACR0U,GAAgCtI,EAAA7O,IAC9BwZ,GACA,CACE3a,IAAKkY,EACL0C,2BAA4B,KAI1BN,GAH2BnB,EAA4B,CACrDC,iBAAkB,iBAMVjY,EAAGA,IAACgV,EAAW7P,KAAM,CAAEnD,MAAOuT,EAAc9S,eAA8B2G,EAAUsQ,GAAI,CAAEH,UAAc,KAAG3C,EAAe/X,IAAKsJ,MAC/IgP,GAAgCtI,EAAA7O,IAC9BwZ,GACA,CACE3a,IAAKmY,EACLyC,2BAA4B,KAI1BN,GAH2BnB,EAA4B,CACrDC,iBAAkB,wBAWpCvB,GAAc5T,YAAcwT,GAC5B,IAAIqD,GAAmB,kBACnBH,GAAavV,EAAgBiE,YAC/B,CAACtI,EAAOsE,KACN,MAAMqR,aAAEA,EAAAkE,2BAAcA,KAA+BG,GAAeha,EAC9D8C,EAAU2S,GAAwBsE,GAAkBpE,GAC1D,OAA0B1G,EAAA7O,IACxB8U,EACA,CACE,eAAe,EACfyE,SAAU,KACPK,EACH/a,IAAKqF,EACLsK,MAAO,CAAE4F,SAAU,SACnByF,QAAUrZ,UACR,MAAMsZ,EAAqBtZ,EAAMqX,gBACG,OAAAjV,EAAQF,EAAAkT,mBAAUnI,SAASqM,KACHL,UAMtED,GAAW1W,YAAc6W,GACzB,IAAII,GAAa,QAKbC,GAAQ/V,EAAgBiE,YAC1B,CAACtI,EAAOsE,KACA,MAAA+V,WAAEA,EAAYC,KAAMC,EAAAC,YAAUA,eAAaC,KAAiBC,GAAe1a,GAC1Esa,EAAMK,GAAWtH,EAAqB,CAC3CC,KAAMiH,EACNhH,YAAaiH,IAAe,EAC5BhH,SAAUiH,EACVhH,OAAQ0G,KAEa/Z,OAAAA,EAAAA,IAAIkQ,EAAU,CAAEC,QAAS8J,GAAcC,EAAMzX,SAA6BoM,EAAA7O,IAC/Fwa,GACA,CACEN,UACGI,EACHzb,IAAKqF,EACLuW,QAAS,IAAMF,GAAQ,GACvBG,QAAS3Q,EAAenK,EAAM8a,SAC9BC,SAAU5Q,EAAenK,EAAM+a,UAC/BC,aAAcxa,EAAqBR,EAAMgb,cAAepa,IAChDA,EAAAqa,cAAcC,aAAa,aAAc,YAEjDC,YAAa3a,EAAqBR,EAAMmb,aAAcva,IACpD,MAAMwa,EAAEA,EAAAC,EAAGA,GAAMza,EAAM0O,OAAOgM,MACxB1a,EAAAqa,cAAcC,aAAa,aAAc,QAC/Cta,EAAMqa,cAAcrM,MAAM2M,YAAY,6BAA8B,GAAGH,OACvExa,EAAMqa,cAAcrM,MAAM2M,YAAY,6BAA8B,GAAGF,UAEzEG,cAAehb,EAAqBR,EAAMwb,eAAgB5a,IAClDA,EAAAqa,cAAcC,aAAa,aAAc,UACzCta,EAAAqa,cAAcrM,MAAM6M,eAAe,8BACnC7a,EAAAqa,cAAcrM,MAAM6M,eAAe,8BACnC7a,EAAAqa,cAAcrM,MAAM6M,eAAe,6BACnC7a,EAAAqa,cAAcrM,MAAM6M,eAAe,gCAE3CC,WAAYlb,EAAqBR,EAAM0b,YAAa9a,IAClD,MAAMwa,EAAEA,EAAAC,EAAGA,GAAMza,EAAM0O,OAAOgM,MACxB1a,EAAAqa,cAAcC,aAAa,aAAc,OACzCta,EAAAqa,cAAcrM,MAAM6M,eAAe,8BACnC7a,EAAAqa,cAAcrM,MAAM6M,eAAe,8BACzC7a,EAAMqa,cAAcrM,MAAM2M,YAAY,4BAA6B,GAAGH,OACtExa,EAAMqa,cAAcrM,MAAM2M,YAAY,4BAA6B,GAAGF,OACtEV,GAAQ,aAMlBP,GAAMlX,YAAciX,GACpB,IAAKwB,GAA0BC,IAA8BtG,GAAmB6E,GAAY,CAC1F,OAAAU,GACF,IAEID,GAAYvW,EAAgBiE,YAC9B,CAACtI,EAAOsE,KACA,MAAAqR,aACJA,EAAA5V,KACAA,EAAO,aACP8V,SAAUgG,EAAAvB,KACVA,EAAAO,QACAA,EAAA1P,gBACAA,EAAA2P,QACAA,EAAAC,SACAA,EAAAC,aACAA,EAAAG,YACAA,EAAAK,cACAA,EAAAE,WACAA,KACGhB,GACD1a,EACE8C,EAAU2S,GAAwB0E,GAAYxE,IAC7CzU,EAAMuK,GAAWC,EAAAA,SAAe,MACjCnD,EAAe9G,EAAgB6C,GAAe0H,GAAUP,EAAQO,KAChE8P,EAAkBxR,EAAYpC,OAAC,MAC/B6T,EAAgBzR,EAAYpC,OAAC,MAC7B2N,EAAWgG,GAAgB/Y,EAAQ+S,SACnCmG,EAAyB1R,EAAYpC,OAAC,GACtC+T,EAA6B3R,EAAYpC,OAAC2N,GAC1CqG,EAAgB5R,EAAYpC,OAAC,IAC7BqO,WAAEA,EAAYE,cAAAA,GAAkB3T,EAChCqZ,EAAchS,GAAe,YACJ,MAANjJ,OAAM,EAAAA,EAAA2M,SAAShC,SAASsM,kBAC3B,OAAQnV,EAAAF,EAAAkT,WAAUhT,EAAA0U,SAC7BmD,OAELuB,EAAa1a,EAAiBC,aACjC0a,IACMA,GAAaA,IAAcC,MACzBxS,OAAAyD,aAAa2O,EAAchc,SAClC8b,EAAuB9b,SAA2B,IAAIqc,MAAQC,UAC9DN,EAAchc,QAAU4J,OAAOwD,WAAW6O,EAAaE,MAEzD,CAACF,IAEH5R,EAAAA,WAAgB,KACd,MAAMyL,EAAWlT,EAAQkT,SACzB,GAAIA,EAAU,CACZ,MAAM8B,EAAe,KACnBsE,EAAWH,EAA2B/b,SACtC,MAAA6a,GAAAA,KAEInD,EAAc,KAClB,MAAM6E,OAAmCF,MAAQC,UAAYR,EAAuB9b,QACzD+b,EAAA/b,QAAU+b,EAA2B/b,QAAUuc,EACnE3S,OAAAyD,aAAa2O,EAAchc,SAClC,MAAA4a,GAAAA,KAIF,OAFS9E,EAAA7I,iBAAiByJ,GAAgBgB,GACjC5B,EAAA7I,iBAAiB0J,GAAiBiB,GACpC,KACI9B,EAAA9I,oBAAoB0J,GAAgBgB,GACpC5B,EAAA9I,oBAAoB2J,GAAiBiB,GAExD,IACO,CAAChV,EAAQkT,SAAUH,EAAUiF,EAASC,EAAUqB,IACnD7R,EAAAA,WAAgB,KACV+P,IAASxX,EAAQuT,iBAAiBnW,WAAoB2V,KACzD,CAACyE,EAAMzE,EAAU/S,EAAQuT,iBAAkB+F,IAC9C7R,EAAAA,WAAgB,KACFgM,IACL,IAAME,MACZ,CAACF,EAAYE,IACV,MAAAiG,EAAsBpa,EAAAA,SAAc,IACjCpB,EAAOyb,GAAuBzb,GAAQ,MAC5C,CAACA,IACA,OAAC4B,EAAQkT,SACc/G,EAAA5O,KAACuc,WAAU,CAAE/Z,SAAU,CAChD6Z,GAA0CzN,EAAA7O,IACxCyc,GACA,CACElH,eACA+D,KAAM,SACN,YAAsB,eAAT3Z,EAAwB,YAAc,SACnD,eAAe,EACf8C,SAAU6Z,IAGEtc,EAAGA,IAACub,GAA0B,CAAEvZ,MAAOuT,EAAckF,QAASsB,EAAatZ,SAAUia,EAAqBzM,aACxGjQ,EAAGA,IAACgV,EAAWpM,SAAU,CAAE5G,MAAOuT,EAAc9S,SAA6BoM,EAAA7O,IAC3F2c,EACA,CACEpT,SAAS,EACTwB,gBAAiB3K,EAAqB2K,GAAiB,KAChDrI,EAAQsT,+BAA+BlW,SAAsBic,IAClErZ,EAAQsT,+BAA+BlW,SAAU,KAEnD2C,SAA6BoM,EAAA7O,IAC3BoJ,EAAUwT,GACV,CACEtD,KAAM,SACN,YAAa,MACb,eAAe,EACfC,SAAU,EACV,aAAcW,EAAO,OAAS,SAC9B,uBAAwBxX,EAAQgT,kBAC7B4E,EACHzb,IAAKsJ,EACLqG,MAAO,CAAEqO,WAAY,OAAQC,YAAa,UAAWld,EAAM4O,OAC3DuO,UAAW3c,EAAqBR,EAAMmd,WAAYvc,IAC9B,WAAdA,EAAM5B,MACV,MAAAmM,GAAAA,EAAkBvK,EAAMwc,aACnBxc,EAAMwc,YAAYvc,mBACrBiC,EAAQsT,+BAA+BlW,SAAU,EACpCic,SAGjBkB,cAAe7c,EAAqBR,EAAMqd,eAAgBzc,IACnC,IAAjBA,EAAM0c,SACVxB,EAAgB5b,QAAU,CAAEkb,EAAGxa,EAAM2c,QAASlC,EAAGza,EAAM4c,aAEzDC,cAAejd,EAAqBR,EAAMyd,eAAgB7c,IACpD,IAACkb,EAAgB5b,QAAS,OAC9B,MAAMkb,EAAIxa,EAAM2c,QAAUzB,EAAgB5b,QAAQkb,EAC5CC,EAAIza,EAAM4c,QAAU1B,EAAgB5b,QAAQmb,EAC5CqC,EAAsB9W,QAAQmV,EAAc7b,SAC5Cyd,EAAoB,CAAC,OAAQ,SAASpL,SAASzP,EAAQgT,gBACvD8H,EAAQ,CAAC,OAAQ,MAAMrL,SAASzP,EAAQgT,gBAAkB+H,KAAKC,IAAMD,KAAKE,IAC1EC,EAAWL,EAAoBC,EAAM,EAAGxC,GAAK,EAC7C6C,EAAYN,EAAkC,EAAdC,EAAM,EAAGvC,GACzC6C,EAAwC,UAAtBtd,EAAMqM,YAA0B,GAAK,EACvDqO,EAAQ,CAAEF,EAAG4C,EAAU3C,EAAG4C,GAC1BnR,EAAc,CAAEE,cAAepM,EAAO0a,SACxCoC,GACF3B,EAAc7b,QAAUob,EACKzO,GA1L1B,kBA0L4CsO,EAAarO,EAAa,CACvEC,UAAU,KAEHoR,GAAmB7C,EAAOxY,EAAQgT,eAAgBoI,IAC3DnC,EAAc7b,QAAUob,EACKzO,GAhMzB,mBAgM4CmO,EAAclO,EAAa,CACzEC,UAAU,IAENnM,EAAAoJ,OAAOoU,kBAAkBxd,EAAMyd,aAC5BR,KAAKS,IAAIlD,GAAK8C,GAAmBL,KAAKS,IAAIjD,GAAK6C,KACxDpC,EAAgB5b,QAAU,SAG9Bqe,YAAa/d,EAAqBR,EAAMue,aAAc3d,IACpD,MAAM0a,EAAQS,EAAc7b,QACtB8J,EAASpJ,EAAMoJ,OAMrB,GALIA,EAAOwU,kBAAkB5d,EAAMyd,YAC1BrU,EAAAyU,sBAAsB7d,EAAMyd,WAErCtC,EAAc7b,QAAU,KACxB4b,EAAgB5b,QAAU,KACtBob,EAAO,CACT,MAAMoD,EAAQ9d,EAAMqa,cACdnO,EAAc,CAAEE,cAAepM,EAAO0a,SACxC6C,GAAmB7C,EAAOxY,EAAQgT,eAAgBhT,EAAQiT,gBAC/BlJ,GAjN7B,iBAiN8C6O,EAAY5O,EAAa,CACrEC,UAAU,IAGZF,GAtNG,oBAwND2O,EACA1O,EACA,CACEC,UAAU,IAIhB2R,EAAMvR,iBAAiB,SAAUwR,GAAWA,EAAOjQ,kBAAkB,CACnEtB,MAAM,GAE5B,WAMQtK,EAAQkT,eA1GkB,QA+G9B6G,GAAiB7c,IACnB,MAAM2V,aAAEA,EAAA9S,SAAcA,KAAa+b,GAAkB5e,EAC/C8C,EAAU2S,GAAwB0E,GAAYxE,IAC7CkJ,EAAoBC,GAAyBpT,EAAAA,UAAe,IAC5DqT,EAAaC,GAAkBtT,EAAAA,UAAe,GAM9C,OAyGT,SAAsBtB,EAAW,QAEzB,MAAA6U,EAAK9U,EAAeC,GAC1B+F,GAAgB,KACd,IAAI+O,EAAO,EACPC,EAAO,EAEX,OADAD,EAAOpV,OAAOsV,uBAAsB,IAAMD,EAAOrV,OAAOsV,sBAAsBH,KACvE,KACLnV,OAAOuV,qBAAqBH,GAC5BpV,OAAOuV,qBAAqBF,MAE7B,CAACF,GACN,CA1HeK,EAAA,IAAMR,GAAsB,KACzCvU,EAAAA,WAAgB,KACd,MAAMgV,EAAQzV,OAAOwD,YAAW,IAAM0R,GAAe,IAAO,KACrD,MAAA,IAAMlV,OAAOyD,aAAagS,KAChC,IACIR,EAAc,KAA0B9P,EAAA7O,IAACyP,EAAQ,CAAElG,SAAS,EAAM9G,SAA0BzC,EAAAA,IAAI8U,EAAgB,IAAK0J,EAAe/b,SAAUgc,UAA2CjC,EAAAA,SAAU,CAAE/Z,SAAU,CACpNC,EAAQ8S,MACR,IACA/S,UAIA2c,GAAanb,EAAgBiE,YAC/B,CAACtI,EAAOsE,KACN,MAAMqR,aAAEA,KAAiB8J,GAAezf,EACjBI,OAAAA,EAAAA,IAAIoJ,EAAU0F,IAAK,IAAKuQ,EAAYxgB,IAAKqF,OAGpEkb,GAAWtc,YAPM,aAQjB,IACIwc,GAAmBrb,EAAgBiE,YACrC,CAACtI,EAAOsE,KACN,MAAMqR,aAAEA,KAAiBgK,GAAqB3f,EACvBI,OAAAA,EAAAA,IAAIoJ,EAAU0F,IAAK,IAAKyQ,EAAkB1gB,IAAKqF,OAG1Eob,GAAiBxc,YAPM,mBAQvB,IACI0c,GAAcvb,EAAgBiE,YAChC,CAACtI,EAAOsE,KACN,MAAMub,QAAEA,KAAYC,GAAgB9f,EAChC,OAAC6f,EAAQ1M,SAMU/S,IAAI2f,GAAsB,CAAEF,UAASlW,SAAS,EAAM9G,SAA0BzC,EAAGA,IAAC4f,GAAY,IAAKF,EAAa7gB,IAAKqF,MAFnI,QAKbsb,GAAY1c,YAbM,cAclB,IAAI+c,GAAa,aACbD,GAAa3b,EAAgBiE,YAC/B,CAACtI,EAAOsE,KACN,MAAMqR,aAAEA,KAAiBuK,GAAelgB,EAClCmgB,EAAqBvE,GAA2BqE,GAAYtK,GAClE,SAAuBvV,IAAI2f,GAAsB,CAAEpW,SAAS,EAAM9G,SAA6BoM,EAAA7O,IAC7FoJ,EAAU8T,OACV,CACEvd,KAAM,YACHmgB,EACHjhB,IAAKqF,EACL8b,QAAS5f,EAAqBR,EAAMogB,QAASD,EAAmBtF,gBAKxEmF,GAAW9c,YAAc+c,GACzB,IAAIF,GAAuB1b,EAAgBiE,YAAC,CAACtI,EAAOsE,KAClD,MAAMqR,aAAEA,EAAAkK,QAAcA,KAAYQ,GAAyBrgB,EAC3D,OAA0BiP,EAAA7O,IACxBoJ,EAAU0F,IACV,CACE,oCAAqC,GACrC,gCAAiC2Q,QAAW,KACzCQ,EACHphB,IAAKqF,OAIX,SAASqY,GAAuB7M,GAC9B,MAAMwQ,EAAc,GAiBb,OAhBYnX,MAAMC,KAAK0G,EAAUyQ,YAC7BC,SAAStf,IAEduf,GADAvf,EAAKwf,WAAaxf,EAAKyf,WAAazf,EAAKof,aAAaA,EAAYM,KAAK1f,EAAKof,aAiDpF,SAAuBpf,GACd,OAAAA,EAAKwf,WAAaxf,EAAK2f,YAChC,CAlDQJ,CAAcvf,GAAO,CACvB,MAAM4f,EAAW5f,EAAK6f,YAAc7f,EAAK8f,QAAiC,SAAvB9f,EAAK0N,MAAMqD,QACxDgP,EAAwD,KAA3C/f,EAAKggB,QAAQC,0BAChC,IAAKL,EACH,GAAIG,EAAY,CACR,MAAApB,EAAU3e,EAAKggB,QAAQE,sBACzBvB,GAAqBS,EAAAM,KAAKf,EACxC,MACUS,EAAYM,QAAQjE,GAAuBzb,GAGrD,KAESof,CACT,CACA,SAASzT,GAA6BtF,EAAM8H,EAASC,GAAQvC,SAAEA,IACvD,MAAAkO,EAAgB3L,EAAOtC,cAAciO,cACrCra,EAAQ,IAAIwO,YAAY7H,EAAM,CAAEgI,SAAS,EAAMC,YAAY,EAAMF,WACnED,KAAuBlC,iBAAiB5F,EAAM8H,EAAS,CAAEjC,MAAM,IAC/DL,EACFhD,EAA4BkR,EAAera,GAE3Cqa,EAAc/Q,cAActJ,EAEhC,CACA,IAAIud,GAAqB,CAAC7C,EAAO+F,EAAWC,EAAY,KACtD,MAAMC,EAAS1D,KAAKS,IAAIhD,EAAMF,GACxBoG,EAAS3D,KAAKS,IAAIhD,EAAMD,GACxBoG,EAAWF,EAASC,EACtB,MAAc,SAAdH,GAAsC,UAAdA,EACnBI,GAAYF,EAASD,GAEpBG,GAAYD,EAASF,GAmBjC,SAAS5I,GAAsB5I,GAC7B,MAAM4R,EAAQ,GACRC,EAAS9V,SAAS+V,iBAAiB9R,EAAW+R,WAAWC,aAAc,CAC3EC,WAAa7gB,IACX,MAAM8gB,EAAiC,UAAjB9gB,EAAK+gB,SAAqC,WAAd/gB,EAAKnB,KACvD,OAAImB,EAAKghB,UAAYhhB,EAAK8f,QAAUgB,EAAsBH,WAAWM,YAC9DjhB,EAAKyY,UAAY,EAAIkI,WAAWO,cAAgBP,WAAWM,eAGtE,KAAOR,EAAOU,YAAkBX,EAAAd,KAAKe,EAAOW,aACrC,OAAAZ,CACT,CACA,SAASnI,GAAWgJ,GAClB,MAAMC,EAA2B3W,SAASsM,cACnC,OAAAoK,EAAW5U,MAAM2L,GAClBA,IAAckJ,IAClBlJ,EAAU5B,QACH7L,SAASsM,gBAAkBqK,IAEtC,CACG,IAAC5f,GAAW8S,GACX+M,GAAW3L,GACX4L,GAAQtI,GACRuI,GAAQnD,GACRoD,GAAclD,GACdmD,GAASjD,GACTkD,GAAQ9C,GCpnBR+C,GAAa9a,EAAM,UAAUkL,OAAOC,mBAAsB,GAC1DlO,GAAQ,EACZ,SAAS8d,GAAMC,GACb,MAAOC,EAAIC,GAASzX,EAAcK,SAACgX,MAIR,OAH3B5S,GAAgB,KACQgT,GAAOC,GAAYA,GAAWC,OAAOne,UAC1D,CAAC+d,IACuBC,EAAK,SAASA,IAAO,EAClD,CCNA,MAAMI,GAAQ,CAAC,MAAO,QAAS,SAAU,QAGnCxF,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACXwF,GAAQ1F,KAAK0F,MACbC,GAAQ3F,KAAK2F,MACbC,GAAqBC,IAAA,CACzBtI,EAAGsI,EACHrI,EAAGqI,IAECC,GAAkB,CACtBC,KAAM,QACNC,MAAO,OACPC,OAAQ,MACRC,IAAK,UAEDC,GAAuB,CAC3BC,MAAO,MACPC,IAAK,SAEP,SAAStG,GAAMqG,EAAOljB,EAAOmjB,GAC3B,OAAOnG,GAAIkG,EAAOnG,GAAI/c,EAAOmjB,GAC/B,CACA,SAASC,GAASpjB,EAAOqjB,GACvB,MAAwB,mBAAVrjB,EAAuBA,EAAMqjB,GAASrjB,CACtD,CACA,SAASsjB,GAAQC,GACf,OAAOA,EAAUC,MAAM,KAAK,EAC9B,CACA,SAASC,GAAaF,GACpB,OAAOA,EAAUC,MAAM,KAAK,EAC9B,CACA,SAASE,GAAgBC,GAChB,MAAS,MAATA,EAAe,IAAM,GAC9B,CACA,SAASC,GAAcD,GACd,MAAS,MAATA,EAAe,SAAW,OACnC,CACA,SAASE,GAAYN,GACZ,MAAA,CAAC,MAAO,UAAU/R,SAAS8R,GAAQC,IAAc,IAAM,GAChE,CACA,SAASO,GAAiBP,GACjB,OAAAG,GAAgBG,GAAYN,GACrC,CAkBA,SAASQ,GAA8BR,GACrC,OAAOA,EAAUhN,QAAQ,cAA2ByN,GAAAf,GAAqBe,IAC3E,CA6BA,SAASC,GAAqBV,GAC5B,OAAOA,EAAUhN,QAAQ,0BAAkC2N,GAAAtB,GAAgBsB,IAC7E,CAUA,SAASC,GAAiBtQ,GACxB,MAA0B,iBAAZA,EAVhB,SAA6BA,GACpB,MAAA,CACLmP,IAAK,EACLF,MAAO,EACPC,OAAQ,EACRF,KAAM,KACHhP,EAEP,CAEuCuQ,CAAoBvQ,GAAW,CAClEmP,IAAKnP,EACLiP,MAAOjP,EACPkP,OAAQlP,EACRgP,KAAMhP,EAEV,CACA,SAASwQ,GAAiBC,GAClB,MAAAjK,EACJA,EAAAC,EACAA,EAAA3G,MACAA,EAAAC,OACAA,GACE0Q,EACG,MAAA,CACL3Q,QACAC,SACAoP,IAAK1I,EACLuI,KAAMxI,EACNyI,MAAOzI,EAAI1G,EACXoP,OAAQzI,EAAI1G,EACZyG,IACAC,IAEJ,CCpIA,SAASiK,GAA2BC,EAAMjB,EAAWkB,GAC/C,IAAAC,UACFA,EAAAC,SACAA,GACEH,EACE,MAAAI,EAAWf,GAAYN,GACvBsB,EAAgBf,GAAiBP,GACjCuB,EAAclB,GAAciB,GAC5BX,EAAOZ,GAAQC,GACfwB,EAA0B,MAAbH,EACbI,EAAUN,EAAUrK,EAAIqK,EAAU/Q,MAAQ,EAAIgR,EAAShR,MAAQ,EAC/DsR,EAAUP,EAAUpK,EAAIoK,EAAU9Q,OAAS,EAAI+Q,EAAS/Q,OAAS,EACjEsR,EAAcR,EAAUI,GAAe,EAAIH,EAASG,GAAe,EACrE,IAAAK,EACJ,OAAQjB,GACN,IAAK,MACMiB,EAAA,CACP9K,EAAG2K,EACH1K,EAAGoK,EAAUpK,EAAIqK,EAAS/Q,QAE5B,MACF,IAAK,SACMuR,EAAA,CACP9K,EAAG2K,EACH1K,EAAGoK,EAAUpK,EAAIoK,EAAU9Q,QAE7B,MACF,IAAK,QACMuR,EAAA,CACP9K,EAAGqK,EAAUrK,EAAIqK,EAAU/Q,MAC3B2G,EAAG2K,GAEL,MACF,IAAK,OACME,EAAA,CACP9K,EAAGqK,EAAUrK,EAAIsK,EAAShR,MAC1B2G,EAAG2K,GAEL,MACF,QACWE,EAAA,CACP9K,EAAGqK,EAAUrK,EACbC,EAAGoK,EAAUpK,GAGX,OAAAmJ,GAAaF,IACnB,IAAK,QACH4B,EAAON,IAAkBK,GAAeT,GAAOM,GAAkB,EAAA,GACjE,MACF,IAAK,MACHI,EAAON,IAAkBK,GAAeT,GAAOM,GAAkB,EAAA,GAG9D,OAAAI,CACT,CAqGAC,eAAeC,GAAerV,EAAOsV,GAC/B,IAAAC,OACY,IAAZD,IACFA,EAAU,CAAE,GAER,MAAAjL,EACJA,EAAAC,EACAA,EACAkL,SAAAA,EAAAA,MACAC,EAAAC,SACAA,EAAAC,SACAA,GACE3V,GACE4V,SACJA,EAAW,oBAAAC,aACXA,EAAe,WAAAC,eACfA,EAAiB,WAAAC,YACjBA,GAAc,EAAAlS,QACdA,EAAU,GACRuP,GAASkC,EAAStV,GAChBgW,EAAgB7B,GAAiBtQ,GAEjClP,EAAU+gB,EAASK,EADa,aAAnBD,EAAgC,YAAc,WACbA,GAC9CG,EAAqB5B,SAAuBmB,EAASU,gBAAgB,CACzEvhB,QAAiH,OAAtG4gB,QAAqD,MAAtBC,EAASW,eAAoB,EAASX,EAASW,UAAUxhB,MAAqB4gB,EAAgC5gB,EAAUA,EAAQyhB,sBAAyD,MAA/BZ,EAASa,wBAA6B,EAASb,EAASa,mBAAmBX,EAASf,WACxRiB,WACAC,eACAF,cAEIrB,EAA0B,aAAnBwB,EAAgC,CAC3CzL,IACAC,IACA3G,MAAO8R,EAAMd,SAAShR,MACtBC,OAAQ6R,EAAMd,SAAS/Q,QACrB6R,EAAMf,UACJ4B,QAAkD,MAA5Bd,EAASe,qBAA0B,EAASf,EAASe,gBAAgBb,EAASf,WACpG6B,QAA4C,MAAtBhB,EAASW,eAAoB,EAASX,EAASW,UAAUG,WAA+C,MAArBd,EAASiB,cAAmB,EAASjB,EAASiB,SAASH,KAGlK,CACFjM,EAAG,EACHC,EAAG,GAECoM,EAAoBrC,GAAiBmB,EAASmB,4DAA8DnB,EAASmB,sDAAsD,CAC/KjB,WACApB,OACAgC,eACAX,aACGrB,GACE,MAAA,CACLtB,KAAMiD,EAAmBjD,IAAM0D,EAAkB1D,IAAMgD,EAAchD,KAAOwD,EAAYlM,EACxFyI,QAAS2D,EAAkB3D,OAASkD,EAAmBlD,OAASiD,EAAcjD,QAAUyD,EAAYlM,EACpGuI,MAAOoD,EAAmBpD,KAAO6D,EAAkB7D,KAAOmD,EAAcnD,MAAQ2D,EAAYnM,EAC5FyI,OAAQ4D,EAAkB5D,MAAQmD,EAAmBnD,MAAQkD,EAAclD,OAAS0D,EAAYnM,EAEpG,CA8TA,SAASuM,GAAe7S,EAAUuQ,GACzB,MAAA,CACLtB,IAAKjP,EAASiP,IAAMsB,EAAK1Q,OACzBkP,MAAO/O,EAAS+O,MAAQwB,EAAK3Q,MAC7BoP,OAAQhP,EAASgP,OAASuB,EAAK1Q,OAC/BiP,KAAM9O,EAAS8O,KAAOyB,EAAK3Q,MAE/B,CACA,SAASkT,GAAsB9S,GAC7B,OAAOwO,GAAM3V,MAAKsX,GAAQnQ,EAASmQ,IAAS,GAC9C,CC7hBA,SAAS4C,KACP,MAAyB,oBAAX/d,MAChB,CACA,SAASge,GAAY5mB,GACf,OAAA6mB,GAAO7mB,IACDA,EAAK8mB,UAAY,IAAIC,cAKxB,WACT,CACA,SAASC,GAAUhnB,GACb,IAAAinB,EACI,OAAQ,MAARjnB,GAA8D,OAA7CinB,EAAsBjnB,EAAKyK,oBAAyB,EAASwc,EAAoB/V,cAAgBtI,MAC5H,CACA,SAASsd,GAAmBlmB,GACtB,IAAAqkB,EACJ,OAA0F,OAAlFA,GAAQwC,GAAO7mB,GAAQA,EAAKyK,cAAgBzK,EAAK2K,WAAa/B,OAAO+B,eAAoB,EAAS0Z,EAAK6C,eACjH,CACA,SAASL,GAAOhnB,GACV,QAAC8mB,OAGE9mB,aAAiB2I,MAAQ3I,aAAiBmnB,GAAUnnB,GAAO2I,KACpE,CACA,SAASwd,GAAUnmB,GACb,QAAC8mB,OAGE9mB,aAAiBsnB,SAAWtnB,aAAiBmnB,GAAUnnB,GAAOsnB,QACvE,CACA,SAAS5H,GAAc1f,GACjB,QAAC8mB,OAGE9mB,aAAiBunB,aAAevnB,aAAiBmnB,GAAUnnB,GAAOunB,YAC3E,CACA,SAASC,GAAaxnB,GACpB,SAAK8mB,MAAqC,oBAAfW,cAGpBznB,aAAiBynB,YAAcznB,aAAiBmnB,GAAUnnB,GAAOynB,WAC1E,CACA,SAASC,GAAkB/iB,GACnB,MAAAoP,SACJA,EAAA4T,UACAA,EAAAC,UACAA,EAAA1W,QACAA,GACEa,GAAiBpN,GACrB,MAAO,kCAAkCc,KAAKsO,EAAW6T,EAAYD,KAAe,CAAC,SAAU,YAAYnW,SAASN,EACtH,CACA,SAAS2W,GAAeljB,GACf,MAAA,CAAC,QAAS,KAAM,MAAM6M,SAASuV,GAAYpiB,GACpD,CACA,SAASmjB,GAAWnjB,GAClB,MAAO,CAAC,gBAAiB,UAAUiI,MAAiBmb,IAC9C,IACK,OAAApjB,EAAQqjB,QAAQD,EACxB,OAAQppB,GACA,OAAA,CACb,IAEA,CACA,SAASspB,GAAkBC,GACzB,MAAMC,EAASC,KACTC,EAAMlC,GAAU+B,GAAgBnW,GAAiBmW,GAAgBA,EAIvE,MAAO,CAAC,YAAa,YAAa,QAAS,SAAU,eAAetb,MAAK5M,KAASqoB,EAAIroB,IAAwB,SAAfqoB,EAAIroB,QAA+BqoB,EAAIC,eAAsC,WAAtBD,EAAIC,gBAAwCH,KAAWE,EAAIE,gBAAwC,SAAvBF,EAAIE,iBAAuCJ,KAAWE,EAAIziB,QAAwB,SAAfyiB,EAAIziB,QAA8B,CAAC,YAAa,YAAa,QAAS,SAAU,cAAe,UAAUgH,MAAK5M,IAAUqoB,EAAIG,YAAc,IAAIhX,SAASxR,MAAW,CAAC,QAAS,SAAU,SAAU,WAAW4M,UAAeyb,EAAII,SAAW,IAAIjX,SAASxR,IAC7hB,CAaA,SAASooB,KACP,QAAmB,oBAARM,MAAwBA,IAAIC,WAChCD,IAAIC,SAAS,0BAA2B,OACjD,CACA,SAASC,GAAsBzoB,GACtB,MAAA,CAAC,OAAQ,OAAQ,aAAaqR,SAASuV,GAAY5mB,GAC5D,CACA,SAAS4R,GAAiBpN,GACxB,OAAOwiB,GAAUxiB,GAASoN,iBAAiBpN,EAC7C,CACA,SAASkkB,GAAclkB,GACjB,OAAAwhB,GAAUxhB,GACL,CACLmkB,WAAYnkB,EAAQmkB,WACpBC,UAAWpkB,EAAQokB,WAGhB,CACLD,WAAYnkB,EAAQqkB,QACpBD,UAAWpkB,EAAQskB,QAEvB,CACA,SAASC,GAAc/oB,GACjB,GAAsB,SAAtB4mB,GAAY5mB,GACP,OAAAA,EAEH,MAAAwF,EAENxF,EAAKgpB,cAELhpB,EAAKipB,YAEL5B,GAAarnB,IAASA,EAAKkpB,MAE3BhD,GAAmBlmB,GACnB,OAAOqnB,GAAa7hB,GAAUA,EAAO0jB,KAAO1jB,CAC9C,CACA,SAAS2jB,GAA2BnpB,GAC5B,MAAAipB,EAAaF,GAAc/oB,GAC7B,OAAAyoB,GAAsBQ,GACjBjpB,EAAKyK,cAAgBzK,EAAKyK,cAAcgD,KAAOzN,EAAKyN,KAEzD8R,GAAc0J,IAAe1B,GAAkB0B,GAC1CA,EAEFE,GAA2BF,EACpC,CACA,SAASG,GAAqBppB,EAAMqpB,EAAMC,GACpC,IAAAC,OACS,IAATF,IACFA,EAAO,SAEe,IAApBC,IACgBA,GAAA,GAEd,MAAAE,EAAqBL,GAA2BnpB,GAChDypB,EAASD,KAAuE,OAA9CD,EAAuBvpB,EAAKyK,oBAAyB,EAAS8e,EAAqB9b,MACrHic,EAAM1C,GAAUwC,GACtB,GAAIC,EAAQ,CACJ,MAAAE,EAAeC,GAAgBF,GACrC,OAAOL,EAAKQ,OAAOH,EAAKA,EAAII,gBAAkB,GAAIvC,GAAkBiC,GAAsBA,EAAqB,GAAIG,GAAgBL,EAAkBF,GAAqBO,GAAgB,GAC9L,CACS,OAAAN,EAAKQ,OAAOL,EAAoBJ,GAAqBI,EAAoB,GAAIF,GACtF,CACA,SAASM,GAAgBF,GAChB,OAAAA,EAAIK,QAAUxsB,OAAOysB,eAAeN,EAAIK,QAAUL,EAAIC,aAAe,IAC9E,CClJA,SAASM,GAAiBzlB,GAClB,MAAA0jB,EAAMtW,GAAiBpN,GAG7B,IAAIgP,EAAQ0W,WAAWhC,EAAI1U,QAAU,EACjCC,EAASyW,WAAWhC,EAAIzU,SAAW,EACjC,MAAA0W,EAAY5K,GAAc/a,GAC1B4lB,EAAcD,EAAY3lB,EAAQ4lB,YAAc5W,EAChD6W,EAAeF,EAAY3lB,EAAQ6lB,aAAe5W,EAClD6W,EAAiBjI,GAAM7O,KAAW4W,GAAe/H,GAAM5O,KAAY4W,EAKlE,OAJHC,IACM9W,EAAA4W,EACC3W,EAAA4W,GAEJ,CACL7W,QACAC,SACA8W,EAAGD,EAEP,CAEA,SAASE,GAAchmB,GACrB,OAAQwhB,GAAUxhB,GAAoCA,EAAzBA,EAAQyhB,cACvC,CAEA,SAASK,GAAS9hB,GACV,MAAAimB,EAAaD,GAAchmB,GAC7B,IAAC+a,GAAckL,GACjB,OAAOlI,GAAa,GAEhB,MAAA4B,EAAOsG,EAAWC,yBAClBlX,MACJA,EAAAC,OACAA,EAAA8W,EACAA,GACEN,GAAiBQ,GACrB,IAAIvQ,GAAKqQ,EAAIlI,GAAM8B,EAAK3Q,OAAS2Q,EAAK3Q,OAASA,EAC3C2G,GAAKoQ,EAAIlI,GAAM8B,EAAK1Q,QAAU0Q,EAAK1Q,QAAUA,EAU1C,OANFyG,GAAMyQ,OAAOC,SAAS1Q,KACrBA,EAAA,GAEDC,GAAMwQ,OAAOC,SAASzQ,KACrBA,EAAA,GAEC,CACLD,IACAC,IAEJ,CAEA,MAAM0Q,MAAsC,GAC5C,SAASC,GAAiBtmB,GAClB,MAAAklB,EAAM1C,GAAUxiB,GACtB,OAAKyjB,MAAeyB,EAAII,eAGjB,CACL5P,EAAGwP,EAAII,eAAeiB,WACtB5Q,EAAGuP,EAAII,eAAekB,WAJfH,EAMX,CAWA,SAASH,GAAsBlmB,EAASymB,EAAcC,EAAiB/E,QAChD,IAAjB8E,IACaA,GAAA,QAEO,IAApBC,IACgBA,GAAA,GAEd,MAAAC,EAAa3mB,EAAQkmB,wBACrBD,EAAaD,GAAchmB,GAC7B,IAAA4mB,EAAQ7I,GAAa,GACrB0I,IACE9E,EACEH,GAAUG,KACZiF,EAAQ9E,GAASH,IAGnBiF,EAAQ9E,GAAS9hB,IAGf,MAAA6mB,EA7BR,SAAgC7mB,EAAS8mB,EAASC,GAIhD,YAHgB,IAAZD,IACQA,GAAA,MAEPC,GAAwBD,GAAWC,IAAyBvE,GAAUxiB,KAGpE8mB,CACT,CAqBwBE,CAAuBf,EAAYS,EAAiB/E,GAAgB2E,GAAiBL,GAAclI,GAAa,GACtI,IAAIrI,GAAKiR,EAAWzI,KAAO2I,EAAcnR,GAAKkR,EAAMlR,EAChDC,GAAKgR,EAAWtI,IAAMwI,EAAclR,GAAKiR,EAAMjR,EAC/C3G,EAAQ2X,EAAW3X,MAAQ4X,EAAMlR,EACjCzG,EAAS0X,EAAW1X,OAAS2X,EAAMjR,EACvC,GAAIsQ,EAAY,CACR,MAAAf,EAAM1C,GAAUyD,GAChBgB,EAAYtF,GAAgBH,GAAUG,GAAgBa,GAAUb,GAAgBA,EACtF,IAAIuF,EAAahC,EACbiC,EAAgB/B,GAAgB8B,GAC7B,KAAAC,GAAiBxF,GAAgBsF,IAAcC,GAAY,CAC1D,MAAAE,EAActF,GAASqF,GACvBE,EAAaF,EAAcjB,wBAC3BxC,EAAMtW,GAAiB+Z,GACvBjJ,EAAOmJ,EAAWnJ,MAAQiJ,EAAcG,WAAa5B,WAAWhC,EAAI6D,cAAgBH,EAAY1R,EAChG2I,EAAMgJ,EAAWhJ,KAAO8I,EAAcK,UAAY9B,WAAWhC,EAAI+D,aAAeL,EAAYzR,EAClGD,GAAK0R,EAAY1R,EACjBC,GAAKyR,EAAYzR,EACjB3G,GAASoY,EAAY1R,EACrBzG,GAAUmY,EAAYzR,EACjBD,GAAAwI,EACAvI,GAAA0I,EACL6I,EAAa1E,GAAU2E,GACvBA,EAAgB/B,GAAgB8B,EACtC,CACA,CACE,OAAOxH,GAAiB,CACtB1Q,QACAC,SACAyG,IACAC,KAEJ,CAIA,SAAS+R,GAAoB1nB,EAAS2f,GAC9B,MAAAgI,EAAazD,GAAclkB,GAASmkB,WAC1C,OAAKxE,EAGEA,EAAKzB,KAAOyJ,EAFVzB,GAAsBxE,GAAmB1hB,IAAUke,KAAOyJ,CAGrE,CAEA,SAASC,GAAclF,EAAiBmF,EAAQC,QACrB,IAArBA,IACiBA,GAAA,GAEf,MAAAC,EAAWrF,EAAgBwD,wBAK1B,MAAA,CACLxQ,EALQqS,EAAS7J,KAAO2J,EAAO1D,YAAc2D,EAAmB,EAElEJ,GAAoBhF,EAAiBqF,IAInCpS,EAHQoS,EAAS1J,IAAMwJ,EAAOzD,UAKlC,CA6GA,SAAS4D,GAAkChoB,EAASioB,EAAkBjH,GAChE,IAAArB,EACJ,GAAyB,aAArBsI,EACKtI,EA7CX,SAAyB3f,EAASghB,GAC1B,MAAAkE,EAAM1C,GAAUxiB,GAChBkoB,EAAOxG,GAAmB1hB,GAC1BslB,EAAiBJ,EAAII,eAC3B,IAAItW,EAAQkZ,EAAKC,YACblZ,EAASiZ,EAAKE,aACd1S,EAAI,EACJC,EAAI,EACR,GAAI2P,EAAgB,CAClBtW,EAAQsW,EAAetW,MACvBC,EAASqW,EAAerW,OACxB,MAAMoZ,EAAsB5E,OACvB4E,GAAuBA,GAAoC,UAAbrH,KACjDtL,EAAI4P,EAAeiB,WACnB5Q,EAAI2P,EAAekB,UAEzB,CACS,MAAA,CACLxX,QACAC,SACAyG,IACAC,IAEJ,CAsBW2S,CAAgBtoB,EAASghB,QACpC,GAAkC,aAArBiH,EACFtI,EAlEX,SAAyB3f,GACjB,MAAAkoB,EAAOxG,GAAmB1hB,GAC1B6nB,EAAS3D,GAAclkB,GACvBiJ,EAAOjJ,EAAQiG,cAAcgD,KAC7B+F,EAAQqJ,GAAI6P,EAAKK,YAAaL,EAAKC,YAAalf,EAAKsf,YAAatf,EAAKkf,aACvElZ,EAASoJ,GAAI6P,EAAKM,aAAcN,EAAKE,aAAcnf,EAAKuf,aAAcvf,EAAKmf,cACjF,IAAI1S,GAAKmS,EAAO1D,WAAauD,GAAoB1nB,GAC3C,MAAA2V,GAAKkS,EAAOzD,UAIX,MAHkC,QAArChX,GAAiBnE,GAAM0S,YACzBjG,GAAK2C,GAAI6P,EAAKC,YAAalf,EAAKkf,aAAenZ,GAE1C,CACLA,QACAC,SACAyG,IACAC,IAEJ,CAiDW8S,CAAgB/G,GAAmB1hB,SAC9C,GAAawhB,GAAUyG,GACZtI,EAvBX,SAAoC3f,EAASghB,GAC3C,MAAM2F,EAAaT,GAAsBlmB,GAAS,EAAmB,UAAbghB,GAClD3C,EAAMsI,EAAWtI,IAAMre,EAAQwnB,UAC/BtJ,EAAOyI,EAAWzI,KAAOle,EAAQsnB,WACjCV,EAAQ7L,GAAc/a,GAAW8hB,GAAS9hB,GAAW+d,GAAa,GAKjE,MAAA,CACL/O,MALYhP,EAAQmoB,YAAcvB,EAAMlR,EAMxCzG,OALajP,EAAQooB,aAAexB,EAAMjR,EAM1CD,EALQwI,EAAO0I,EAAMlR,EAMrBC,EALQ0I,EAAMuI,EAAMjR,EAOxB,CAQW+S,CAA2BT,EAAkBjH,OAC/C,CACC,MAAA6F,EAAgBP,GAAiBtmB,GAChC2f,EAAA,CACLjK,EAAGuS,EAAiBvS,EAAImR,EAAcnR,EACtCC,EAAGsS,EAAiBtS,EAAIkR,EAAclR,EACtC3G,MAAOiZ,EAAiBjZ,MACxBC,OAAQgZ,EAAiBhZ,OAE/B,CACE,OAAOyQ,GAAiBC,EAC1B,CACA,SAASgJ,GAAyB3oB,EAAS4oB,GACnC,MAAAnE,EAAaF,GAAcvkB,GAC7B,QAAAykB,IAAemE,IAAapH,GAAUiD,IAAeR,GAAsBQ,MAG9B,UAA1CrX,GAAiBqX,GAAY3V,UAAwB6Z,GAAyBlE,EAAYmE,GACnG,CA2EA,SAASC,GAA8B7oB,EAAS2hB,EAAcX,GACtD,MAAA8H,EAA0B/N,GAAc4G,GACxCe,EAAkBhB,GAAmBC,GACrCmF,EAAuB,UAAb9F,EACVrB,EAAOuG,GAAsBlmB,GAAS,EAAM8mB,EAASnF,GAC3D,IAAIkG,EAAS,CACX1D,WAAY,EACZC,UAAW,GAEP,MAAA2E,EAAUhL,GAAa,GAI7B,SAASiL,IACCD,EAAArT,EAAIgS,GAAoBhF,EACpC,CACE,GAAIoG,IAA4BA,IAA4BhC,EAI1D,IAHkC,SAA9B1E,GAAYT,IAA4BoB,GAAkBL,MAC5DmF,EAAS3D,GAAcvC,IAErBmH,EAAyB,CAC3B,MAAMG,EAAa/C,GAAsBvE,GAAc,EAAMmF,EAASnF,GAC9DoH,EAAArT,EAAIuT,EAAWvT,EAAIiM,EAAa2F,WAChCyB,EAAApT,EAAIsT,EAAWtT,EAAIgM,EAAa6F,SACzC,MAAU9E,GACkBsG,IAG3BlC,IAAYgC,GAA2BpG,GACdsG,IAEvB,MAAAE,GAAaxG,GAAoBoG,GAA4BhC,EAAmD/I,GAAa,GAAtD6J,GAAclF,EAAiBmF,GAGrG,MAAA,CACLnS,EAHQiK,EAAKzB,KAAO2J,EAAO1D,WAAa4E,EAAQrT,EAAIwT,EAAWxT,EAI/DC,EAHQgK,EAAKtB,IAAMwJ,EAAOzD,UAAY2E,EAAQpT,EAAIuT,EAAWvT,EAI7D3G,MAAO2Q,EAAK3Q,MACZC,OAAQ0Q,EAAK1Q,OAEjB,CAEA,SAASka,GAAmBnpB,GACnBoN,MAAuC,WAAvCA,GAAiBpN,GAAS8O,QACnC,CAEA,SAASsa,GAAoBppB,EAASqpB,GAChC,IAACtO,GAAc/a,IAAmD,UAAvCoN,GAAiBpN,GAAS8O,SAChD,OAAA,KAET,GAAIua,EACF,OAAOA,EAASrpB,GAElB,IAAIspB,EAAkBtpB,EAAQ2hB,aASvB,OAHHD,GAAmB1hB,KAAaspB,IAClCA,EAAkBA,EAAgBrjB,cAAcgD,MAE3CqgB,CACT,CAIA,SAAS1H,GAAgB5hB,EAASqpB,GAC1B,MAAAnE,EAAM1C,GAAUxiB,GAClB,GAAAmjB,GAAWnjB,GACN,OAAAklB,EAEL,IAACnK,GAAc/a,GAAU,CACvB,IAAAupB,EAAkBhF,GAAcvkB,GACpC,KAAOupB,IAAoBtF,GAAsBsF,IAAkB,CACjE,GAAI/H,GAAU+H,KAAqBJ,GAAmBI,GAC7C,OAAAA,EAETA,EAAkBhF,GAAcgF,EACtC,CACW,OAAArE,CACX,CACM,IAAAvD,EAAeyH,GAAoBppB,EAASqpB,GAChD,KAAO1H,GAAgBuB,GAAevB,IAAiBwH,GAAmBxH,IACzDA,EAAAyH,GAAoBzH,EAAc0H,GAE/C,OAAA1H,GAAgBsC,GAAsBtC,IAAiBwH,GAAmBxH,KAAkB2B,GAAkB3B,GACzGuD,EAEFvD,GD5XT,SAA4B3hB,GACtB,IAAA4c,EAAc2H,GAAcvkB,GAChC,KAAO+a,GAAc6B,KAAiBqH,GAAsBrH,IAAc,CACpE,GAAA0G,GAAkB1G,GACb,OAAAA,EACb,GAAeuG,GAAWvG,GACb,OAAA,KAETA,EAAc2H,GAAc3H,EAChC,CACS,OAAA,IACT,CCiXyB4M,CAAmBxpB,IAAYklB,CACxD,CAqBA,MAAMrE,GAAW,CACfmB,sDA/TF,SAA+DnC,GACzD,IAAAkB,SACFA,EAAApB,KACAA,EAAAgC,aACAA,EAAAX,SACAA,GACEnB,EACJ,MAAMiH,EAAuB,UAAb9F,EACV0B,EAAkBhB,GAAmBC,GACrC8H,IAAW1I,GAAWoC,GAAWpC,EAASf,UAC5C,GAAA2B,IAAiBe,GAAmB+G,GAAY3C,EAC3C,OAAAnH,EAET,IAAIkI,EAAS,CACX1D,WAAY,EACZC,UAAW,GAETwC,EAAQ7I,GAAa,GACnB,MAAAgL,EAAUhL,GAAa,GACvB+K,EAA0B/N,GAAc4G,GAC9C,IAAImH,IAA4BA,IAA4BhC,MACxB,SAA9B1E,GAAYT,IAA4BoB,GAAkBL,MAC5DmF,EAAS3D,GAAcvC,IAErB5G,GAAc4G,IAAe,CACzB,MAAAsH,EAAa/C,GAAsBvE,GACzCiF,EAAQ9E,GAASH,GACToH,EAAArT,EAAIuT,EAAWvT,EAAIiM,EAAa2F,WAChCyB,EAAApT,EAAIsT,EAAWtT,EAAIgM,EAAa6F,SAC9C,CAEE,MAAM0B,GAAaxG,GAAoBoG,GAA4BhC,EAAyD/I,GAAa,GAA5D6J,GAAclF,EAAiBmF,GAAQ,GAC7G,MAAA,CACL7Y,MAAO2Q,EAAK3Q,MAAQ4X,EAAMlR,EAC1BzG,OAAQ0Q,EAAK1Q,OAAS2X,EAAMjR,EAC5BD,EAAGiK,EAAKjK,EAAIkR,EAAMlR,EAAImS,EAAO1D,WAAayC,EAAMlR,EAAIqT,EAAQrT,EAAIwT,EAAWxT,EAC3EC,EAAGgK,EAAKhK,EAAIiR,EAAMjR,EAAIkS,EAAOzD,UAAYwC,EAAMjR,EAAIoT,EAAQpT,EAAIuT,EAAWvT,EAE9E,EA0RE+L,sBACAH,gBAvJF,SAAyB1B,GACnB,IAAA7f,QACFA,EAAAihB,SACAA,EAAAC,aACAA,EAAAF,SACAA,GACEnB,EACJ,MACM6J,EAAoB,IADoB,sBAAbzI,EAAmCkC,GAAWnjB,GAAW,GAxC5F,SAAqCA,EAAS2pB,GACtC,MAAAC,EAAeD,EAAMxpB,IAAIH,GAC/B,GAAI4pB,EACK,OAAAA,EAET,IAAI5oB,EAAS4jB,GAAqB5kB,EAAS,IAAI,GAAOiB,QAAO4oB,GAAMrI,GAAUqI,IAA2B,SAApBzH,GAAYyH,KAC5FC,EAAsC,KAC1C,MAAMC,EAAwD,UAAvC3c,GAAiBpN,GAAS8O,SACjD,IAAI8N,EAAcmN,EAAiBxF,GAAcvkB,GAAWA,EAG5D,KAAOwhB,GAAU5E,KAAiBqH,GAAsBrH,IAAc,CAC9D,MAAAoN,EAAgB5c,GAAiBwP,GACjCqN,EAA0B3G,GAAkB1G,GAC7CqN,GAAsD,UAA3BD,EAAclb,WACNgb,EAAA,OAEVC,GAAkBE,IAA4BH,GAAuCG,GAAsD,WAA3BD,EAAclb,UAA2Bgb,GAAuC,CAAC,WAAY,SAASjd,SAASid,EAAoChb,WAAaiU,GAAkBnG,KAAiBqN,GAA2BtB,GAAyB3oB,EAAS4c,IAG5Y5b,EAASA,EAAOC,QAAmBipB,GAAAA,IAAatN,IAGVkN,EAAAE,EAExCpN,EAAc2H,GAAc3H,EAChC,CAES,OADD+M,EAAAvmB,IAAIpD,EAASgB,GACZA,CACT,CAWiGmpB,CAA4BnqB,EAASoqB,KAAKtW,IAAM,GAAGuR,OAAOpE,GACjGC,GAClDmJ,EAAwBX,EAAkB,GAC1CY,EAAeZ,EAAkBrrB,QAAO,CAACksB,EAAStC,KACtD,MAAMtI,EAAOqI,GAAkChoB,EAASioB,EAAkBjH,GAKnE,OAJPuJ,EAAQlM,IAAMhG,GAAIsH,EAAKtB,IAAKkM,EAAQlM,KACpCkM,EAAQpM,MAAQ/F,GAAIuH,EAAKxB,MAAOoM,EAAQpM,OACxCoM,EAAQnM,OAAShG,GAAIuH,EAAKvB,OAAQmM,EAAQnM,QAC1CmM,EAAQrM,KAAO7F,GAAIsH,EAAKzB,KAAMqM,EAAQrM,MAC/BqM,IACNvC,GAAkChoB,EAASqqB,EAAuBrJ,IAC9D,MAAA,CACLhS,MAAOsb,EAAanM,MAAQmM,EAAapM,KACzCjP,OAAQqb,EAAalM,OAASkM,EAAajM,IAC3C3I,EAAG4U,EAAapM,KAChBvI,EAAG2U,EAAajM,IAEpB,EAgIEuD,mBACA4I,gBAxBsB/J,eAAgBgK,GAChC,MAAAC,EAAoBN,KAAKxI,iBAAmBA,GAC5C+I,EAAkBP,KAAKQ,cACvBC,QAA2BF,EAAgBF,EAAKzK,UAC/C,MAAA,CACLD,UAAW8I,GAA8B4B,EAAK1K,gBAAiB2K,EAAkBD,EAAKzK,UAAWyK,EAAKzJ,UACtGhB,SAAU,CACRtK,EAAG,EACHC,EAAG,EACH3G,MAAO6b,EAAmB7b,MAC1BC,OAAQ4b,EAAmB5b,QAGjC,EAYE6b,eA5RF,SAAwB9qB,GACtB,OAAOyD,MAAMC,KAAK1D,EAAQ8qB,iBAC5B,EA2REF,cAjIF,SAAuB5qB,GACf,MAAAgP,MACJA,EAAAC,OACAA,GACEwW,GAAiBzlB,GACd,MAAA,CACLgP,QACAC,SAEJ,EAyHE6S,YACAN,aACAuJ,MAdF,SAAe/qB,GACNoN,MAAwC,QAAxCA,GAAiBpN,GAAS2b,SACnC,GAeA,SAASqP,GAAcpxB,EAAGE,GACxB,OAAOF,EAAE8b,IAAM5b,EAAE4b,GAAK9b,EAAE+b,IAAM7b,EAAE6b,GAAK/b,EAAEoV,QAAUlV,EAAEkV,OAASpV,EAAEqV,SAAWnV,EAAEmV,MAC7E,CAkGA,SAASgc,GAAWlL,EAAWC,EAAUkL,EAAQvK,QAC/B,IAAZA,IACFA,EAAU,CAAE,GAER,MAAAwK,eACJA,GAAiB,EAAAC,eACjBA,GAAiB,EAAAC,cACjBA,EAA0C,mBAAnBC,eAAmBC,YAC1CA,EAA8C,mBAAzBC,qBAAyBC,eAC9CA,GAAiB,GACf9K,EACE+K,EAAc1F,GAAcjG,GAC5B4L,EAAYR,GAAkBC,EAAiB,IAAKM,EAAc9G,GAAqB8G,GAAe,MAAQ9G,GAAqB5E,IAAa,GACtJ2L,EAAU7Q,SAAoBoP,IACViB,GAAAjB,EAASziB,iBAAiB,SAAUyjB,EAAQ,CAC5DU,SAAS,IAEOR,GAAAlB,EAASziB,iBAAiB,SAAUyjB,MAExD,MAAMW,EAAYH,GAAeH,EAlHnC,SAAqBvrB,EAAS8rB,GAC5B,IACItf,EADAuf,EAAK,KAEH,MAAAC,EAAOtK,GAAmB1hB,GAChC,SAASpE,IACH,IAAAqwB,EACJpkB,aAAa2E,GACC,OAAbyf,EAAMF,IAAeE,EAAIC,aACrBH,EAAA,IACT,CA2ES,OA1EE,SAAAI,EAAQC,EAAMxQ,QACR,IAATwQ,IACKA,GAAA,QAES,IAAdxQ,IACUA,EAAA,GAELhgB,IACH,MAAAywB,EAA2BrsB,EAAQkmB,yBACnChI,KACJA,EAAAG,IACAA,EAAArP,MACAA,EAAAC,OACAA,GACEod,EAIA,GAHCD,GACKN,KAEL9c,IAAUC,EACb,OAEI,MAKA0R,EAAU,CACd2L,YANexO,GAAMO,GAIQ,OAHZP,GAAMkO,EAAK7D,aAAejK,EAAOlP,IAGC,OAFjC8O,GAAMkO,EAAK5D,cAAgB/J,EAAMpP,IAEuB,OAD1D6O,GAAMI,GACyE,KAG/FtC,UAAWvD,GAAI,EAAGD,GAAI,EAAGwD,KAAe,GAE1C,IAAI2Q,GAAgB,EACpB,SAASC,EAAcC,GACf,MAAAC,EAAQD,EAAQ,GAAGE,kBACzB,GAAID,IAAU9Q,EAAW,CACvB,IAAK2Q,EACH,OAAOJ,IAEJO,EAOHP,GAAQ,EAAOO,GAJflgB,EAAY5E,YAAW,KACrBukB,GAAQ,EAAO,QACd,IAIb,CACoB,IAAVO,GAAgB1B,GAAcqB,EAA0BrsB,EAAQkmB,0BAQzDiG,IAEKI,GAAA,CACtB,CAIQ,IACGR,EAAA,IAAIP,qBAAqBgB,EAAe,IACxC7L,EAEHqL,KAAMA,EAAK/lB,eAEd,OAAQ2mB,GACFb,EAAA,IAAIP,qBAAqBgB,EAAe7L,EACnD,CACIoL,EAAGc,QAAQ7sB,EACf,CACEmsB,EAAQ,GACDvwB,CACT,CA6BiDkxB,CAAYpB,EAAaR,GAAU,KAClF,IAsBI6B,EAtBAC,GAAiB,EACjBC,EAAiB,KACjB5B,IACe4B,EAAA,IAAI3B,gBAAuBzL,IACtC,IAACqN,GAAcrN,EACfqN,GAAcA,EAAW5oB,SAAWonB,GAAeuB,IAGrDA,EAAeE,UAAUnN,GACzBrG,qBAAqBqT,GACrBA,EAAiBtT,uBAAsB,KACjC,IAAA0T,EACkC,OAArCA,EAAkBH,IAA2BG,EAAgBP,QAAQ7M,OAGlEkL,OAENQ,IAAgBD,GAClBwB,EAAeJ,QAAQnB,GAEzBuB,EAAeJ,QAAQ7M,IAGzB,IAAIqN,EAAc5B,EAAiBvF,GAAsBnG,GAAa,KAatE,OAZI0L,GAGJ,SAAS6B,IACD,MAAAC,EAAcrH,GAAsBnG,GACtCsN,IAAgBrC,GAAcqC,EAAaE,IACrCrC,IAEImC,EAAAE,EACdR,EAAUrT,sBAAsB4T,EACpC,CATeA,GAULpC,IACD,KACD,IAAAsC,EACJ7B,EAAU7Q,SAAoBoP,IACViB,GAAAjB,EAAS1iB,oBAAoB,SAAU0jB,GACvCE,GAAAlB,EAAS1iB,oBAAoB,SAAU0jB,MAE9C,MAAbW,GAAqBA,IACkB,OAAtC2B,EAAmBP,IAA2BO,EAAiBtB,aAC/Ce,EAAA,KACbxB,GACF9R,qBAAqBoT,GAG3B,CAmBA,MAAMU,GFyGS,SAAU9M,GAIhB,YAHS,IAAZA,IACQA,EAAA,GAEL,CACL9e,KAAM,SACN8e,UACA,QAAMpH,CAAGlO,GACP,IAAIqiB,EAAuBC,EACrB,MAAAjY,EACJA,EAAAC,EACAA,EAAAiJ,UACAA,EAAAgP,eACAA,GACEviB,EACEwiB,QA9DZpN,eAAoCpV,EAAOsV,GACnC,MAAA/B,UACJA,EACAiC,SAAAA,EAAAA,SACAE,GACE1V,EACEyU,QAA+B,MAAlBe,EAASkK,WAAgB,EAASlK,EAASkK,MAAMhK,EAASf,WACvET,EAAOZ,GAAQC,GACfS,EAAYP,GAAaF,GACzBwB,EAAwC,MAA3BlB,GAAYN,GACzBkP,EAAgB,CAAC,OAAQ,OAAOjhB,SAAS0S,IAAa,EAAA,EACtDwO,EAAiBjO,GAAOM,GAAkB,EAAA,EAC1C4N,EAAWvP,GAASkC,EAAStV,GAG/B,IAAA4iB,SACFA,EAAAC,UACAA,EAAAhO,cACAA,GACsB,iBAAb8N,EAAwB,CACjCC,SAAUD,EACVE,UAAW,EACXhO,cAAe,MACb,CACF+N,SAAUD,EAASC,UAAY,EAC/BC,UAAWF,EAASE,WAAa,EACjChO,cAAe8N,EAAS9N,eAK1B,OAHIb,GAAsC,iBAAlBa,IACVgO,EAAc,QAAd7O,GAA2C,EAArBa,EAAqBA,GAElDE,EAAa,CAClB1K,EAAGwY,EAAYH,EACfpY,EAAGsY,EAAWH,GACZ,CACFpY,EAAGuY,EAAWH,EACdnY,EAAGuY,EAAYH,EAEnB,CAwB+BI,CAAqB9iB,EAAOsV,GAIrD,OAAI/B,KAAkE,OAAlD8O,EAAwBE,EAAeH,aAAkB,EAASC,EAAsB9O,YAAgE,OAAjD+O,EAAwBC,EAAeQ,QAAkBT,EAAsBU,gBACjM,CAAE,EAEJ,CACL3Y,EAAGA,EAAImY,EAAWnY,EAClBC,EAAGA,EAAIkY,EAAWlY,EAClB8U,KAAM,IACDoD,EACHjP,aAGV,EAEA,EE1HM0P,GFiIQ,SAAU3N,GAIf,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL9e,KAAM,QACN8e,UACA,QAAMpH,CAAGlO,GACD,MAAAqK,EACJA,EAAAC,EACAA,EAAAiJ,UACAA,GACEvT,GAEF4iB,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,EAAAC,QAC5BA,EAAU,CACRlV,GAAYsG,IACN,IACFnK,EAAAA,EACAC,EAAAA,GACEkK,EACG,MAAA,CACLnK,EAAAA,EACAC,EAAAA,QAIH+Y,GACDjQ,GAASkC,EAAStV,GAChBmV,EAAS,CACb9K,IACAC,KAEIvG,QAAiBsR,GAAerV,EAAOqjB,GACvCR,EAAYhP,GAAYP,GAAQC,IAChCqP,EAAWlP,GAAgBmP,GAC7B,IAAAS,EAAgBnO,EAAOyN,GACvBW,EAAiBpO,EAAO0N,GAC5B,GAAIK,EAAe,CACX,MACAM,EAAuB,MAAbZ,EAAmB,SAAW,QAG9B/V,EAAAA,GAFJyW,EAAgBvf,EAFC,MAAb6e,EAAmB,MAAQ,QAIhBU,EADfA,EAAgBvf,EAASyf,GAE7C,CACM,GAAIL,EAAgB,CACZ,MACAK,EAAwB,MAAdX,EAAoB,SAAW,QAG9BhW,EAAAA,GAFL0W,EAAiBxf,EAFC,MAAd8e,EAAoB,MAAQ,QAIhBU,EADhBA,EAAiBxf,EAASyf,GAE9C,CACY,MAAAC,EAAgBL,EAAQlV,GAAG,IAC5BlO,EACH4iB,CAACA,GAAWU,EACZT,CAACA,GAAYU,IAER,MAAA,IACFE,EACHrE,KAAM,CACJ/U,EAAGoZ,EAAcpZ,EAAIA,EACrBC,EAAGmZ,EAAcnZ,EAAIA,EACrBoZ,QAAS,CACPd,CAACA,GAAWM,EACZL,CAACA,GAAYM,IAIzB,EAEA,EEhMMQ,GFrSO,SAAUrO,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL9e,KAAM,OACN8e,UACA,QAAMpH,CAAGlO,GACP,IAAIsiB,EAAuBsB,EACrB,MAAArQ,UACJA,EAAAgP,eACAA,EAAA9M,MACAA,EAAAoO,iBACAA,EACArO,SAAAA,EAAAA,SACAE,GACE1V,GAEF4iB,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,EAC5BW,mBAAoBC,EAAAC,iBACpBA,EAAmB,UAAAC,0BACnBA,EAA4B,OAAAC,cAC5BA,GAAgB,KACbb,GACDjQ,GAASkC,EAAStV,GAMtB,GAAsD,OAAjDsiB,EAAwBC,EAAeQ,QAAkBT,EAAsBU,gBAClF,MAAO,CAAE,EAEL,MAAA9O,EAAOZ,GAAQC,GACf4Q,EAAkBtQ,GAAYgQ,GAC9BO,EAAkB9Q,GAAQuQ,KAAsBA,EAChDpP,QAA+B,MAAlBe,EAASkK,WAAgB,EAASlK,EAASkK,MAAMhK,EAASf,WACvEmP,EAAqBC,IAAgCK,IAAoBF,EAAgB,CAACjQ,GAAqB4P,ID7X3H,SAA+BtQ,GACvB,MAAA8Q,EAAoBpQ,GAAqBV,GAC/C,MAAO,CAACQ,GAA8BR,GAAY8Q,EAAmBtQ,GAA8BsQ,GACrG,CC0XgJC,CAAsBT,IAC1JU,EAA6D,SAA9BN,GAChCF,GAA+BQ,GAClCT,EAAmBjU,QDxW3B,SAAmC0D,EAAW2Q,EAAe5T,EAAWmE,GAChE,MAAAT,EAAYP,GAAaF,GAC/B,IAAIiG,EAnBN,SAAqBtF,EAAMsQ,EAAS/P,GAC5B,MAAAgQ,EAAK,CAAC,OAAQ,SACdC,EAAK,CAAC,QAAS,QACfC,EAAK,CAAC,MAAO,UACbC,EAAK,CAAC,SAAU,OACtB,OAAQ1Q,GACN,IAAK,MACL,IAAK,SACC,OAAAO,EAAY+P,EAAUE,EAAKD,EACxBD,EAAUC,EAAKC,EACxB,IAAK,OACL,IAAK,QACH,OAAOF,EAAUG,EAAKC,EACxB,QACE,MAAO,GAEb,CAGaC,CAAYvR,GAAQC,GAA0B,UAAdjD,EAAuBmE,GAO3D,OANHT,IACFwF,EAAOA,EAAKlpB,KAAY4jB,GAAAA,EAAO,IAAMF,IACjCkQ,IACF1K,EAAOA,EAAKQ,OAAOR,EAAKlpB,IAAIyjB,OAGzByF,CACT,CC8VmCsL,CAA0BjB,EAAkBK,EAAeD,EAA2BxP,IAEnH,MAAMsQ,EAAa,CAAClB,KAAqBC,GACnC/f,QAAiBsR,GAAerV,EAAOqjB,GACvC2B,EAAY,GACd,IAAAC,GAAiE,OAA/CrB,EAAuBrB,EAAeoB,WAAgB,EAASC,EAAqBoB,YAAc,GAIxH,GAHI9B,GACQ8B,EAAAnV,KAAK9L,EAASmQ,IAEtBiP,EAAgB,CAClB,MAAM5Q,EDvZd,SAA2BgB,EAAWkC,EAAOhB,QAC/B,IAARA,IACIA,GAAA,GAEF,MAAAT,EAAYP,GAAaF,GACzBsB,EAAgBf,GAAiBP,GACjC9iB,EAASmjB,GAAciB,GACzB,IAAAqQ,EAAsC,MAAlBrQ,EAAwBb,KAAeS,EAAM,MAAQ,SAAW,QAAU,OAAuB,UAAdT,EAAwB,SAAW,MAI9I,OAHIyB,EAAMf,UAAUjkB,GAAUglB,EAAMd,SAASlkB,KAC3Cy0B,EAAoBjR,GAAqBiR,IAEpC,CAACA,EAAmBjR,GAAqBiR,GAClD,CC2YsBC,CAAkB5R,EAAWkC,EAAOhB,GACxCuQ,EAAAnV,KAAK9L,EAASwO,EAAM,IAAKxO,EAASwO,EAAM,IAC1D,CAOM,GANgB0S,EAAA,IAAIA,EAAe,CACjC1R,YACAyR,eAIGA,EAAUve,OAAMyN,GAAQA,GAAQ,IAAI,CACvC,IAAIkR,EAAuBC,EACrB,MAAAC,IAA+D,OAAhDF,EAAwB7C,EAAeoB,WAAgB,EAASyB,EAAsBxzB,QAAU,GAAK,EACpH2zB,EAAgBR,EAAWO,GACjC,GAAIC,EAAe,CACb,IAAAC,EACJ,MAAMC,EAA6C,cAAnBtC,GAAiCgB,IAAoBtQ,GAAY0R,GAC3FG,GAAsE,OAAvCF,EAAkBP,EAAc,SAAc,EAASO,EAAgBR,UAAU,IAAM,EACxH,IAACS,GAA2BC,EAEvB,MAAA,CACLtG,KAAM,CACJxtB,MAAO0zB,EACPN,UAAWC,GAEbU,MAAO,CACLpS,UAAWgS,GAI3B,CAIQ,IAAIK,EAAgJ,OAA9HP,EAAwBJ,EAAcrvB,QAAYlH,GAAAA,EAAEs2B,UAAU,IAAM,IAAGzsB,MAAK,CAAChK,EAAGE,IAAMF,EAAEy2B,UAAU,GAAKv2B,EAAEu2B,UAAU,KAAI,SAAc,EAASK,EAAsB9R,UAG1L,IAAKqS,EACH,OAAQ5B,GACN,IAAK,UACH,CACM,IAAA6B,EACJ,MAAMtS,EASmJ,OATtIsS,EAAyBZ,EAAcrvB,QAAYlH,IACpE,GAAI61B,EAA8B,CAC1B,MAAAuB,EAAkBjS,GAAYnlB,EAAE6kB,WACtC,OAAOuS,IAAoB3B,GAGP,MAApB2B,CACpB,CACyB,OAAA,KACNx1B,QAAS,CAAC5B,EAAE6kB,UAAW7kB,EAAEs2B,UAAUpvB,QAAOmO,GAAYA,EAAW,IAAG/Q,QAAO,CAAC+yB,EAAKhiB,IAAagiB,EAAMhiB,GAAU,MAAKxL,MAAK,CAAChK,EAAGE,IAAMF,EAAE,GAAKE,EAAE,KAAI,SAAc,EAASo3B,EAAuB,GAC5LtS,IACeA,EAAAA,GAEnB,KAChB,CACY,IAAK,mBACcqS,EAAA/B,EAIvB,GAAItQ,IAAcqS,EACT,MAAA,CACLD,MAAO,CACLpS,UAAWqS,GAIzB,CACM,MAAO,CAAE,CACf,EAEA,EEkLMtqB,GFoQO,SAAUga,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL9e,KAAM,OACN8e,UACA,QAAMpH,CAAGlO,GACP,IAAIgmB,EAAuBC,EACrB,MAAA1S,UACJA,EAAAkC,MACAA,EACAD,SAAAA,EAAAA,SACAE,GACE1V,GACEkmB,MACJA,EAAQ,UACL7C,GACDjQ,GAASkC,EAAStV,GAChB+D,QAAiBsR,GAAerV,EAAOqjB,GACvCnP,EAAOZ,GAAQC,GACfS,EAAYP,GAAaF,GACzB4S,EAAqC,MAA3BtS,GAAYN,IACtB5P,MACJA,EAAAC,OACAA,GACE6R,EAAMd,SACN,IAAAyR,EACAC,EACS,QAATnS,GAA2B,WAATA,GACPkS,EAAAlS,EACbmS,EAAYrS,WAAyC,MAAlBwB,EAASkK,WAAgB,EAASlK,EAASkK,MAAMhK,EAASf,WAAc,QAAU,OAAS,OAAS,UAE3H0R,EAAAnS,EACCkS,EAAc,QAAdpS,EAAsB,MAAQ,UAE7C,MAAMsS,EAAwB1iB,EAASG,EAASiP,IAAMjP,EAASgP,OACzDwT,EAAuB5iB,EAAQI,EAAS8O,KAAO9O,EAAS+O,MACxD0T,EAA0BzZ,GAAInJ,EAASG,EAASqiB,GAAaE,GAC7DG,EAAyB1Z,GAAIpJ,EAAQI,EAASsiB,GAAYE,GAC1DG,GAAW1mB,EAAMuiB,eAAeU,MACtC,IAAI0D,EAAkBH,EAClBI,EAAiBH,EAOjB,GANwD,OAAvDT,EAAwBhmB,EAAMuiB,eAAeU,QAAkB+C,EAAsBtC,QAAQrZ,IAC/Euc,EAAAL,GAE0C,OAAxDN,EAAyBjmB,EAAMuiB,eAAeU,QAAkBgD,EAAuBvC,QAAQpZ,IAChFqc,EAAAL,GAEhBI,IAAY1S,EAAW,CACzB,MAAM6S,EAAO7Z,GAAIjJ,EAAS8O,KAAM,GAC1BiU,EAAO9Z,GAAIjJ,EAAS+O,MAAO,GAC3BiU,EAAO/Z,GAAIjJ,EAASiP,IAAK,GACzBgU,EAAOha,GAAIjJ,EAASgP,OAAQ,GAC9BoT,EACFS,EAAiBjjB,EAAQ,GAAc,IAATkjB,GAAuB,IAATC,EAAaD,EAAOC,EAAO9Z,GAAIjJ,EAAS8O,KAAM9O,EAAS+O,QAEnG6T,EAAkB/iB,EAAS,GAAc,IAATmjB,GAAuB,IAATC,EAAaD,EAAOC,EAAOha,GAAIjJ,EAASiP,IAAKjP,EAASgP,QAE9G,OACYmT,EAAM,IACPlmB,EACH4mB,iBACAD,oBAEF,MAAMM,QAAuBzR,EAAS+J,cAAc7J,EAASf,UAC7D,OAAIhR,IAAUsjB,EAAetjB,OAASC,IAAWqjB,EAAerjB,OACvD,CACL+hB,MAAO,CACLlQ,OAAO,IAIN,CAAE,CACf,EAEA,EEzUMyR,GFvKO,SAAU5R,GAId,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACL9e,KAAM,OACN8e,UACA,QAAMpH,CAAGlO,GACD,MAAAyV,MACJA,GACEzV,GACE2V,SACJA,EAAW,qBACR0N,GACDjQ,GAASkC,EAAStV,GACtB,OAAQ2V,GACN,IAAK,kBACH,CACQ,MAIA+H,EAAU9G,SAJOvB,GAAerV,EAAO,IACxCqjB,EACHvN,eAAgB,cAEuBL,EAAMf,WACxC,MAAA,CACL0K,KAAM,CACJ+H,uBAAwBzJ,EACxB0J,gBAAiBvQ,GAAsB6G,IAGvD,CACQ,IAAK,UACH,CACQ,MAIAA,EAAU9G,SAJOvB,GAAerV,EAAO,IACxCqjB,EACHtN,aAAa,IAE0BN,EAAMd,UACxC,MAAA,CACLyK,KAAM,CACJiI,eAAgB3J,EAChB4J,QAASzQ,GAAsB6G,IAG/C,CACQ,QAEI,MAAO,CAAE,EAGrB,EAEA,EE2HMqF,GFrfoBzN,IAAA,CACxB9e,KAAM,QACN8e,UACA,QAAMpH,CAAGlO,GACD,MAAAqK,EACJA,EAAAC,EACAA,EAAAiJ,UACAA,EAAAkC,MACAA,EACAD,SAAAA,EAAAA,SACAE,EAAA6M,eACAA,GACEviB,GAEErL,QACJA,EAAAkP,QACAA,EAAU,GACRuP,GAASkC,EAAStV,IAAU,CAAE,EAClC,GAAe,MAAXrL,EACF,MAAO,CAAE,EAEL,MAAAqhB,EAAgB7B,GAAiBtQ,GACjCsR,EAAS,CACb9K,IACAC,KAEIqJ,EAAOG,GAAiBP,GACxB9iB,EAASmjB,GAAcD,GACvB4T,QAAwB/R,EAAS+J,cAAc5qB,GAC/CwxB,EAAmB,MAATxS,EACV6T,EAAUrB,EAAU,MAAQ,OAC5BsB,EAAUtB,EAAU,SAAW,QAC/BuB,EAAavB,EAAU,eAAiB,cACxCwB,EAAUlS,EAAMf,UAAUjkB,GAAUglB,EAAMf,UAAUf,GAAQwB,EAAOxB,GAAQ8B,EAAMd,SAASlkB,GAC1Fm3B,EAAYzS,EAAOxB,GAAQ8B,EAAMf,UAAUf,GAC3CkU,QAAuD,MAA5BrS,EAASe,qBAA0B,EAASf,EAASe,gBAAgB5hB,IACtG,IAAImzB,EAAaD,EAAoBA,EAAkBH,GAAc,EAGhEI,SAA6C,MAAtBtS,EAASW,eAAoB,EAASX,EAASW,UAAU0R,MACnFC,EAAapS,EAASf,SAAS+S,IAAejS,EAAMd,SAASlkB,IAEzD,MAAAs3B,EAAoBJ,EAAU,EAAIC,EAAY,EAI9CI,EAAyBF,EAAa,EAAIP,EAAgB92B,GAAU,EAAI,EACxEw3B,EAAalb,GAAIiJ,EAAcwR,GAAUQ,GACzCE,EAAanb,GAAIiJ,EAAcyR,GAAUO,GAIzCG,EAAQF,EACRjb,EAAM8a,EAAaP,EAAgB92B,GAAUy3B,EAC7CE,EAASN,EAAa,EAAIP,EAAgB92B,GAAU,EAAIs3B,EACxD3F,EAASvV,GAAMsb,EAAOC,EAAQpb,GAM9Bqb,GAAmB9F,EAAeQ,OAAoC,MAA3BtP,GAAaF,IAAsB6U,IAAWhG,GAAU3M,EAAMf,UAAUjkB,GAAU,GAAK23B,EAASD,EAAQF,EAAaC,GAAcX,EAAgB92B,GAAU,EAAI,EAC5MuyB,EAAkBqF,EAAkBD,EAASD,EAAQC,EAASD,EAAQC,EAASpb,EAAM,EACpF,MAAA,CACL2G,CAACA,GAAOwB,EAAOxB,GAAQqP,EACvB5D,KAAM,CACJzL,CAACA,GAAOyO,EACRkG,aAAcF,EAAShG,EAASY,KAC5BqF,GAAmB,CACrBrF,oBAGJ2C,MAAO0C,EAEb,IEubME,GFkKa,SAAUjT,GAIpB,YAHS,IAAZA,IACFA,EAAU,CAAE,GAEP,CACLA,UACA,EAAApH,CAAGlO,GACK,MAAAqK,EACJA,EAAAC,EACAA,EAAAiJ,UACAA,EAAAkC,MACAA,EAAA8M,eACAA,GACEviB,GAEFoiB,OAAAA,EAAS,EACTQ,SAAUM,GAAgB,EAC1BL,UAAWM,GAAiB,GAC1B/P,GAASkC,EAAStV,GAChBmV,EAAS,CACb9K,IACAC,KAEIuY,EAAYhP,GAAYN,GACxBqP,EAAWlP,GAAgBmP,GAC7B,IAAAS,EAAgBnO,EAAOyN,GACvBW,EAAiBpO,EAAO0N,GACtB,MAAA2F,EAAYpV,GAASgP,EAAQpiB,GAC7ByoB,EAAsC,iBAAdD,EAAyB,CACrD5F,SAAU4F,EACV3F,UAAW,GACT,CACFD,SAAU,EACVC,UAAW,KACR2F,GAEL,GAAItF,EAAe,CACX,MAAAwF,EAAmB,MAAb9F,EAAmB,SAAW,QACpC+F,EAAWlT,EAAMf,UAAUkO,GAAYnN,EAAMd,SAAS+T,GAAOD,EAAe7F,SAC5EgG,EAAWnT,EAAMf,UAAUkO,GAAYnN,EAAMf,UAAUgU,GAAOD,EAAe7F,SAC/EU,EAAgBqF,EACFrF,EAAAqF,EACPrF,EAAgBsF,IACTtF,EAAAsF,EAE1B,CACM,GAAIzF,EAAgB,CAClB,IAAId,EAAuBwG,EACrB,MAAAH,EAAmB,MAAb9F,EAAmB,QAAU,SACnCkG,EAAe,CAAC,MAAO,QAAQtnB,SAAS8R,GAAQC,IAChDoV,EAAWlT,EAAMf,UAAUmO,GAAapN,EAAMd,SAAS+T,IAAQI,IAAmE,OAAlDzG,EAAwBE,EAAeH,aAAkB,EAASC,EAAsBQ,KAAmB,IAAMiG,EAAe,EAAIL,EAAe5F,WACnO+F,EAAWnT,EAAMf,UAAUmO,GAAapN,EAAMf,UAAUgU,IAAQI,EAAe,GAAyD,OAAnDD,EAAyBtG,EAAeH,aAAkB,EAASyG,EAAuBhG,KAAe,IAAMiG,EAAeL,EAAe5F,UAAY,GAChPU,EAAiBoF,EACFpF,EAAAoF,EACRpF,EAAiBqF,IACTrF,EAAAqF,EAE3B,CACa,MAAA,CACLhG,CAACA,GAAWU,EACZT,CAACA,GAAYU,EAErB,EAEA,EE5NMwF,GAAkB,CAACrU,EAAWC,EAAUW,KAItC,MAAAgJ,MAAYtnB,IACZgyB,EAAgB,CACpBxT,eACGF,GAEC2T,EAAoB,IACrBD,EAAcxT,SACjB/M,GAAI6V,GAEC,MF9qBelJ,OAAOV,EAAWC,EAAUuU,KAC5C,MAAA3V,UACJA,EAAY,SAAAoC,SACZA,EAAW,WAAAwT,WACXA,EAAa,GACb3T,SAAAA,GACE0T,EACEE,EAAkBD,EAAWvzB,OAAOC,SACpC4e,QAA+B,MAAlBe,EAASkK,WAAgB,EAASlK,EAASkK,MAAM/K,IAChE,IAAAc,QAAcD,EAAS2J,gBAAgB,CACzCzK,YACAC,WACAgB,cAEEtL,EACFA,EAAAC,EACAA,GACEiK,GAA2BkB,EAAOlC,EAAWkB,GAC7C4U,EAAoB9V,EACpBgP,EAAiB,CAAE,EACnB+G,EAAa,EACjB,IAAA,IAAS94B,EAAI,EAAGA,EAAI44B,EAAgB34B,OAAQD,IAAK,CACzC,MAAAgG,KACJA,EAAA0X,GACAA,GACEkb,EAAgB54B,IAElB6Z,EAAGkf,EACHjf,EAAGkf,EAAApK,KACHA,EAAAuG,MACAA,SACQzX,EAAG,CACX7D,IACAC,IACAuZ,iBAAkBtQ,EAClBA,UAAW8V,EACX1T,WACA4M,iBACA9M,QACAD,SAAAA,EACAE,SAAU,CACRhB,YACAC,cAGAtK,EAAS,MAATkf,EAAgBA,EAAQlf,EACxBC,EAAS,MAATkf,EAAgBA,EAAQlf,EACXiY,EAAA,IACZA,EACH/rB,CAACA,GAAO,IACH+rB,EAAe/rB,MACf4oB,IAGHuG,GAAS2D,GAAc,KACzBA,IACqB,iBAAV3D,IACLA,EAAMpS,YACR8V,EAAoB1D,EAAMpS,WAExBoS,EAAMlQ,QACRA,GAAwB,IAAhBkQ,EAAMlQ,YAAuBD,EAAS2J,gBAAgB,CAC5DzK,YACAC,WACAgB,aACGgQ,EAAMlQ,SAGXpL,IACAC,KACEiK,GAA2BkB,EAAO4T,EAAmB5U,KAEvDjkB,GAAA,EAEV,CACS,MAAA,CACL6Z,IACAC,IACAiJ,UAAW8V,EACX1T,WACA4M,mBE8lBKkH,CAAkB/U,EAAWC,EAAU,IACzCqU,EACHxT,SAAUyT,KC5uBd,IAAIr3B,GAA4B,oBAAbkJ,SAA2BsE,EAAeA,gBAAGtH,EAASA,UAIzE,SAAS4xB,GAAUn7B,EAAGE,GACpB,GAAIF,IAAME,EACD,OAAA,EAEL,UAAOF,UAAaE,EACf,OAAA,EAEL,GAAa,mBAANF,GAAoBA,EAAE8T,aAAe5T,EAAE4T,WACzC,OAAA,EAEL,IAAA5R,EACAD,EACAm5B,EACJ,GAAIp7B,GAAKE,GAAkB,iBAANF,EAAgB,CAC/B,GAAA6J,MAAMwxB,QAAQr7B,GAAI,CAEhB,GADJkC,EAASlC,EAAEkC,OACPA,IAAWhC,EAAEgC,OAAe,OAAA,EAC3B,IAAAD,EAAIC,EAAgB,IAARD,KACX,IAACk5B,GAAUn7B,EAAEiC,GAAI/B,EAAE+B,IACd,OAAA,EAGJ,OAAA,CACb,CAGI,GAFOm5B,EAAAj8B,OAAOi8B,KAAKp7B,GACnBkC,EAASk5B,EAAKl5B,OACVA,IAAW/C,OAAOi8B,KAAKl7B,GAAGgC,OACrB,OAAA,EAEJ,IAAAD,EAAIC,EAAgB,IAARD,KACX,IAAC,CAAE,EAAC5C,eAAeiB,KAAKJ,EAAGk7B,EAAKn5B,IAC3B,OAAA,EAGN,IAAAA,EAAIC,EAAgB,IAARD,KAAY,CACrB,MAAAvC,EAAM07B,EAAKn5B,GACb,IAAQ,WAARvC,IAAoBM,EAAEQ,YAGrB26B,GAAUn7B,EAAEN,GAAMQ,EAAER,IAChB,OAAA,CAEf,CACW,OAAA,CACX,CACS,OAAAM,GAAMA,GAAKE,GAAMA,CAC1B,CAEA,SAASo7B,GAAOl1B,GACV,GAAkB,oBAAXoE,OACF,OAAA,EAGT,OADYpE,EAAQiG,cAAcyG,aAAetI,QACtC+wB,kBAAoB,CACjC,CAEA,SAASC,GAAWp1B,EAAS3E,GACrB,MAAAg6B,EAAMH,GAAOl1B,GACnB,OAAOmY,KAAK0F,MAAMxiB,EAAQg6B,GAAOA,CACnC,CAEA,SAASC,GAAaj6B,GACd,MAAA9B,EAAMqL,EAAYpC,OAACnH,GAIlB,OAHP4B,IAAM,KACJ1D,EAAIiB,QAAUa,KAET9B,CACT,CAoKA,MAAMg8B,GAAqB5U,IAIlB,CACL9e,KAAM,QACN8e,UACA,EAAApH,CAAGlO,GACK,MAAArL,QACJA,EAAAkP,QACAA,GACqB,mBAAZyR,EAAyBA,EAAQtV,GAASsV,EACjD,OAAA3gB,IAXO3E,EAWU2E,EAVhB,CAAE,EAAC/G,eAAeiB,KAAKmB,EAAO,YAWV,MAAnB2E,EAAQxF,QACHg7B,GAAQ,CACbx1B,QAASA,EAAQxF,QACjB0U,YACCqK,GAAGlO,GAED,CAAE,EAEPrL,EACKw1B,GAAQ,CACbx1B,UACAkP,YACCqK,GAAGlO,GAED,CAAE,EA1Bb,IAAehQ,CA2BjB,IAWMoyB,GAAS,CAAC9M,EAAS8U,KAAU,IAC9BC,GAAS/U,GACZA,QAAS,CAACA,EAAS8U,KAQfnH,GAAQ,CAAC3N,EAAS8U,KAAU,IAC7BE,GAAQhV,GACXA,QAAS,CAACA,EAAS8U,KAMf7B,GAAa,CAACjT,EAAS8U,KAAU,IAClCG,GAAajV,GAChBA,QAAS,CAACA,EAAS8U,KASfzG,GAAO,CAACrO,EAAS8U,KAAU,IAC5BI,GAAOlV,GACVA,QAAS,CAACA,EAAS8U,KASf9uB,GAAO,CAACga,EAAS8U,KAAU,IAC5BK,GAAOnV,GACVA,QAAS,CAACA,EAAS8U,KAmBflD,GAAO,CAAC5R,EAAS8U,KAAU,IAC5BM,GAAOpV,GACVA,QAAS,CAACA,EAAS8U,KAmBfrH,GAAQ,CAACzN,EAAS8U,KAAU,IAC7BF,GAAQ5U,GACXA,QAAS,CAACA,EAAS8U,KCxWrB,IACIO,GAAQr3B,EAAgBiE,YAAC,CAACtI,EAAOsE,KAC7B,MAAAzB,SAAEA,QAAU6R,EAAQ,GAAAC,OAAIA,EAAS,KAAMgnB,GAAe37B,EAC5D,OAA0BiP,EAAA7O,IACxBoJ,EAAUoyB,IACV,IACKD,EACH18B,IAAKqF,EACLoQ,QACAC,SACAknB,QAAS,YACTC,oBAAqB,OACrBj5B,SAAU7C,EAAM2J,QAAU9G,EAA2BzC,EAAGA,IAAC,UAAW,CAAE27B,OAAQ,wBAIpFL,GAAMx4B,YAhBK,QAiBX,IAAIuM,GAAOisB,GClBX,SAASM,GAAQt2B,GACf,MAAO2G,EAAM4vB,GAAWvwB,EAAAA,cAAe,GA+BhCW,OA9BP8D,GAAgB,KACd,GAAIzK,EAAS,CACXu2B,EAAQ,CAAEvnB,MAAOhP,EAAQ4lB,YAAa3W,OAAQjP,EAAQ6lB,eACtD,MAAMoH,EAAiB,IAAI3B,gBAAgBmB,IACzC,IAAKhpB,MAAMwxB,QAAQxI,GACjB,OAEE,IAACA,EAAQ3wB,OACX,OAEI,MAAA06B,EAAQ/J,EAAQ,GAClB,IAAAzd,EACAC,EACJ,GAAI,kBAAmBunB,EAAO,CACtB,MAAAC,EAAkBD,EAAqB,cACvCE,EAAajzB,MAAMwxB,QAAQwB,GAAmBA,EAAgB,GAAKA,EACzEznB,EAAQ0nB,EAAuB,WAC/BznB,EAASynB,EAAsB,SACzC,MACU1nB,EAAQhP,EAAQ4lB,YAChB3W,EAASjP,EAAQ6lB,aAEX0Q,EAAA,CAAEvnB,QAAOC,cAGZ,OADPge,EAAeJ,QAAQ7sB,EAAS,CAAE22B,IAAK,eAChC,IAAM1J,EAAeE,UAAUntB,EAC5C,CACMu2B,OAAQ,KAET,CAACv2B,IACG2G,CACT,CCXA,IAAIiwB,GAAc,UACbC,GAAqBC,IAAqB56B,EAAmB06B,KAC7DG,GAAgBC,IAAoBH,GAAoBD,IACzDK,GAAU38B,IACN,MAAA48B,cAAEA,EAAe/5B,SAAAA,GAAa7C,GAC7B68B,EAAQC,GAAapxB,EAAAA,SAAe,MACpBtL,OAAAA,EAAGA,IAACq8B,GAAgB,CAAEr6B,MAAOw6B,EAAeC,SAAQE,eAAgBD,EAAWj6B,cAExG85B,GAAOz5B,YAAco5B,GACrB,IAAIU,GAAc,eACdC,GAAe54B,EAAgBiE,YACjC,CAACtI,EAAOsE,KACN,MAAMs4B,cAAEA,EAAAM,WAAeA,KAAeC,GAAgBn9B,EAChD8C,EAAU45B,GAAiBM,GAAaJ,GACxC39B,EAAMqL,EAAYpC,OAAC,MACnBK,EAAe9G,EAAgB6C,EAAcrF,GAI5C,OAHPsL,EAAAA,WAAgB,KACdzH,EAAQi6B,gBAAe,MAAAG,OAAA,EAAAA,EAAYh9B,UAAWjB,EAAIiB,YAE7Cg9B,EAAa,KAAuB98B,EAAAA,IAAIoJ,EAAU0F,IAAK,IAAKiuB,EAAal+B,IAAKsJ,OAGzF00B,GAAa/5B,YAAc85B,GAC3B,IAAII,GAAe,iBACdC,GAAuBC,IAAqBf,GAAoBa,IACjEG,GAAgBl5B,EAAgBiE,YAClC,CAACtI,EAAOsE,qBACA,MAAAs4B,cACJA,EAAA3X,KACAA,EAAO,SAAAuY,WACPA,EAAa,EAAAC,MACbA,EAAQ,SAAAC,YACRA,EAAc,EAAAC,aACdA,EAAe,EAAAC,gBACfA,GAAkB,EAAAC,kBAClBA,EAAoB,GACpBC,iBAAkBC,EAAuB,EAAAC,OACzCA,EAAS,UAAAC,iBACTA,GAAmB,EAAAC,uBACnBA,EAAyB,YAAAC,SACzBA,KACGC,GACDp+B,EACE8C,EAAU45B,GAAiBU,GAAcR,IACxCyB,EAASC,GAAc5yB,EAAAA,SAAe,MACvCnD,EAAe9G,EAAgB6C,GAAepD,GAASo9B,EAAWp9B,MACjE4yB,EAAOyK,GAAY7yB,EAAAA,SAAe,MACnC8yB,EAAYxC,GAAQlI,GACpB2K,SAAaD,WAAW9pB,QAAS,EACjCgqB,SAAcF,WAAW7pB,SAAU,EACnCgqB,EAAmB1Z,GAAkB,WAAVwY,EAAqB,IAAMA,EAAQ,IAC9DK,EAAmD,iBAAzBC,EAAoCA,EAAuB,CAAEha,IAAK,EAAGF,MAAO,EAAGC,OAAQ,EAAGF,KAAM,KAAMma,GAChIpX,EAAWxd,MAAMwxB,QAAQkD,GAAqBA,EAAoB,CAACA,GACnEe,EAAwBjY,EAASnlB,OAAS,EAC1C4yB,EAAwB,CAC5Bxf,QAASkpB,EACTnX,SAAUA,EAAShgB,OAAOk4B,IAE1B/X,YAAa8X,IAET39B,KAAEA,EAAM69B,eAAAA,EAAAxa,UAAgBA,eAAWya,EAAczL,eAAAA,GHF3D,SAAqBjN,QACH,IAAZA,IACFA,EAAU,CAAE,GAER,MAAA/B,UACJA,EAAY,SAAAoC,SACZA,EAAW,WAAAwT,WACXA,EAAa,GACb3T,SAAAA,EACAE,UACEhB,UAAWuZ,EACXtZ,SAAUuZ,GACR,CAAE,EAAAC,UACNA,GAAY,EAAAC,qBACZA,EAAA7kB,KACAA,GACE+L,GACG8J,EAAMiP,GAAW1zB,WAAe,CACrC0P,EAAG,EACHC,EAAG,EACHqL,WACApC,YACAgP,eAAgB,CAAE,EAClByL,cAAc,KAETM,EAAkBC,GAAuB5zB,EAAAA,SAAewuB,GAC1DO,GAAU4E,EAAkBnF,IAC/BoF,EAAoBpF,GAEtB,MAAOqF,EAAYC,GAAiB9zB,EAAAA,SAAe,OAC5C+zB,EAAWC,GAAgBh0B,EAAAA,SAAe,MAC3Ci0B,EAAej+B,EAAiBC,aAAST,IACzCA,IAAS0+B,EAAa1/B,UACxB0/B,EAAa1/B,QAAUgB,EACvBs+B,EAAct+B,MAEf,IACG2+B,EAAcn+B,EAAiBC,aAAST,IACxCA,IAAS4+B,EAAY5/B,UACvB4/B,EAAY5/B,QAAUgB,EACtBw+B,EAAax+B,MAEd,IACGkwB,EAAc4N,GAAqBO,EACnCQ,EAAad,GAAoBQ,EACjCG,EAAet1B,EAAYpC,OAAC,MAC5B43B,EAAcx1B,EAAYpC,OAAC,MAC3B83B,EAAU11B,EAAYpC,OAACioB,GACvB8P,EAAkD,MAAxBd,EAC1Be,EAA0BlF,GAAamE,GACvCgB,EAAcnF,GAAazU,GAC3B6Z,EAAUpF,GAAa1gB,GACvBsW,EAASlvB,EAAAA,aAAkB,KAC/B,IAAKk+B,EAAa1/B,UAAY4/B,EAAY5/B,QACxC,OAEF,MAAM+5B,EAAS,CACb3V,YACAoC,WACAwT,WAAYmF,GAEVc,EAAYjgC,UACd+5B,EAAO1T,SAAW4Z,EAAYjgC,SAEhB45B,GAAA8F,EAAa1/B,QAAS4/B,EAAY5/B,QAAS+5B,GAAQoG,MAAKlQ,IACtE,MAAMmQ,EAAW,IACZnQ,EAKH4O,cAAkC,IAApBqB,EAAQlgC,SAEpBqgC,EAAargC,UAAYu6B,GAAUuF,EAAQ9/B,QAASogC,KACtDN,EAAQ9/B,QAAUogC,EAClBr2B,EAAAA,WAAmB,KACjBm1B,EAAQkB,YAIb,CAACjB,EAAkB/a,EAAWoC,EAAUyZ,EAAaC,IACxDz9B,IAAM,MACS,IAAT2X,GAAkB0lB,EAAQ9/B,QAAQ6+B,eACpCiB,EAAQ9/B,QAAQ6+B,cAAe,EAC/BK,GAAQjP,IAAS,IACZA,EACH4O,cAAc,SAGjB,CAACzkB,IACE,MAAAimB,EAAej2B,EAAYpC,QAAC,GAClCvF,IAAM,KACJ49B,EAAargC,SAAU,EAChB,KACLqgC,EAAargC,SAAU,KAExB,IACHyC,IAAM,KAGJ,GAFIyuB,MAA0BlxB,QAAUkxB,GACpC2O,MAAwB7/B,QAAU6/B,GAClC3O,GAAe2O,EAAY,CAC7B,GAAIG,EAAwBhgC,QAC1B,OAAOggC,EAAwBhgC,QAAQkxB,EAAa2O,EAAYnP,GAE1DA,GACd,IACK,CAACQ,EAAa2O,EAAYnP,EAAQsP,EAAyBD,IACxD,MAAAh/B,EAAOqB,EAAAA,SAAc,KAAO,CAChCmjB,UAAWma,EACXla,SAAUoa,EACVH,eACAE,iBACE,CAACF,EAAcE,IACbpZ,EAAWnkB,EAAAA,SAAc,KAAO,CACpCmjB,UAAW2L,EACX1L,SAAUqa,KACR,CAAC3O,EAAa2O,IACZjB,EAAiBx8B,EAAAA,SAAc,KACnC,MAAMk+B,EAAgB,CACpBhsB,SAAUkS,EACV9C,KAAM,EACNG,IAAK,GAEH,IAAC0C,EAASf,SACL,OAAA8a,EAET,MAAMplB,EAAI0f,GAAWrU,EAASf,SAAUyK,EAAK/U,GACvCC,EAAIyf,GAAWrU,EAASf,SAAUyK,EAAK9U,GAC7C,OAAI6jB,EACK,IACFsB,EACHtB,UAAW,aAAe9jB,EAAI,OAASC,EAAI,SACvCuf,GAAOnU,EAASf,WAAa,KAAO,CACtC6D,WAAY,cAIX,CACL/U,SAAUkS,EACV9C,KAAMxI,EACN2I,IAAK1I,KAEN,CAACqL,EAAUwY,EAAWzY,EAASf,SAAUyK,EAAK/U,EAAG+U,EAAK9U,IAClD/Y,OAAAA,EAAaC,SAAC,KAAO,IACvB4tB,EACHS,SACA3vB,OACAwlB,WACAqY,oBACE,CAAC3O,EAAMS,EAAQ3vB,EAAMwlB,EAAUqY,GACrC,CGpJ8E2B,CAAY,CAEpF/Z,SAAU,QACVpC,UAAWqa,EACXQ,qBAAsB,IAAI14B,IACRkqB,MAAclqB,EAAM,CAClC0qB,eAA2C,WAA3B+M,IAIpBzX,SAAU,CACRhB,UAAW3iB,EAAQ+5B,QAErB3C,WAAY,CACV/G,GAAO,CAAEQ,SAAU6J,EAAakB,EAAa9Y,cAAe8X,IAC5DE,GAAmB5J,GAAM,CACvBL,UAAU,EACVC,WAAW,EACXO,QAAoB,YAAX6J,EAAuB1E,UAAe,KAC5ClF,IAELwJ,GAAmBlJ,GAAK,IAAKN,IAC7B/nB,GAAK,IACA+nB,EACH6C,MAAO,EAAGxQ,WAAUD,QAAOmR,iBAAgBD,sBACzC,MAAQhjB,MAAOgsB,EAAa/rB,OAAQgsB,GAAiBna,EAAMf,UACrDmb,EAAena,EAASf,SAAS9W,MACvCgyB,EAAarlB,YAAY,iCAAkC,GAAGoc,OAC9DiJ,EAAarlB,YAAY,kCAAmC,GAAGmc,OAC/DkJ,EAAarlB,YAAY,8BAA+B,GAAGmlB,OAC3DE,EAAarlB,YAAY,+BAAgC,GAAGolB,UAGhE7M,GAAS+M,GAAgB,CAAEn7B,QAASouB,EAAOlf,QAAS+oB,IACpDmD,GAAgB,CAAErC,aAAYC,gBAC9BT,GAAoBhG,GAAK,CAAEvR,SAAU,qBAAsB0N,QAGxD2M,EAAYC,GAAeC,GAA6B3c,GACzD4c,EAAe/2B,EAAeg0B,GACpChuB,GAAgB,KACV4uB,IACF,MAAAmC,GAAAA,OAED,CAACnC,EAAcmC,IACZ,MAAAC,EAAS,OAAAn+B,EAAeswB,EAAAQ,YAAO,EAAA9wB,EAAAoY,EAC/BgmB,EAAS,OAAAp7B,EAAestB,EAAAQ,YAAO,EAAA9tB,EAAAqV,EAC/BgmB,EAA2D,KAAvC,OAAA7nB,EAAA8Z,EAAeQ,YAAf,EAAAta,EAAsB6f,eACzCiI,GAAeC,IAAoB71B,aAI1C,OAHAyE,GAAgB,KACVkuB,GAA0BkD,GAAAz3B,OAAOgJ,iBAAiBurB,GAASmD,UAC9D,CAACnD,IACsBpvB,EAAA7O,IACxB,MACA,CACEnB,IAAKgC,EAAK4+B,YACV,oCAAqC,GACrCjxB,MAAO,IACFkwB,EACHI,UAAWH,EAAeD,EAAeI,UAAY,sBAErDuC,SAAU,cACVD,OAAQF,GACR,kCAAqC,CACnC,OAAAI,EAAApO,EAAewN,sBAAiB,EAAAY,EAAAtmB,EAChC,OAAAkX,EAAAgB,EAAewN,sBAAiB,EAAAxO,EAAAjX,GAChCxU,KAAK,SAIJ,OAAA86B,EAAArO,EAAe2E,WAAf,EAAA0J,EAAqBxJ,kBAAmB,CACzCyJ,WAAY,SACZ/yB,cAAe,SAGnBgzB,IAAK7hC,EAAM6hC,IACXh/B,SAA6BoM,EAAA7O,IAC3Bi9B,GACA,CACEj7B,MAAOw6B,EACPmE,aACAe,cAAevD,EACf4C,SACAC,SACAW,gBAAiBV,EACjBx+B,SAA6BoM,EAAA7O,IAC3BoJ,EAAU0F,IACV,CACE,YAAa6xB,EACb,aAAcC,KACX5C,EACHn/B,IAAKsJ,EACLqG,MAAO,IACFwvB,EAAaxvB,MAGhBozB,UAAYjD,OAAwB,EAAT,iBAU7CxB,GAAcr6B,YAAck6B,GAC5B,IAAI6E,GAAa,cACbC,GAAgB,CAClBne,IAAK,SACLF,MAAO,OACPC,OAAQ,MACRF,KAAM,SAEJue,GAAc99B,EAAAA,YAAiB,SAAsBrE,EAAOsE,GAC9D,MAAMs4B,cAAEA,KAAkBjB,GAAe37B,EACnCoiC,EAAiB9E,GAAkB2E,GAAYrF,GAC/CyF,EAAWH,GAAcE,EAAerB,YAC9C,OAIqB9xB,EAAA7O,IACjB,OACA,CACEnB,IAAKmjC,EAAeN,cACpBlzB,MAAO,CACL4F,SAAU,WACVoP,KAAMwe,EAAejB,OACrBpd,IAAKqe,EAAehB,OACpBiB,CAACA,GAAW,EACZvB,gBAAiB,CACf/c,IAAK,GACLF,MAAO,MACPC,OAAQ,WACRF,KAAM,UACNwe,EAAerB,YACjB7B,UAAW,CACTnb,IAAK,mBACLF,MAAO,iDACPC,OAAQ,iBACRF,KAAM,kDACNwe,EAAerB,YACjBa,WAAYQ,EAAeL,gBAAkB,cAAW,GAE1Dl/B,SAA6BoM,EAAA7O,IAC3BkiC,GACA,IACK3G,EACH18B,IAAKqF,EACLsK,MAAO,IACF+sB,EAAW/sB,MAEdqD,QAAS,YAOvB,IAEA,SAAS4sB,GAAU99B,GACjB,OAAiB,OAAVA,CACT,CAHAohC,GAAYj/B,YAAc++B,GAI1B,IAAInB,GAAmBza,IAAa,CAClC9e,KAAM,kBACN8e,UACA,EAAApH,CAAGkR,aACD,MAAM7L,UAAEA,EAAAkC,MAAWA,EAAO8M,eAAAA,GAAmBnD,EAEvCoS,EAD2D,KAAvC,OAAAv/B,EAAAswB,EAAeQ,YAAf,EAAA9wB,EAAsBq2B,cAE1CoF,EAAa8D,EAAgB,EAAIlc,EAAQoY,WACzCC,EAAc6D,EAAgB,EAAIlc,EAAQqY,aACzCqC,EAAYC,GAAeC,GAA6B3c,GACzDke,EAAe,CAAEve,MAAO,KAAMkV,OAAQ,MAAOjV,IAAK,QAAS8c,GAC3DyB,IAAgB,OAAAz8B,EAAestB,EAAAQ,YAAO,EAAA9tB,EAAAoV,IAAK,GAAKqjB,EAAa,EAC7DiE,IAAgB,OAAAlpB,EAAe8Z,EAAAQ,YAAO,EAAAta,EAAA6B,IAAK,GAAKqjB,EAAc,EACpE,IAAItjB,EAAI,GACJC,EAAI,GAcR,MAbmB,WAAf0lB,GACE3lB,EAAAmnB,EAAgBC,EAAe,GAAGC,MAClCpnB,GAAIqjB,EAAJ,MACoB,QAAfqC,GACL3lB,EAAAmnB,EAAgBC,EAAe,GAAGC,MACtCpnB,EAAI,GAAGmL,EAAMd,SAAS/Q,OAAS+pB,OACP,UAAfqC,GACL3lB,GAAIsjB,EAAJ,KACArjB,EAAAknB,EAAgBC,EAAe,GAAGE,OACd,SAAf3B,IACT3lB,EAAI,GAAGoL,EAAMd,SAAShR,MAAQgqB,MAC1BrjB,EAAAknB,EAAgBC,EAAe,GAAGE,OAEjC,CAAEvS,KAAM,CAAE/U,IAAGC,KACxB,IAEA,SAAS4lB,GAA6B3c,GACpC,MAAOW,EAAMwY,EAAQ,UAAYnZ,EAAUC,MAAM,KAC1C,MAAA,CAACU,EAAMwY,EAChB,CACG,IAAC/a,GAAQia,GACRgG,GAAS1F,GACT2F,GAAUrF,GACV7B,GAAQyG,GC/RZ,SAASvkB,GAAM7c,GAAQ+c,EAAKC,IAC1B,OAAOF,KAAKC,IAAIC,EAAKF,KAAKE,IAAID,EAAK/c,GACrC,CCAA,IAAI8hC,GAAmB1gC,EAAmBO,mBAAC,GAK3C,SAASogC,GAAaC,GACd,MAAAC,EAAY5/B,EAAgBC,WAACw/B,IACnC,OAAOE,GAAYC,GAAa,KAClC,CCPA,IAAI99B,GAAQ,EAKZ,SAAS+9B,KACP14B,EAAAA,WAAgB,KACR,MAAA24B,EAAar3B,SAASxC,iBAAiB,4BAI7C,OAHAwC,SAAS8C,KAAKw0B,sBAAsB,aAAcD,EAAW,IAAME,MACnEv3B,SAAS8C,KAAKw0B,sBAAsB,YAAaD,EAAW,IAAME,MAClEl+B,KACO,KACS,IAAVA,IACO2G,SAAAxC,iBAAiB,4BAA4BmX,SAAStf,GAASA,EAAKmiC,WAE/En+B,QAED,GACL,CACA,SAASk+B,KACD,MAAA19B,EAAUmG,SAASy3B,cAAc,QAOhC,OANC59B,EAAAwV,aAAa,yBAA0B,IAC/CxV,EAAQiU,SAAW,EACnBjU,EAAQkJ,MAAM20B,QAAU,OACxB79B,EAAQkJ,MAAM40B,QAAU,IACxB99B,EAAQkJ,MAAM4F,SAAW,QACzB9O,EAAQkJ,MAAMC,cAAgB,OACvBnJ,CACT,CCxBA,IAAI+9B,GAAqB,8BACrBC,GAAuB,gCACvBC,GAAgB,CAAEp0B,SAAS,EAAOC,YAAY,GAE9Co0B,GAAav/B,EAAgBiE,YAAC,CAACtI,EAAOsE,KAClC,MAAAu/B,KACJA,GAAO,EAAAC,QACPA,GAAU,EACVC,iBAAkBC,EAClBC,mBAAoBC,KACjBC,GACDnkC,GACG8P,EAAWs0B,GAAgB14B,EAAAA,SAAe,MAC3Cq4B,EAAmB55B,EAAe65B,GAClCC,EAAqB95B,EAAe+5B,GACpCG,EAAwB/5B,EAAYpC,OAAC,MACrCK,EAAe9G,EAAgB6C,GAAepD,GAASkjC,EAAaljC,KACpEojC,EAAah6B,EAAAA,OAAa,CAC9Bi6B,QAAQ,EACR,KAAAC,GACE1U,KAAKyU,QAAS,CACf,EACD,MAAAE,GACE3U,KAAKyU,QAAS,CACpB,IACKrkC,QACHqK,EAAAA,WAAgB,KACd,GAAIu5B,EAAS,CACP,IAAAY,EAAiB,SAAS9jC,GACxB,GAAA0jC,EAAWC,SAAWz0B,EAAW,OACrC,MAAM9F,EAASpJ,EAAMoJ,OACjB8F,EAAUjC,SAAS7D,GACrBq6B,EAAsBnkC,QAAU8J,EAEhC0N,GAAM2sB,EAAsBnkC,QAAS,CAAEykC,QAAQ,GAEzD,EAASC,EAAkB,SAAShkC,GACxB,GAAA0jC,EAAWC,SAAWz0B,EAAW,OACrC,MAAMmI,EAAgBrX,EAAMqX,cACN,OAAlBA,IACCnI,EAAUjC,SAASoK,IACtBP,GAAM2sB,EAAsBnkC,QAAS,CAAEykC,QAAQ,IAEzD,EAASE,EAAmB,SAASC,GAEzB,GADmBj5B,SAASsM,gBACTtM,SAAS8C,KAChC,IAAA,MAAWo2B,KAAYD,EACjBC,EAASC,aAAaxjC,OAAS,MAASsO,EAE/C,EAEQjE,SAAAsB,iBAAiB,UAAWu3B,GAC5B74B,SAAAsB,iBAAiB,WAAYy3B,GAChC,MAAAK,EAAmB,IAAIC,iBAAiBL,GAE9C,OADI/0B,KAA4ByiB,QAAQziB,EAAW,CAAEq1B,WAAW,EAAMC,SAAS,IACxE,KACIv5B,SAAAqB,oBAAoB,UAAWw3B,GAC/B74B,SAAAqB,oBAAoB,WAAY03B,GACzCK,EAAiBrT,aAEzB,IACK,CAACkS,EAASh0B,EAAWw0B,EAAWC,SACnCh6B,EAAAA,WAAgB,KACd,GAAIuF,EAAW,CACbu1B,GAAiBv2B,IAAIw1B,GACrB,MAAM9hB,EAA2B3W,SAASsM,cAE1C,IAD4BrI,EAAUjC,SAAS2U,GACrB,CACxB,MAAM8iB,EAAa,IAAIl2B,YAAYq0B,GAAoBE,IAC7C7zB,EAAA3C,iBAAiBs2B,GAAoBM,GAC/Cj0B,EAAU5F,cAAco7B,GACnBA,EAAWzkC,oBAkDxB,SAAoB0hB,GAAYoiB,OAAEA,GAAS,GAAU,CAAA,GACnD,MAAMniB,EAA2B3W,SAASsM,cAC1C,IAAA,MAAWmB,KAAaiJ,EAElB,GADE7K,GAAA4B,EAAW,CAAEqrB,WACf94B,SAASsM,gBAAkBqK,EAA0B,MAE7D,CAvDqBjJ,EA6HAgsB,EA7HY7sB,GAAsB5I,GA8H9Cy1B,EAAM5+B,QAAQ6+B,GAA0B,MAAjBA,EAAKvjB,WA9H+B,CAAE0iB,QAAQ,IAChE94B,SAASsM,gBAAkBqK,GAC7B9K,GAAM5H,GAGlB,CACM,MAAO,KACKA,EAAA5C,oBAAoBu2B,GAAoBM,GAClDz2B,YAAW,KACT,MAAMm4B,EAAe,IAAIr2B,YAAYs0B,GAAsBC,IACjD7zB,EAAA3C,iBAAiBu2B,GAAsBO,GACjDn0B,EAAU5F,cAAcu7B,GACnBA,EAAa5kC,kBAChB6W,GAAM8K,GAA4B3W,SAAS8C,KAAM,CAAEg2B,QAAQ,IAEnD70B,EAAA5C,oBAAoBw2B,GAAsBO,GACpDoB,GAAiBhC,OAAOiB,KACvB,GAEX,CA0GA,IAAqBiB,IAzGhB,CAACz1B,EAAWi0B,EAAkBE,EAAoBK,IACrD,MAAM/1B,EAAgB7M,EAAiBC,aACpCf,IACK,IAACijC,IAASC,EAAS,OACvB,GAAIQ,EAAWC,OAAQ,OACjB,MAAAmB,EAAyB,QAAd9kC,EAAM5B,MAAkB4B,EAAMkY,SAAWlY,EAAMmY,UAAYnY,EAAMoY,QAC5EC,EAAiBpN,SAASsM,cAChC,GAAIutB,GAAYzsB,EAAgB,CAC9B,MAAM0sB,EAAa/kC,EAAMqa,eAClB2qB,EAAOC,GA2BtB,SAA0B/1B,GAClB,MAAAyS,EAAa7J,GAAsB5I,GACnC81B,EAAQE,GAAYvjB,EAAYzS,GAChC+1B,EAAOC,GAAYvjB,EAAW5J,UAAW7I,GACxC,MAAA,CAAC81B,EAAOC,EACjB,CAhC8BE,CAAiBJ,GACLC,GAASC,EAIpCjlC,EAAMuY,UAAYF,IAAmB4sB,EAG/BjlC,EAAMuY,UAAYF,IAAmB2sB,IAC9ChlC,EAAM8N,iBACFm1B,GAAYnsB,GAAAmuB,EAAM,CAAElB,QAAQ,MAJhC/jC,EAAM8N,iBACFm1B,GAAYnsB,GAAAkuB,EAAO,CAAEjB,QAAQ,KAJ/B1rB,IAAmB0sB,GAAY/kC,EAAM8N,gBAUnD,IAEI,CAACm1B,EAAMC,EAASQ,EAAWC,SAE7B,aAA2B/6B,EAAU0F,IAAK,CAAEyK,UAAc,KAAGwqB,EAAYllC,IAAKsJ,EAAc4U,UAAW5O,OAgBzG,SAASmK,GAAsB5I,GAC7B,MAAM4R,EAAQ,GACRC,EAAS9V,SAAS+V,iBAAiB9R,EAAW+R,WAAWC,aAAc,CAC3EC,WAAa7gB,IACX,MAAM8gB,EAAiC,UAAjB9gB,EAAK+gB,SAAqC,WAAd/gB,EAAKnB,KACvD,OAAImB,EAAKghB,UAAYhhB,EAAK8f,QAAUgB,EAAsBH,WAAWM,YAC9DjhB,EAAKyY,UAAY,EAAIkI,WAAWO,cAAgBP,WAAWM,eAGtE,KAAOR,EAAOU,YAAkBX,EAAAd,KAAKe,EAAOW,aACrC,OAAAZ,CACT,CACA,SAASokB,GAAYrf,EAAU3W,GAC7B,IAAA,MAAWpK,KAAW+gB,EAChB,IAAC3F,GAASpb,EAAS,CAAEsgC,KAAMl2B,IAAqB,OAAApK,CAExD,CACA,SAASob,GAAS5f,GAAM8kC,KAAEA,IACxB,GAA0C,WAAtClzB,iBAAiB5R,GAAM0gC,WAAgC,OAAA,EAC3D,KAAO1gC,GAAM,CACX,QAAa,IAAT8kC,GAAmB9kC,IAAS8kC,EAAa,OAAA,EAC7C,GAAuC,SAAnClzB,iBAAiB5R,GAAM+Q,QAA2B,OAAA,EACtD/Q,EAAOA,EAAK+kC,aAChB,CACS,OAAA,CACT,CAIA,SAASvuB,GAAMhS,GAASi/B,OAAEA,GAAS,GAAU,CAAA,GACvC,GAAAj/B,GAAWA,EAAQgS,MAAO,CAC5B,MAAM8K,EAA2B3W,SAASsM,cAC1CzS,EAAQgS,MAAM,CAAEwuB,eAAe,IAC3BxgC,IAAY8c,GAPpB,SAA2B9c,GAClB,OAAAA,aAAmBygC,kBAAoB,WAAYzgC,CAC5D,CAKgD0gC,CAAkB1gC,IAAYi/B,GACxEj/B,EAAQi/B,QACd,CACA,CAlDAf,GAAW1gC,YArHY,aAwKvB,IAAImiC,GACJ,WACE,IAAIgB,EAAQ,GACL,MAAA,CACL,GAAAv3B,CAAIw1B,GACI,MAAAgC,EAAmBD,EAAM,GAC3B/B,IAAegC,IACC,MAAAA,GAAAA,EAAA9B,SAEZ6B,EAAAE,GAAYF,EAAO/B,GAC3B+B,EAAMG,QAAQlC,EACf,EACD,MAAAjB,CAAOiB,SACG+B,EAAAE,GAAYF,EAAO/B,GAC3B,OAAMthC,EAAAqjC,EAAA,KAAIrjC,EAAAyhC,QAChB,EAEA,CAjBuBgC,GAkBvB,SAASF,GAAYG,EAAOlB,GACpB,MAAAmB,EAAe,IAAID,GACnB/jC,EAAQgkC,EAAap9B,QAAQi8B,GAI5B,OAHW,IAAd7iC,GACWgkC,EAAAC,OAAOjkC,EAAO,GAEtBgkC,CACT,CC1MA,SAASE,GAAY9lC,GACnB,MAAM9B,EAAMqL,EAAAA,OAAa,CAAEvJ,QAAO+lC,SAAU/lC,IACrCuB,OAAAA,EAAaC,SAAC,KACftD,EAAIiB,QAAQa,QAAUA,IACpB9B,EAAAiB,QAAQ4mC,SAAW7nC,EAAIiB,QAAQa,MACnC9B,EAAIiB,QAAQa,MAAQA,GAEf9B,EAAIiB,QAAQ4mC,WAClB,CAAC/lC,GACN,CCXA,IAOIgmC,OAAiBC,QACjBC,OAAwBD,QACxBE,GAAY,CAAE,EACdC,GAAY,EACZC,GAAa,SAAUlmC,GACvB,OAAOA,IAASA,EAAKkpB,MAAQgd,GAAWlmC,EAAKipB,YACjD,EAwBIkd,GAAyB,SAAUC,EAAgBnd,EAAYod,EAAYC,GACvE,IAAAC,EAxBa,SAAUxc,EAAQwc,GAC5B,OAAAA,EACFpmC,KAAI,SAAU2I,GACX,GAAAihB,EAAOpd,SAAS7D,GACT,OAAAA,EAEP,IAAA09B,EAAkBN,GAAWp9B,GACjC,OAAI09B,GAAmBzc,EAAOpd,SAAS65B,GAC5BA,EAGJ,IACV,IACI/gC,QAAO,SAAUyU,GAAK,OAAOxU,QAAQwU,KAC9C,CAUkBusB,CAAexd,EAAYhhB,MAAMwxB,QAAQ2M,GAAkBA,EAAiB,CAACA,IACtFJ,GAAUK,KACDL,GAAAK,GAAc,IAAIP,SAE5B,IAAAY,EAAgBV,GAAUK,GAC1BM,EAAc,GACdC,MAAqBh9B,IACrBi9B,EAAiB,IAAIj9B,IAAI28B,GACzBO,EAAO,SAAUzY,GACZA,IAAMuY,EAAeG,IAAI1Y,KAG9BuY,EAAeh5B,IAAIygB,GACnByY,EAAKzY,EAAGpF,YACX,EACDsd,EAAQjnB,QAAQwnB,GACZ,IAAAE,EAAO,SAAUjd,GACZA,IAAU8c,EAAeE,IAAIhd,IAGlC9hB,MAAMzK,UAAU8hB,QAAQ5gB,KAAKqrB,EAAOpoB,UAAU,SAAU3B,GAChD,GAAA4mC,EAAeG,IAAI/mC,GACnBgnC,EAAKhnC,QAGD,IACI,IAAAinC,EAAOjnC,EAAKknC,aAAaZ,GACzBa,EAAyB,OAATF,GAA0B,UAATA,EACjCG,GAAgBvB,GAAWlhC,IAAI3E,IAAS,GAAK,EAC7CqnC,GAAeX,EAAc/hC,IAAI3E,IAAS,GAAK,EACxC6lC,GAAAj+B,IAAI5H,EAAMonC,GACPV,EAAA9+B,IAAI5H,EAAMqnC,GACxBV,EAAYjnB,KAAK1f,GACI,IAAjBonC,GAAsBD,GACJpB,GAAAn+B,IAAI5H,GAAM,GAEZ,IAAhBqnC,GACKrnC,EAAAga,aAAaqsB,EAAY,QAE7Bc,GACInnC,EAAAga,aAAassB,EAAkB,OAE5D,OACuB9nC,GAEvB,CAEA,GACK,EAID,OAHAwoC,EAAK/d,GACL2d,EAAeU,QACfrB,KACO,WACSU,EAAArnB,SAAQ,SAAUtf,GAC1B,IAAIonC,EAAevB,GAAWlhC,IAAI3E,GAAQ,EACtCqnC,EAAcX,EAAc/hC,IAAI3E,GAAQ,EACjC6lC,GAAAj+B,IAAI5H,EAAMonC,GACPV,EAAA9+B,IAAI5H,EAAMqnC,GACnBD,IACIrB,GAAkBgB,IAAI/mC,IACvBA,EAAKunC,gBAAgBjB,GAEzBP,GAAkBl+B,OAAO7H,IAExBqnC,GACDrnC,EAAKunC,gBAAgBlB,EAErC,MACQJ,KAGIJ,OAAiBC,QACjBD,OAAiBC,QACjBC,OAAwBD,QACxBE,GAAY,CAAE,EAErB,CACL,EAQWwB,GAAa,SAAUpB,EAAgBnd,EAAYod,QACvC,IAAfA,IAAsCA,EAAA,oBACtC,IAAAE,EAAUt+B,MAAMC,KAAKD,MAAMwxB,QAAQ2M,GAAkBA,EAAiB,CAACA,IACvEqB,EA9He,SAAUrB,GACzB,MAAoB,oBAAbz7B,SACA,MAEQ1C,MAAMwxB,QAAQ2M,GAAkBA,EAAe,GAAKA,GACnD37B,cAAcgD,IACtC,CAwHyCi6B,CAAiBtB,GACtD,OAAKqB,GAKGlB,EAAA7mB,KAAKqW,MAAMwQ,EAASt+B,MAAMC,KAAKu/B,EAAiBt/B,iBAAiB,yBAClEg+B,GAAuBI,EAASkB,EAAkBpB,EAAY,gBAL1D,WAAqB,OAAA,IAAO,CAM3C,ECvGWsB,GAAW,WAQb,OAPPA,GAAWpqC,OAAOqqC,QAAU,SAAkBC,GACjC,IAAA,IAAAC,EAAGznC,EAAI,EAAG3C,EAAIqqC,UAAUznC,OAAQD,EAAI3C,EAAG2C,IAE5C,IAAA,IAASxC,KADTiqC,EAAIC,UAAU1nC,GACO9C,OAAOC,UAAUC,eAAeiB,KAAKopC,EAAGjqC,KAAIgqC,EAAEhqC,GAAKiqC,EAAEjqC,IAEvE,OAAAgqC,CACb,EACSF,GAAS5R,MAAMnH,KAAMmZ,UAC9B,EAEO,SAASC,GAAOF,EAAGtpC,GACxB,IAAIqpC,EAAI,CAAE,EACV,IAAA,IAAShqC,KAAKiqC,EAAOvqC,OAAOC,UAAUC,eAAeiB,KAAKopC,EAAGjqC,IAAMW,EAAE6J,QAAQxK,GAAK,IAC5EA,EAAAA,GAAKiqC,EAAEjqC,IACb,GAAS,MAALiqC,GAAqD,mBAAjCvqC,OAAO0qC,sBAClB,KAAA5nC,EAAI,EAAJ,IAAOxC,EAAIN,OAAO0qC,sBAAsBH,GAAIznC,EAAIxC,EAAEyC,OAAQD,IAC3D7B,EAAE6J,QAAQxK,EAAEwC,IAAM,GAAK9C,OAAOC,UAAU0qC,qBAAqBxpC,KAAKopC,EAAGjqC,EAAEwC,MACvEwnC,EAAEhqC,EAAEwC,IAAMynC,EAAEjqC,EAAEwC,IAF4B,CAI/C,OAAAwnC,CACT,CAmRkD,mBAApBM,iBAAiCA,gBCvUxD,IAAIC,GAAqB,4BACrBC,GAAqB,0BCYzB,SAASC,GAAUvqC,EAAK8B,GAOpB,MANY,mBAAR9B,EACPA,EAAI8B,GAEC9B,IACLA,EAAIiB,QAAUa,GAEX9B,CACX,CClBA,IAAIwqC,GAA8C,oBAAX3/B,OAAyB8F,EAAqBO,gBAAG5F,EAAe1B,UACnG6gC,OAAoB1C,QAejB,SAAS2C,GAAa1oC,EAAM2oC,GAC/B,ICL2BC,EAAcz/B,EACrCnL,EDIAoL,GCLuBw/B,EDKsB,KCLRz/B,EDKc,SAAU0/B,GACtD,OAAA7oC,EAAKuf,SAAQ,SAAUvhB,GAAc,OAAAuqC,GAAUvqC,EAAK6qC,KACnE,GCNQ7qC,EAAM8M,YAAS,WAAsB,MAAA,CAErChL,MAAO8oC,EAEPz/B,WAEA2/B,OAAQ,CACJ,WAAI7pC,GACA,OAAOjB,EAAI8B,KACd,EACD,WAAIb,CAAQa,GACR,IAAI8kC,EAAO5mC,EAAI8B,MACX8kC,IAAS9kC,IACT9B,EAAI8B,MAAQA,EACR9B,EAAAmL,SAASrJ,EAAO8kC,GAE3B,GAEN,IAAI,IAEHz7B,SAAWA,EACRnL,EAAI8qC,QDMJ,OAnBPN,IAA0B,WAClB,IAAAO,EAAWN,GAAc7jC,IAAIwE,GACjC,GAAI2/B,EAAU,CACN,IAAAC,EAAa,IAAIn/B,IAAIk/B,GACrBE,EAAa,IAAIp/B,IAAI7J,GACrBkpC,EAAY9/B,EAAYnK,QACjB+pC,EAAAzpB,SAAQ,SAAUvhB,GACpBirC,EAAWjC,IAAIhpC,IAChBuqC,GAAUvqC,EAAK,KAEnC,IACuBirC,EAAA1pB,SAAQ,SAAUvhB,GACpBgrC,EAAWhC,IAAIhpC,IAChBuqC,GAAUvqC,EAAKkrC,EAEnC,GACA,CACsBT,GAAA5gC,IAAIuB,EAAapJ,EACvC,GAAO,CAACA,IACGoJ,CACX,CE3CA,SAAS+/B,GAAK9qC,GACH,OAAAA,CACX,CCDA,IAAI+qC,GAAU,SAAUrnC,GAChB,IAAAsnC,EAAUtnC,EAAGsnC,QAASC,EAAOrB,GAAOlmC,EAAI,CAAC,YAC7C,IAAKsnC,EACK,MAAA,IAAIhnC,MAAM,sEAEhB,IAAAknC,EAASF,EAAQG,OACrB,IAAKD,EACK,MAAA,IAAIlnC,MAAM,4BAEpB,OAAOonC,EAAmBpH,cAACkH,EAAQ3B,GAAS,CAAE,EAAE0B,GACpD,EACAF,GAAQM,iBAAkB,ECZnB,IAAIC,GFuEJ,SAA6BvkB,QAChB,IAAZA,IAAsBA,EAAU,CAAA,GAChC,IAAAwkB,EAtER,SAA2BC,EAAU5Q,QACd,IAAfA,IAAsCA,EAAAkQ,IAC1C,IAAIW,EAAS,GACTC,GAAW,EA0DR,MAzDM,CACTP,KAAM,WACF,GAAIO,EACM,MAAA,IAAI1nC,MAAM,oGAEpB,OAAIynC,EAAOvpC,OACAupC,EAAOA,EAAOvpC,OAAS,GAE3BspC,CACV,EACDG,UAAW,SAAU9a,GACb,IAAAqV,EAAOtL,EAAW/J,EAAM6a,GAE5B,OADAD,EAAOnqB,KAAK4kB,GACL,WACMuF,EAAAA,EAAOpkC,QAAO,SAAUyU,GAAK,OAAOA,IAAMoqB,IACtD,CACJ,EACD0F,iBAAkB,SAAUC,GAExB,IADWH,GAAA,EACJD,EAAOvpC,QAAQ,CAClB,IAAI4pC,EAAML,EACVA,EAAS,GACTK,EAAI5qB,QAAQ2qB,EAC5B,CACqBJ,EAAA,CACLnqB,KAAM,SAAUxF,GAAK,OAAO+vB,EAAG/vB,EAAK,EACpCzU,OAAQ,WAAqB,OAAAokC,CAAS,EAE7C,EACDM,aAAc,SAAUF,GACTH,GAAA,EACX,IAAIM,EAAe,GACnB,GAAIP,EAAOvpC,OAAQ,CACf,IAAI4pC,EAAML,EACVA,EAAS,GACTK,EAAI5qB,QAAQ2qB,GACGG,EAAAP,CAC/B,CACY,IAAIQ,EAAe,WACf,IAAIH,EAAME,EACVA,EAAe,GACfF,EAAI5qB,QAAQ2qB,EACf,EACGK,EAAQ,WAAc,OAAOC,QAAQC,UAAUrL,KAAKkL,EAAgB,EACjEC,IACET,EAAA,CACLnqB,KAAM,SAAUxF,GACZkwB,EAAa1qB,KAAKxF,GACXowB,GACV,EACD7kC,OAAQ,SAAUA,GAEP,OADQ2kC,EAAAA,EAAa3kC,OAAOA,GAC5BokC,CACV,EAER,EAGT,CAQiBY,CAAkB,MAExB,OADAd,EAAAxkB,QAAUwiB,GAAS,CAAE1iB,OAAO,EAAMylB,KAAK,GAASvlB,GAChDwkB,CACX,CE5EuBgB,GCInBC,GAAU,WAEd,EAIIC,GAAe1nC,EAAgBiE,YAAC,SAAUtI,EAAOgsC,GAC7C,IAAA/sC,EAAMqL,EAAYpC,OAAC,MACnBlF,EAAK0I,EAAAA,SAAe,CACpBugC,gBAAiBH,GACjBI,eAAgBJ,GAChBK,mBAAoBL,KACpBM,EAAYppC,EAAG,GAAIqpC,EAAerpC,EAAG,GACrCspC,EAAetsC,EAAMssC,aAAczpC,EAAW7C,EAAM6C,SAAU0pC,EAAYvsC,EAAMusC,UAAWC,EAAkBxsC,EAAMwsC,gBAAiB/X,EAAUz0B,EAAMy0B,QAASgY,EAASzsC,EAAMysC,OAAQnC,EAAUtqC,EAAMsqC,QAASoC,EAAa1sC,EAAM0sC,WAAYC,EAAc3sC,EAAM2sC,YAAaC,EAAQ5sC,EAAM4sC,MAAOC,EAAiB7sC,EAAM6sC,eAAgB7mC,EAAKhG,EAAM8sC,GAAIC,OAAmB,IAAP/mC,EAAgB,MAAQA,EAAIgnC,EAAUhtC,EAAMgtC,QAASzC,EAAOrB,GAAOlpC,EAAO,CAAC,eAAgB,WAAY,YAAa,kBAAmB,UAAW,SAAU,UAAW,aAAc,cAAe,QAAS,iBAAkB,KAAM,YACzkBqqC,EAAUC,EACV2C,EAAetD,GAAa,CAAC1qC,EAAK+sC,IAClCkB,EAAiBrE,GAASA,GAAS,CAAA,EAAI0B,GAAO6B,GAClD,OAAQ1B,EAAAA,cAAoB3jC,EAAAA,SAAgB,KACxC0tB,GAAYiW,EAAmBpH,cAAC+G,EAAS,CAAEC,QAASM,GAAW4B,kBAAkCC,SAAgBC,aAAwBC,cAA0BC,QAAcP,eAA4BQ,iBAAkBA,EAAgBM,QAASluC,EAAK+tC,YAC7PV,EAAgBhnC,EAAAA,aAAmBb,EAAAA,SAAeU,KAAKtC,GAAWgmC,GAASA,GAAS,CAAE,EAAEqE,GAAiB,CAAEjuC,IAAKguC,KAAqBvC,EAAAA,cAAoBqC,EAAWlE,GAAS,CAAE,EAAEqE,EAAgB,CAAEX,YAAsBttC,IAAKguC,IAAiBpqC,GACvP,IACAkpC,GAAalsC,aAAe,CACxB40B,SAAS,EACT+X,iBAAiB,EACjBI,OAAO,GAEXb,GAAaqB,WAAa,CACtBC,UAAW9D,GACX+D,UAAWhE,IChCf,SAASiE,KACL,IAAK1hC,SACM,OAAA,KACP,IAAA2hC,EAAM3hC,SAASy3B,cAAc,SACjCkK,EAAIztC,KAAO,WACX,IAAI0tC,ECFc,WAId,GAA6B,oBAAtBC,kBACA,OAAAA,iBAGf,CDNgBC,GAIL,OAHHF,GACID,EAAAtyB,aAAa,QAASuyB,GAEvBD,CACX,CAeO,IAAII,GAAsB,WAC7B,IAAIC,EAAU,EACVC,EAAa,KACV,MAAA,CACHh/B,IAAK,SAAUF,GAlBvB,IAAsB4+B,EAAKpkB,EAmBA,GAAXykB,IACKC,EAAaP,QApBPnkB,EAqBkBxa,GArBvB4+B,EAqBWM,GAnBrBC,WAEJP,EAAIO,WAAWC,QAAU5kB,EAGzBokB,EAAIS,YAAYpiC,SAASqiC,eAAe9kB,IAGhD,SAAwBokB,IACT3hC,SAASsiC,MAAQtiC,SAASuiC,qBAAqB,QAAQ,IAC7DH,YAAYT,EACrB,CASoBa,CAAeP,IAGvBD,GACH,EACDxK,OAAQ,eACJwK,GACgBC,IACZA,EAAW3jB,YAAc2jB,EAAW3jB,WAAWmkB,YAAYR,GAC9CA,EAAA,KAEpB,EAET,EExCWS,GAAiB,WACxB,ICIIC,EDJAC,GCIAD,EAAQZ,KACL,SAAU97B,EAAQ48B,GACrBnkC,EAAAA,WAAgB,WAEZ,OADAikC,EAAM1/B,IAAIgD,GACH,WACH08B,EAAMnL,QACT,CACb,GAAW,CAACvxB,GAAU48B,GACjB,GDNM,OALK,SAAU1rC,GAClB,IAAI8O,EAAS9O,EAAG8O,OAAQ68B,EAAU3rC,EAAG2rC,QAE9B,OADPF,EAAS38B,EAAQ68B,GACV,IACV,CAEL,EEfWC,GAAU,CACjBhrB,KAAM,EACNG,IAAK,EACLF,MAAO,EACPgrB,IAAK,GAELC,GAAQ,SAAU1zB,GAAK,OAAO2zB,SAAS3zB,GAAK,GAAI,KAAO,CAAI,EAQpD4zB,GAAc,SAAUhC,GAE3B,QADY,IAAZA,IAAgCA,EAAA,UACd,oBAAXljC,OACA,OAAA8kC,GAEP,IAAAngB,EAZQ,SAAUue,GACtB,IAAIiC,EAAKnlC,OAAOgJ,iBAAiBjH,SAAS8C,MACtCiV,EAAOqrB,EAAe,YAAZjC,EAAwB,cAAgB,cAClDjpB,EAAMkrB,EAAe,YAAZjC,EAAwB,aAAe,aAChDnpB,EAAQorB,EAAe,YAAZjC,EAAwB,eAAiB,eACjD,MAAA,CAAC8B,GAAMlrB,GAAOkrB,GAAM/qB,GAAM+qB,GAAMjrB,GAC3C,CAMkBqrB,CAAUlC,GACpBmC,EAAgBtjC,SAASuc,gBAAgByF,YACzCuhB,EAActlC,OAAOulC,WAClB,MAAA,CACHzrB,KAAM6K,EAAQ,GACd1K,IAAK0K,EAAQ,GACb5K,MAAO4K,EAAQ,GACfogB,IAAKhxB,KAAKE,IAAI,EAAGqxB,EAAcD,EAAgB1gB,EAAQ,GAAKA,EAAQ,IAE5E,ECxBI6gB,GAAQf,KACDgB,GAAgB,qBAIvBC,GAAY,SAAUxsC,EAAIysC,EAAezC,EAAS0C,GAC9C,IAAA9rB,EAAO5gB,EAAG4gB,KAAMG,EAAM/gB,EAAG+gB,IAAKF,EAAQ7gB,EAAG6gB,MAAOgrB,EAAM7rC,EAAG6rC,IAEtD,YADS,IAAZ7B,IAAgCA,EAAA,UAC7B,QAAQjiB,ObVgB,0BaUc,4BAA4BA,OAAO2kB,EAAW,yBAAyB3kB,OAAO8jB,EAAK,OAAO9jB,OAAO2kB,EAAW,mBAAmB3kB,OAAOwkB,GAAe,8BAA8BxkB,OAAO2kB,EAAW,8CAA8C3kB,OAAO,CACnS0kB,GAAiB,sBAAsB1kB,OAAO2kB,EAAW,KAC7C,WAAZ1C,GACI,uBAAuBjiB,OAAOnH,EAAM,0BAA0BmH,OAAOhH,EAAK,4BAA4BgH,OAAOlH,EAAO,kEAAkEkH,OAAO8jB,EAAK,OAAO9jB,OAAO2kB,EAAW,WACnN,YAAZ1C,GAAyB,kBAAkBjiB,OAAO8jB,EAAK,OAAO9jB,OAAO2kB,EAAW,MAE/E/oC,OAAOC,SACPC,KAAK,IAAK,kBAAkBkkB,OAAOue,GAAoB,mBAAmBve,OAAO8jB,EAAK,OAAO9jB,OAAO2kB,EAAW,mBAAmB3kB,OAAOwe,GAAoB,0BAA0Bxe,OAAO8jB,EAAK,OAAO9jB,OAAO2kB,EAAW,mBAAmB3kB,OAAOue,GAAoB,MAAMve,OAAOue,GAAoB,qBAAqBve,OAAO2kB,EAAW,mBAAmB3kB,OAAOwe,GAAoB,MAAMxe,OAAOwe,GAAoB,4BAA4Bxe,OAAO2kB,EAAW,uBAAuB3kB,OAAOwkB,GAAe,aAAaxkB,ObZ9e,iCaY6gB,MAAMA,OAAO8jB,EAAK,aACnkB,EACIc,GAAuB,WACnB,IAAA9B,EAAUkB,SAASljC,SAAS8C,KAAKy5B,aAAamH,KAAkB,IAAK,IAClE,OAAAzjB,SAAS+hB,GAAWA,EAAU,CACzC,EAkBW+B,GAAkB,SAAU5sC,GACnC,IAAI0pC,EAAa1pC,EAAG0pC,WAAYmD,EAAc7sC,EAAG6sC,YAAa7pC,EAAKhD,EAAGgqC,QAASA,OAAiB,IAAPhnC,EAAgB,SAAWA,EAjBpHuE,EAAAA,WAAgB,WAEZ,OADAsB,SAAS8C,KAAKuM,aAAaq0B,IAAgBI,KAAyB,GAAGv8B,YAChE,WACC,IAAA08B,EAAaH,KAAyB,EACtCG,GAAc,EACLjkC,SAAA8C,KAAK85B,gBAAgB8G,IAG9B1jC,SAAS8C,KAAKuM,aAAaq0B,GAAeO,EAAW18B,WAE5D,CACJ,GAAE,IAaC,IAAAy7B,EAAMvsC,WAAc,WAAc,OAAO0sC,GAAYhC,EAAS,GAAI,CAACA,IACvE,OAAOtC,EAAmBpH,cAACgM,GAAO,CAAEx9B,OAAQ09B,GAAUX,GAAMnC,EAAYM,EAAU6C,EAA6B,GAAf,eACpG,ECpDIE,IAAmB,EACvB,GAAsB,oBAAXjmC,OACH,IACA,IAAIuc,GAAU5nB,OAAOuxC,eAAe,CAAA,EAAI,UAAW,CAC/CnqC,IAAK,WAEM,OADYkqC,IAAA,GACZ,CACV,IAGEjmC,OAAAqD,iBAAiB,OAAQkZ,GAASA,IAElCvc,OAAAoD,oBAAoB,OAAQmZ,GAASA,GACpD,OACW4pB,IACgBF,IAAA,CAC3B,CAEO,IAAIG,KAAaH,IAAmB,CAAEze,SAAS,GCdlD6e,GAAuB,SAAUjvC,EAAM4T,GACnC,KAAE5T,aAAgBmnB,SACX,OAAA,EAEP,IAAAvW,EAAShI,OAAOgJ,iBAAiB5R,GACrC,MAEqB,WAArB4Q,EAAOgD,MAEDhD,EAAO6W,YAAc7W,EAAO4W,YAbX,SAAUxnB,GAEjC,MAAwB,aAAjBA,EAAK+gB,OAChB,CAUoDmuB,CAAqBlvC,IAA8B,YAArB4Q,EAAOgD,GACzF,EAGWu7B,GAA0B,SAAU3rB,EAAMxjB,GACjD,IAAIyK,EAAgBzK,EAAKyK,cACrBzL,EAAUgB,EACX,EAAA,CAMC,GAJ0B,oBAAfsnB,YAA8BtoB,aAAmBsoB,aACxDtoB,EAAUA,EAAQkqB,MAEHkmB,GAAuB5rB,EAAMxkB,GAC9B,CACV,IAAA8C,EAAKutC,GAAmB7rB,EAAMxkB,GAClC,GAD2D8C,EAAG,GAAmBA,EAAG,GAEzE,OAAA,CAEvB,CACQ9C,EAAUA,EAAQiqB,UAC1B,OAAajqB,GAAWA,IAAYyL,EAAcgD,MACvC,OAAA,CACX,EAiBI2hC,GAAyB,SAAU5rB,EAAMxjB,GACzC,MAAgB,MAATwjB,EAtCmB,SAAUxjB,GAAe,OAAAivC,GAAqBjvC,EAAM,YAAe,CAsCvEsvC,CAAwBtvC,GArCpB,SAAUA,GAAe,OAAAivC,GAAqBjvC,EAAM,YAAe,CAqCvCuvC,CAAwBvvC,EAClF,EACIqvC,GAAqB,SAAU7rB,EAAMxjB,GACrC,MAAgB,MAATwjB,EAlBA,EAFyB1hB,EAoBU9B,GAnBvB4oB,UAA0B9mB,EAAGkrB,aAA6BlrB,EAAG8qB,cAO1D,SAAU9qB,GAEzB,MAAA,CADUA,EAAG6mB,WAA0B7mB,EAAGirB,YAA2BjrB,EAAG6qB,YAMnF,CAKsD6iB,CAAoBxvC,GApBhD,IAAU8B,CAqBpC,ECnDW2tC,GAAa,SAAU/vC,GAC9B,MAAO,mBAAoBA,EAAQ,CAACA,EAAMgwC,eAAe,GAAGrzB,QAAS3c,EAAMgwC,eAAe,GAAGpzB,SAAW,CAAC,EAAG,EAChH,EACWqzB,GAAa,SAAUjwC,GAAS,MAAO,CAACA,EAAM2gB,OAAQ3gB,EAAM4gB,OAAU,EAC7EsvB,GAAa,SAAU7xC,GACvB,OAAOA,GAAO,YAAaA,EAAMA,EAAIiB,QAAUjB,CACnD,EAEI8xC,GAAgB,SAAU7tB,GAAM,MAAO,4BAA4B6H,OAAO7H,EAAI,qDAAqD6H,OAAO7H,EAAI,4BAA+B,EAC7K8tB,GAAY,EACZC,GAAY,GAkIhB,SAASC,GAAyBhwC,GAE9B,IADA,IAAIiwC,EAAe,KACH,OAATjwC,GACCA,aAAgBsnB,aAChB2oB,EAAejwC,EAAKkpB,KACpBlpB,EAAOA,EAAKkpB,MAEhBlpB,EAAOA,EAAKipB,WAET,OAAAgnB,CACX,CCzJA,MAAA9G,IZWsC+G,GWG/B,SAA6BpxC,GAChC,IAAIqxC,EAAqB/mC,EAAYpC,OAAC,IAClCopC,EAAgBhnC,EAAYpC,OAAC,CAAC,EAAG,IACjCqpC,EAAajnC,EAAAA,SACb4Y,EAAKxX,EAAcK,SAACilC,MAAa,GACjC1B,EAAQ5jC,EAAAA,SAAe6iC,IAAgB,GACvCiD,EAAYlnC,EAAYpC,OAAClI,GAC7BuK,EAAAA,WAAgB,WACZinC,EAAUtxC,QAAUF,CAC5B,GAAO,CAACA,IACJuK,EAAAA,WAAgB,WACZ,GAAIvK,EAAM4sC,MAAO,CACb/gC,SAAS8C,KAAK8iC,UAAU3iC,IAAI,uBAAuBic,OAAO7H,IAC1D,IAAIwuB,EjBuLT,SAAuBC,EAAIvoC,EAAMwoC,GACtC,GAAIA,GAA6B,IAArB3I,UAAUznC,OAAuB,IAAA,IAAwBqwC,EAAxBtwC,EAAI,EAAGhD,EAAI6K,EAAK5H,OAAYD,EAAIhD,EAAGgD,KACxEswC,GAAQtwC,KAAK6H,IACRyoC,IAASA,EAAA1oC,MAAMzK,UAAUwN,MAAMtM,KAAKwJ,EAAM,EAAG7H,IAC/CswC,EAAAtwC,GAAK6H,EAAK7H,IAGd,OAAAowC,EAAG5mB,OAAO8mB,GAAM1oC,MAAMzK,UAAUwN,MAAMtM,KAAKwJ,GACpD,CiB/L0B0oC,CAAc,CAAC9xC,EAAMmtC,QAAQjtC,UAAWF,EAAMysC,QAAU,IAAIprC,IAAIyvC,KAAa,GAAMnqC,OAAOC,SAExG,OADQ8qC,EAAAlxB,SAAQ,SAAU+O,GAAM,OAAOA,EAAGkiB,UAAU3iC,IAAI,uBAAuBic,OAAO7H,GAAK,IACpF,WACHrX,SAAS8C,KAAK8iC,UAAUpO,OAAO,uBAAuBtY,OAAO7H,IACrDwuB,EAAAlxB,SAAQ,SAAU+O,GAAM,OAAOA,EAAGkiB,UAAUpO,OAAO,uBAAuBtY,OAAO7H,GAAK,GACjG,CACb,CAEA,GAAO,CAACljB,EAAM4sC,MAAO5sC,EAAMmtC,QAAQjtC,QAASF,EAAMysC,SAC9C,IAAIsF,EAAoBrwC,EAAAA,aAAkB,SAAUd,EAAOqqB,GAClD,GAAA,YAAarqB,GAAkC,IAAzBA,EAAMoxC,QAAQxwC,QAAiC,UAAfZ,EAAMb,MAAoBa,EAAMmY,QAChF,OAACy4B,EAAUtxC,QAAQ2sC,eAE1B,IAIAoF,EAJAC,EAAQvB,GAAW/vC,GACnBuxC,EAAab,EAAcpxC,QAC3BqhB,EAAS,WAAY3gB,EAAQA,EAAM2gB,OAAS4wB,EAAW,GAAKD,EAAM,GAClE1wB,EAAS,WAAY5gB,EAAQA,EAAM4gB,OAAS2wB,EAAW,GAAKD,EAAM,GAElEloC,EAASpJ,EAAMoJ,OACfooC,EAAgBv0B,KAAKS,IAAIiD,GAAU1D,KAAKS,IAAIkD,GAAU,IAAM,IAEhE,GAAI,YAAa5gB,GAA2B,MAAlBwxC,GAAyC,UAAhBpoC,EAAOjK,KAC/C,OAAA,EAEP,IAAAsyC,EAA+BhC,GAAwB+B,EAAepoC,GAC1E,IAAKqoC,EACM,OAAA,EAUX,GARIA,EACcJ,EAAAG,GAGAH,EAAkB,MAAlBG,EAAwB,IAAM,IACbC,EAAAhC,GAAwB+B,EAAepoC,KAGrEqoC,EACM,OAAA,EAKX,IAHKd,EAAWrxC,SAAW,mBAAoBU,IAAU2gB,GAAUC,KAC/D+vB,EAAWrxC,QAAU+xC,IAEpBA,EACM,OAAA,EAEP,IAAAK,EAAgBf,EAAWrxC,SAAW+xC,EAC1C,ODVkB,SAAUvtB,EAAM6tB,EAAW3xC,EAAO4xC,GACxD,IAAIC,EATiB,SAAU/tB,EAAMrD,GAMrC,MAAgB,MAATqD,GAA8B,QAAdrD,GAA2B,EAAA,CACtD,CAE0BqxB,CAAmBhuB,EAAM5a,OAAOgJ,iBAAiBy/B,GAAWlxB,WAC9E/F,EAAQm3B,EAAkBD,EAE1BxoC,EAASpJ,EAAMoJ,OACf2oC,EAAeJ,EAAU1kC,SAAS7D,GAClC4oC,GAAqB,EACrBC,EAAkBv3B,EAAQ,EAC1Bw3B,EAAkB,EAClBC,EAAqB,EACtB,EAAA,CACC,IAAI/vC,EAAKutC,GAAmB7rB,EAAM1a,GAASwK,EAAWxR,EAAG,GACrDgwC,EADoEhwC,EAAG,GAAeA,EAAG,GACnDyvC,EAAkBj+B,GACxDA,GAAYw+B,IACR1C,GAAuB5rB,EAAM1a,KACV8oC,GAAAE,EACGD,GAAAv+B,GAKpBxK,EAAAA,EAAOmgB,WAAWC,MAAQpgB,EAAOmgB,UAC9C,QAECwoB,GAAgB3oC,IAAW6B,SAAS8C,MAEjCgkC,IAAiBJ,EAAU1kC,SAAS7D,IAAWuoC,IAAcvoC,IAU3D,OARH6oC,GACkBh1B,KAAKS,IAAIw0B,GAAmB,IAGxCD,GACYh1B,KAAKS,IAAIy0B,GAAsB,KAH5BH,GAAA,GAMlBA,CACX,CC3BeK,CAAaX,EAAernB,EAAQrqB,EAAyB,MAAlB0xC,EAAwB/wB,EAASC,EACtF,GAAE,IACC0xB,EAAgBxxC,eAAkB,SAAUyxC,GAC5C,IAAIvyC,EAAQuyC,EACR,GAAClC,GAAUzvC,QAAUyvC,GAAUA,GAAUzvC,OAAS,KAAO8tC,EAAzD,CAIJ,IAAIh0B,EAAQ,WAAY1a,EAAQiwC,GAAWjwC,GAAS+vC,GAAW/vC,GAC3DwyC,EAAc/B,EAAmBnxC,QAAQyG,QAAO,SAAUjH,GAAK,OAAOA,EAAE6H,OAAS3G,EAAMb,OAASL,EAAEsK,SAAWpJ,EAAMoJ,QAAUpJ,EAAMoJ,SAAWtK,EAAEyxC,gBAxE/H/1B,EAwE6J1b,EAAE4b,MAxE5JD,EAwEmKC,EAxEvJF,EAAE,KAAOC,EAAE,IAAMD,EAAE,KAAOC,EAAE,IAArD,IAAUD,EAAGC,CAwE4K,IAAE,GAElM,GAAA+3B,GAAeA,EAAYC,OACvBzyC,EAAM4O,YACN5O,EAAM8N,sBAKd,IAAK0kC,EAAa,CACd,IAAIE,GAAc9B,EAAUtxC,QAAQusC,QAAU,IACzCprC,IAAIyvC,IACJnqC,OAAOC,SACPD,QAAO,SAAUzF,GAAe,OAAAA,EAAK2M,SAASjN,EAAMoJ,YACxCspC,EAAW9xC,OAAS,EAAIuwC,EAAkBnxC,EAAO0yC,EAAW,KAAO9B,EAAUtxC,QAAQysC,cAE9F/rC,EAAM4O,YACN5O,EAAM8N,gBAG1B,CAtBA,CAuBK,GAAE,IACC6kC,EAAe7xC,EAAAA,aAAkB,SAAU6F,EAAM+T,EAAOtR,EAAQqpC,GAC5D,IAAAzyC,EAAQ,CAAE2G,OAAY+T,QAActR,SAAgBqpC,SAAgBlC,aAAcD,GAAyBlnC,IAC5FqnC,EAAAnxC,QAAQ0gB,KAAKhgB,GAChC0M,YAAW,WACP+jC,EAAmBnxC,QAAUmxC,EAAmBnxC,QAAQyG,QAAO,SAAUjH,GAAK,OAAOA,IAAMkB,IAC9F,GAAE,EACN,GAAE,IACC4yC,EAAmB9xC,eAAkB,SAAUd,GACjC0wC,EAAApxC,QAAUywC,GAAW/vC,GACnC2wC,EAAWrxC,aAAU,CACxB,GAAE,IACCuzC,EAAc/xC,eAAkB,SAAUd,GAC1C2yC,EAAa3yC,EAAMb,KAAM8wC,GAAWjwC,GAAQA,EAAMoJ,OAAQ+nC,EAAkBnxC,EAAOZ,EAAMmtC,QAAQjtC,SACpG,GAAE,IACCwzC,EAAkBhyC,eAAkB,SAAUd,GAC9C2yC,EAAa3yC,EAAMb,KAAM4wC,GAAW/vC,GAAQA,EAAMoJ,OAAQ+nC,EAAkBnxC,EAAOZ,EAAMmtC,QAAQjtC,SACpG,GAAE,IACHqK,EAAAA,WAAgB,WAUZ,OATA0mC,GAAUrwB,KAAK0uB,GACftvC,EAAMqsC,aAAa,CACfJ,gBAAiBwH,EACjBvH,eAAgBuH,EAChBtH,mBAAoBuH,IAEf7nC,SAAAsB,iBAAiB,QAAS+lC,EAAehD,IACzCrkC,SAAAsB,iBAAiB,YAAa+lC,EAAehD,IAC7CrkC,SAAAsB,iBAAiB,aAAcqmC,EAAkBtD,IACnD,WACSe,GAAAA,GAAUtqC,QAAO,SAAUgtC,GAAQ,OAAOA,IAASrE,KACtDzjC,SAAAqB,oBAAoB,QAASgmC,EAAehD,IAC5CrkC,SAAAqB,oBAAoB,YAAagmC,EAAehD,IAChDrkC,SAAAqB,oBAAoB,aAAcsmC,EAAkBtD,GAChE,CACJ,GAAE,IACH,IAAI1D,EAAkBxsC,EAAMwsC,gBAAiBI,EAAQ5sC,EAAM4sC,MAC3D,OAAQlC,EAAAA,cAAoB3jC,EAAAA,SAAgB,KACxC6lC,EAAQlC,EAAAA,cAAoB4E,EAAO,CAAEx9B,OAAQi/B,GAAc7tB,KAAS,KACpEspB,EAAkB9B,EAAmBpH,cAACsM,GAAiB,CAAElD,WAAY1sC,EAAM0sC,WAAYM,QAAShtC,EAAMgtC,UAAa,KAC3H,EC9I6BpC,GZYlBK,UAAUmG,IACV/G,IAFJ,IAA+B+G,GaVlCwC,GAAoBvvC,EAAgBiE,YAAC,SAAUtI,EAAOf,GAAO,OAAQyrC,EAAmBpH,cAACyI,GAAclD,GAAS,CAAA,EAAI7oC,EAAO,CAAEf,MAAUqrC,QAASD,KAAe,IACnKuJ,GAAkBxG,WAAarB,GAAaqB,WCuB5C,IAAIyG,GAAY,CAAC,IAAK,QAAS,UAAW,aACtCC,GAAiB,CAAC,IAAK,SACvBC,GAAc,UACb3+B,GAAYC,GAAe3N,IAAyBJ,EAAiBysC,KACrEC,GAAqBC,IAAqBryC,EAAmBmyC,GAAa,CAC7ErsC,GACA80B,KAEE0X,GAAiB1X,MAChB2X,GAAgBC,IAAoBJ,GAAoBD,KACxDM,GAA6BC,IAAiCN,GAAoBD,IACnFQ,GAAUv0C,IACN,MAAAw0C,cACJA,EAAA3xC,SACAA,EACAyX,KAAMC,EAAAC,YACNA,EAAAC,aACAA,EACA1Z,MAAO0zC,EAAA7K,aACPA,EAAA8K,cACAA,EAAA7S,IACAA,EAAAt6B,KACAA,EAAAotC,aACAA,EAAAzyB,SACAA,EAAA0yB,SACAA,EAAAC,KACAA,GACE70C,EACE80C,EAAcZ,GAAeM,IAC5BO,EAASC,GAActpC,EAAAA,SAAe,OACtCupC,EAAWC,GAAgBxpC,EAAAA,SAAe,OAC1CypC,EAAsBC,GAA2B1pC,EAAAA,UAAe,GACjE2V,EAAYyhB,GAAajB,IACxBvnB,EAAMK,GAAWtH,EAAqB,CAC3CC,KAAMiH,EACNhH,YAAaiH,IAAe,EAC5BhH,SAAUiH,EACVhH,OAAQsgC,MAEHhzC,EAAO8S,GAAYR,EAAqB,CAC7CC,KAAMmhC,EACNlhC,YAAaq2B,EACbp2B,SAAUkhC,EACVjhC,OAAQsgC,KAEJsB,EAA2B/qC,EAAYpC,OAAC,MACxCotC,GAAgBP,IAAUF,KAAUE,EAAQQ,QAAQ,UACnDC,EAAkBC,GAAuB/pC,EAAAA,SAA+B,IAAIZ,KAC7E4qC,EAAkBvsC,MAAMC,KAAKosC,GAAkBn0C,KAAKs0C,GAAWA,EAAO31C,MAAMe,QAAO8F,KAAK,KAC9F,SAA0BzG,IAACw1C,GAAsB,IAAKd,EAAajyC,SAA8BoM,EAAA5O,KAC/F8zC,GACA,CACES,WACAxyC,MAAOoyC,EACPO,UACAc,gBAAiBb,EACjBC,YACAa,kBAAmBZ,EACnBC,uBACAY,6BAA8BX,EAC9BY,UAAWhzB,KACXjiB,QACA2zC,cAAe7gC,EACfyG,OACAG,aAAcE,EACdknB,IAAKxgB,EACLg0B,2BACAnzB,WACArf,SAAU,CACQzC,EAAGA,IAACgV,GAAWxS,SAAU,CAAER,MAAOoyC,EAAe3xC,SAA6BoM,EAAA7O,IAC5Fi0C,GACA,CACEjyC,MAAOpC,EAAMw0C,cACbyB,kBAAmBv0C,EAAAA,aAAmBi0C,IAChBF,GAACS,GAAS,IAAIprC,IAAIorC,GAAMpnC,IAAI6mC,OAC/C,IACHQ,qBAAsBz0C,EAAAA,aAAmBi0C,IACvCF,GAAqBS,IACb,MAAAE,EAAa,IAAItrC,IAAIorC,GAEpB,OADPE,EAAWrtC,OAAO4sC,GACXS,OAER,IACHvzC,eAGJyyC,EAAoCrmC,EAAA5O,KAClCg2C,GACA,CACE,eAAe,EACfzB,WACAj7B,UAAU,EACVpS,OACAotC,eACA5zC,QACAyS,SAAW5S,GAAUiT,EAASjT,EAAMoJ,OAAOjJ,OAC3CmhB,WACA2yB,OACAhyC,SAAU,MACE,IAAV9B,QAAuC,SAAU,CAAEA,MAAO,KAAQ,KAClEoI,MAAMC,KAAKosC,KAGfE,GACE,WAKZnB,GAAOrxC,YAAc6wC,GACrB,IAAIuC,GAAe,gBACfC,GAAgBlyC,EAAgBiE,YAClC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,EAAetyB,SAAAA,GAAW,KAAUs0B,GAAiBx2C,EACvD80C,EAAcZ,GAAeM,GAC7B1xC,EAAUsxC,GAAiBkC,GAAc9B,GACzCiC,EAAa3zC,EAAQof,UAAYA,EACjC3Z,EAAe9G,EAAgB6C,EAAcxB,EAAQ+yC,iBACrD5+B,EAAW5B,GAAcm/B,GACzBkC,EAAiBpsC,EAAYpC,OAAC,UAC7ByuC,EAAWC,EAAuBC,GAAkBC,IAAoBC,IACvE,MAAAC,EAAe//B,IAAWtQ,QAAQ6+B,IAAUA,EAAKtjB,WACjD+0B,EAAcD,EAAapyC,MAAM4gC,GAASA,EAAKzkC,QAAU+B,EAAQ/B,QACjEm2C,EAAWC,GAAaH,EAAcD,EAAQE,QACnC,IAAbC,GACMp0C,EAAA4xC,cAAcwC,EAASn2C,UAG7Bq2C,EAAcC,IACbZ,IACH3zC,EAAQ2X,cAAa,GACLo8B,KAEdQ,IACFv0C,EAAQuyC,yBAAyBn1C,QAAU,CACzCkb,EAAGyC,KAAK0F,MAAM8zB,EAAaC,OAC3Bj8B,EAAGwC,KAAK0F,MAAM8zB,EAAaE,UAIVn3C,OAAAA,EAAAA,IAAIo3C,GAAwB,CAAE7tC,SAAS,KAASmrC,EAAajyC,SAA6BoM,EAAA7O,IAC/GoJ,EAAU8T,OACV,CACEvd,KAAM,SACN2Z,KAAM,WACN,gBAAiB5W,EAAQkzC,UACzB,gBAAiBlzC,EAAQwX,KACzB,gBAAiBxX,EAAQ8xC,SACzB,oBAAqB,OACrB/S,IAAK/+B,EAAQ++B,IACb,aAAc/+B,EAAQwX,KAAO,OAAS,SACtC4H,SAAUu0B,EACV,gBAAiBA,EAAa,QAAK,EACnC,mBAAoBgB,GAAsB30C,EAAQ/B,OAAS,QAAK,KAC7Dy1C,EACHv3C,IAAKsJ,EACL6X,QAAS5f,EAAqBg2C,EAAap2B,SAAUxf,IACnDA,EAAMqa,cAAcvD,QACW,UAA3Bg/B,EAAex2C,SACjBk3C,EAAWx2C,MAGfyc,cAAe7c,EAAqBg2C,EAAan5B,eAAgBzc,IAC/D81C,EAAex2C,QAAUU,EAAMqM,YAC/B,MAAMjD,EAASpJ,EAAMoJ,OACjBA,EAAOwU,kBAAkB5d,EAAMyd,YAC1BrU,EAAAyU,sBAAsB7d,EAAMyd,WAEhB,IAAjBzd,EAAM0c,SAAkC,IAAlB1c,EAAMmY,SAA2C,UAAtBnY,EAAMqM,cACzDmqC,EAAWx2C,GACXA,EAAM8N,qBAGVyO,UAAW3c,EAAqBg2C,EAAar5B,WAAYvc,IACjD,MAAA82C,EAAsC,KAAtBf,EAAUz2C,QACVU,EAAMmY,SAAWnY,EAAMkY,QAAUlY,EAAMoY,SAClB,IAArBpY,EAAM5B,IAAIwC,QAAco1C,EAAsBh2C,EAAM5B,KACtE04C,GAA+B,MAAd92C,EAAM5B,KACvB60C,GAAUthC,SAAS3R,EAAM5B,OACfo4C,IACZx2C,EAAM8N,4BAOlB6nC,GAAcrzC,YAAcozC,GAC5B,IAAIqB,GAAa,cACbC,GAAcvzC,EAAgBiE,YAChC,CAACtI,EAAOsE,KACA,MAAAkwC,cAAEA,YAAejI,EAAW39B,MAAAA,EAAA/L,SAAOA,cAAUg1C,EAAc,MAAOC,GAAe93C,EACjF8C,EAAUsxC,GAAiBuD,GAAYnD,IACvCuB,6BAAEA,GAAiCjzC,EACnCi1C,OAA2B,IAAbl1C,EACd0F,EAAe9G,EAAgB6C,EAAcxB,EAAQgzC,mBAI3D,OAHA3lC,GAAgB,KACd4lC,EAA6BgC,KAC5B,CAAChC,EAA8BgC,IACR9oC,EAAA7O,IACxBoJ,EAAU2L,KACV,IACK2iC,EACH74C,IAAKsJ,EACLqG,MAAO,CAAEC,cAAe,QACxBhM,SAAU40C,GAAsB30C,EAAQ/B,OAAyBX,MAAIwc,EAAAA,SAAU,CAAE/Z,SAAUg1C,IAAiBh1C,OAKpH+0C,GAAY10C,YAAcy0C,GAC1B,IACIK,GAAa3zC,EAAgBiE,YAC/B,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,EAAA3xC,SAAeA,KAAao1C,GAAcj4C,EAClD,OAA0BiP,EAAA7O,IAACoJ,EAAU2L,KAAM,CAAE,eAAe,KAAS8iC,EAAWh5C,IAAKqF,EAAczB,SAAUA,GAAY,SAG7Hm1C,GAAW90C,YAPK,aAQhB,IACIg1C,GAAgBl4C,GACKI,EAAAA,IAAI+3C,EAAiB,CAAExuC,SAAS,KAAS3J,IAElEk4C,GAAah1C,YAJK,eAKlB,IAAIk6B,GAAe,gBACfgb,GAAgB/zC,EAAgBiE,YAClC,CAACtI,EAAOsE,KACN,MAAMxB,EAAUsxC,GAAiBhX,GAAcp9B,EAAMw0C,gBAC9C6D,EAAUC,GAAe5sC,aAI5B,GAHJyE,GAAgB,KACFmoC,EAAA,IAAIC,oBACf,KACEz1C,EAAQwX,KAAM,CACjB,MAAMk+B,EAAOH,EACb,OAAOG,EAAO17B,EAAqBzM,mBACbooC,GAAuB,CAAEr2C,MAAOpC,EAAMw0C,cAAe3xC,SAA6BoM,EAAA7O,IAACgV,GAAW7P,KAAM,CAAEnD,MAAOpC,EAAMw0C,cAAe3xC,SAA0BzC,EAAGA,IAAC,MAAO,CAAEyC,SAAU7C,EAAM6C,eAC7M21C,GACE,IACV,CAC2Bp4C,OAAAA,EAAAA,IAAIs4C,GAAmB,IAAK14C,EAAOf,IAAKqF,OAGnE8zC,GAAcl1C,YAAck6B,GAC5B,IAAIub,GAAiB,IAChBF,GAAuBG,IAA2B5E,GAAoB5W,IAEvE73B,KAAkB,8BAClBmzC,GAAoBr0C,EAAgBiE,YACtC,CAACtI,EAAOsE,KACA,MAAAkwC,cACJA,EAAAhgC,SACAA,EAAW,eAAAqkC,iBACXA,EAAA1tC,gBACAA,EAAAC,qBACAA,EAAA6Z,KAGAA,EAAAuY,WACAA,EAAAC,MACAA,EAAAC,YACAA,EAAAC,aACAA,EAAAE,kBACAA,EAAAC,iBACAA,EAAAE,OACAA,EAAAC,iBACAA,EAAAL,gBACAA,KAEGQ,GACDp+B,EACE8C,EAAUsxC,GAAiBhX,GAAcoX,IACxCnW,EAASC,GAAc5yB,EAAAA,SAAe,OACtCsK,EAAUC,GAAevK,EAAAA,SAAe,MACzCnD,EAAe9G,EAAgB6C,GAAepD,GAASo9B,EAAWp9B,MACjE43C,EAAcC,GAAmBrtC,EAAAA,SAAe,OAChDstC,EAAkBC,GAAuBvtC,EAAcK,SAC5D,MAEIkL,EAAW5B,GAAcm/B,IACxBzV,EAAcma,GAAmBxtC,EAAAA,UAAe,GACjDytC,EAAyB7uC,EAAYpC,QAAC,GAC5CqC,EAAAA,WAAgB,KACV,GAAA8zB,EAAgB,OAAAqK,GAAWrK,KAC9B,CAACA,IACY4E,KAChB,MAAM1pB,EAAa7X,EAAiBC,aACjC4gB,IACC,MAAO62B,KAAcC,GAAapiC,IAAW5V,KAAKmkC,GAASA,EAAKvmC,IAAIiB,WAC7Do5C,GAAYD,EAAUntC,OAAQ,GAC/BqtC,EAA6B1tC,SAASsM,cAC5C,IAAA,MAAWmB,KAAaiJ,EAAY,CAClC,GAAIjJ,IAAcigC,EAA4B,OAK1C,GAJO,MAAAjgC,GAAAA,EAAAkgC,eAAe,CAAEC,MAAO,YAC/BngC,IAAc8/B,GAAapjC,IAAUA,EAAS8T,UAAY,GAC1DxQ,IAAcggC,GAAYtjC,IAAUA,EAAS8T,UAAY9T,EAASkY,cAC3D,MAAA5U,GAAAA,EAAA5B,QACP7L,SAASsM,gBAAkBohC,EAA4B,MACrE,IAEM,CAACtiC,EAAUjB,IAEP0jC,EAAoBh4C,EAAiBC,aACzC,IAAM4X,EAAW,CAACu/B,EAAcza,KAChC,CAAC9kB,EAAYu/B,EAAcza,IAE7B9zB,EAAAA,WAAgB,KACVw0B,GACiB2a,MAEpB,CAAC3a,EAAc2a,IACZ,MAAAj/B,aAAEA,EAAc46B,yBAAAA,GAA6BvyC,EACnDyH,EAAAA,WAAgB,KACd,GAAI8zB,EAAS,CACX,IAAIsb,EAAmB,CAAEv+B,EAAG,EAAGC,EAAG,GAC5B,MAAAu+B,EAAqBh5C,YACN+4C,EAAA,CACjBv+B,EAAGyC,KAAKS,IAAIT,KAAK0F,MAAM3iB,EAAM02C,SAAU,OAAAt0C,EAAyBqyC,EAAAn1C,cAAS,EAAA8C,EAAAoY,IAAK,IAC9EC,EAAGwC,KAAKS,IAAIT,KAAK0F,MAAM3iB,EAAM22C,SAAU,OAAAvxC,EAAyBqvC,EAAAn1C,cAAS,EAAA8F,EAAAqV,IAAK,MAG5Ew+B,EAAmBj5C,IACnB+4C,EAAiBv+B,GAAK,IAAMu+B,EAAiBt+B,GAAK,GACpDza,EAAM8N,iBAED2vB,EAAQxwB,SAASjN,EAAMoJ,SAC1ByQ,GAAa,GAGR5O,SAAAqB,oBAAoB,cAAe0sC,GAC5CvE,EAAyBn1C,QAAU,MAMrC,OAJyC,OAArCm1C,EAAyBn1C,UAClB2L,SAAAsB,iBAAiB,cAAeysC,GAChC/tC,SAAAsB,iBAAiB,YAAa0sC,EAAiB,CAAErrC,SAAS,EAAMpB,MAAM,KAE1E,KACIvB,SAAAqB,oBAAoB,cAAe0sC,GAC5C/tC,SAASqB,oBAAoB,YAAa2sC,EAAiB,CAAErrC,SAAS,IAEhF,IACO,CAAC6vB,EAAS5jB,EAAc46B,IAC3B9qC,EAAAA,WAAgB,KACR,MAAAuvC,EAAQ,IAAMr/B,GAAa,GAGjC,OAFO3Q,OAAAqD,iBAAiB,OAAQ2sC,GACzBhwC,OAAAqD,iBAAiB,SAAU2sC,GAC3B,KACEhwC,OAAAoD,oBAAoB,OAAQ4sC,GAC5BhwC,OAAAoD,oBAAoB,SAAU4sC,MAEtC,CAACr/B,IACJ,MAAOk8B,EAAWC,GAAyBE,IAAoBC,IACvD,MAAAC,EAAe//B,IAAWtQ,QAAQ6+B,IAAUA,EAAKtjB,WACjD+0B,EAAcD,EAAapyC,MAAM4gC,GAASA,EAAKvmC,IAAIiB,UAAY2L,SAASsM,gBACxE++B,EAAWC,GAAaH,EAAcD,EAAQE,GAChDC,GACF5pC,YAAW,IAAM4pC,EAASj4C,IAAIiB,QAAQwX,aAGpCqiC,EAAkBr4C,EAAiBC,aACvC,CAACT,EAAMH,EAAOmhB,KACZ,MAAM83B,GAAoBb,EAAuBj5C,UAAYgiB,QACpB,IAAlBpf,EAAQ/B,OAAoB+B,EAAQ/B,QAAUA,GAC/Ci5C,KACpBjB,EAAgB73C,GACZ84C,MAAyC95C,SAAU,MAG3D,CAAC4C,EAAQ/B,QAELk5C,EAAkBv4C,EAAAA,aAAkB,UAAM28B,WAAS3mB,SAAS,CAAC2mB,IAC7D6b,EAAsBx4C,EAAiBC,aAC3C,CAACT,EAAMH,EAAOmhB,KACZ,MAAM83B,GAAoBb,EAAuBj5C,UAAYgiB,QACpB,IAAlBpf,EAAQ/B,OAAoB+B,EAAQ/B,QAAUA,GAC/Ci5C,IACpBf,EAAoB/3C,KAGxB,CAAC4B,EAAQ/B,QAELo5C,EAA8B,WAAb3lC,EAAwB4lC,GAAuBC,GAChEC,EAAqBH,IAAmBC,GAAuB,CACnEn1B,OACAuY,aACAC,QACAC,cACAC,eACAE,oBACAC,mBACAE,SACAC,mBACAL,mBACE,CAAE,EACN,OAA0B3uB,EAAA7O,IACxBq4C,GACA,CACEr2C,MAAOoyC,EACPnW,UACAroB,WACAM,iBAAkBL,EAClB8jC,kBACAjB,eACAyB,YAAaN,EACbC,sBACAR,oBACAV,mBACAxkC,WACAuqB,eACA4X,YACA9zC,SAA0BzC,EAAAA,IAAI2rC,GAAc,CAAEe,GAAIvnC,GAAMsnC,gBAAgB,EAAMhqC,SAA6BoM,EAAA7O,IACzGwjC,GACA,CACEj6B,SAAS,EACTm6B,QAAShhC,EAAQwX,KACjBypB,iBAAmBnjC,IACjBA,EAAM8N,kBAERu1B,mBAAoBzjC,EAAqBq4C,GAAmBj4C,UAC1D,OAAAoC,EAAAF,EAAQiyC,UAAR/xC,EAAiB0U,MAAM,CAAEwuB,eAAe,IACxCtlC,EAAM8N,oBAER7L,SAA6BoM,EAAA7O,IAC3B6K,EACA,CACEtB,SAAS,EACTuB,6BAA6B,EAC7BC,kBACAC,uBACAC,eAAiBzK,GAAUA,EAAM8N,iBACjCnD,UAAW,IAAMzI,EAAQ2X,cAAa,GACtC5X,SAA6BoM,EAAA7O,IAC3B+5C,EACA,CACEzgC,KAAM,UACNwJ,GAAIpgB,EAAQkzC,UACZ,aAAclzC,EAAQwX,KAAO,OAAS,SACtCunB,IAAK/+B,EAAQ++B,IACb2Y,cAAgB55C,GAAUA,EAAM8N,oBAC7B0vB,KACAkc,EACHnc,SAAU,IAAM+a,GAAgB,GAChCj6C,IAAKsJ,EACLqG,MAAO,CAELqD,QAAS,OACTwoC,cAAe,SAEflX,QAAS,UACNnF,EAAaxvB,OAElBuO,UAAW3c,EAAqB49B,EAAajhB,WAAYvc,IACvD,MAAM85C,EAAgB95C,EAAMmY,SAAWnY,EAAMkY,QAAUlY,EAAMoY,QAGzD,GAFc,QAAdpY,EAAM5B,KAAe4B,EAAM8N,iBAC1BgsC,GAAsC,IAArB95C,EAAM5B,IAAIwC,QAAco1C,EAAsBh2C,EAAM5B,KACtE,CAAC,UAAW,YAAa,OAAQ,OAAOuT,SAAS3R,EAAM5B,KAAM,CAE/D,IAAI27C,EADU1jC,IAAWtQ,QAAQ6+B,IAAUA,EAAKtjB,WACrB7gB,KAAKmkC,GAASA,EAAKvmC,IAAIiB,UAIlD,GAHI,CAAC,UAAW,OAAOqS,SAAS3R,EAAM5B,OACnB27C,EAAAA,EAAezuC,QAAQyM,WAEtC,CAAC,UAAW,aAAapG,SAAS3R,EAAM5B,KAAM,CAChD,MAAM47C,EAAiBh6C,EAAMoJ,OACvB6wC,EAAeF,EAAepxC,QAAQqxC,GAC3BD,EAAAA,EAAezuC,MAAM2uC,EAAe,EAC/E,CACmCvtC,YAAA,IAAMiM,EAAWohC,KAC5B/5C,EAAM8N,gBAC9B,kBAYAgqC,GAAkBx1C,YA1OM,oBA2OxB,IACIm3C,GAA4Bh2C,EAAgBiE,YAAC,CAACtI,EAAOsE,KACvD,MAAMkwC,cAAEA,EAAArW,SAAeA,KAAa2c,GAAgB96C,EAC9C8C,EAAUsxC,GAAiBhX,GAAcoX,GACzCpS,EAAiBwW,GAAwBxb,GAAcoX,IACtDuG,EAAgBC,GAAqBtvC,EAAAA,SAAe,OACpD2yB,EAASC,GAAc5yB,EAAAA,SAAe,MACvCnD,EAAe9G,EAAgB6C,GAAepD,GAASo9B,EAAWp9B,KAClE+V,EAAW5B,GAAcm/B,GACzByG,EAA0B3wC,EAAYpC,QAAC,GACvCgzC,EAAsB5wC,EAAYpC,QAAC,IACnC8N,SAAEA,EAAA8iC,aAAUA,EAAcE,iBAAAA,EAAAU,kBAAkBA,GAAsBtX,EAClE5tB,EAAW9S,EAAAA,aAAkB,KAC7B,GAAAoB,EAAQiyC,SAAWjyC,EAAQmyC,WAAa8F,GAAkB1c,GAAWroB,GAAY8iC,GAAgBE,EAAkB,CAC/G,MAAAmC,EAAcr4C,EAAQiyC,QAAQnpB,wBAC9BwvB,EAAc/c,EAAQzS,wBACtByvB,EAAgBv4C,EAAQmyC,UAAUrpB,wBAClC0vB,EAAetC,EAAiBptB,wBAClC,GAAgB,QAAhB9oB,EAAQ++B,IAAe,CACnB,MAAA0Z,EAAiBD,EAAa13B,KAAOw3B,EAAYx3B,KACjDA,EAAOy3B,EAAcz3B,KAAO23B,EAC5BC,EAAYL,EAAYv3B,KAAOA,EAC/B63B,EAAkBN,EAAYzmC,MAAQ8mC,EACtCE,EAAe79B,KAAKE,IAAI09B,EAAiBL,EAAY1mC,OACrDinC,EAAY7xC,OAAOulC,WAAasJ,GAChCiD,EAAch+B,GAAMgG,EAAM,CAC9B+0B,GAMA96B,KAAKE,IAAI46B,GAAgBgD,EAAYD,KAExBX,EAAAnsC,MAAM6yB,SAAWga,EAAkB,KACnCV,EAAAnsC,MAAMgV,KAAOg4B,EAAc,IAClD,KAAa,CACC,MAAAL,EAAiBH,EAAYv3B,MAAQy3B,EAAaz3B,MAClDA,EAAQ/Z,OAAOulC,WAAagM,EAAcx3B,MAAQ03B,EAClDM,EAAa/xC,OAAOulC,WAAa8L,EAAYt3B,MAAQA,EACrD43B,EAAkBN,EAAYzmC,MAAQmnC,EACtCH,EAAe79B,KAAKE,IAAI09B,EAAiBL,EAAY1mC,OACrDonC,EAAWhyC,OAAOulC,WAAasJ,GAC/BoD,EAAen+B,GAAMiG,EAAO,CAChC80B,GACA96B,KAAKE,IAAI46B,GAAgBmD,EAAWJ,KAEvBX,EAAAnsC,MAAM6yB,SAAWga,EAAkB,KACnCV,EAAAnsC,MAAMiV,MAAQk4B,EAAe,IACpD,CACM,MAAMxW,EAAQtuB,IACRygB,EAAkB5tB,OAAOkyC,YAA+B,EAAjBrD,GACvCsD,EAAcjmC,EAASkY,aACvBguB,EAAgBpyC,OAAOgJ,iBAAiBurB,GACxC8d,EAAwBpN,SAASmN,EAAcE,eAAgB,IAC/DC,EAAoBtN,SAASmN,EAAc/uB,WAAY,IACvDmvB,EAA2BvN,SAASmN,EAAcK,kBAAmB,IAErEC,EAAoBL,EAAwBE,EAAoBJ,EADzClN,SAASmN,EAAcO,cAAe,IACwCH,EACrGI,EAAmB7+B,KAAKC,IAAgC,EAA5Bg7B,EAAavtB,aAAkBixB,GAC3DG,EAAiB7yC,OAAOgJ,iBAAiBkD,GACzC4mC,EAAqB7N,SAAS4N,EAAexvB,WAAY,IACzD0vB,EAAwB9N,SAAS4N,EAAeF,cAAe,IAC/DK,EAAyB3B,EAAYp3B,IAAMo3B,EAAYxmC,OAAS,EAAIgkC,GACpEoE,EAA4BrlB,EAAkBolB,EAC9CE,EAAyBlE,EAAavtB,aAAe,EAErD0xB,EAAyBd,EAAwBE,GAD9BvD,EAAa5sB,UAAY8wB,GAE5CE,EAA4BV,EAAoBS,EAEtD,GADoCA,GAA0BH,EAC7B,CACzB,MAAAK,EAAa5X,EAAM/jC,OAAS,GAAKs3C,IAAiBvT,EAAMA,EAAM/jC,OAAS,GAAGvC,IAAIiB,QACpF66C,EAAensC,MAAMkV,OAAS,MAC9B,MAAMs5B,EAAuB/e,EAAQvQ,aAAe9X,EAASkW,UAAYlW,EAASuV,aAM5E5W,EAASsoC,EAL0Bp/B,KAAKE,IAC5Cg/B,EACAC,GACCG,EAAaN,EAAwB,GAAKO,EAAuBd,GAGrDvB,EAAAnsC,MAAM+F,OAASA,EAAS,IAC/C,KAAa,CACC,MAAA0oC,EAAc9X,EAAM/jC,OAAS,GAAKs3C,IAAiBvT,EAAM,GAAGtmC,IAAIiB,QACtE66C,EAAensC,MAAMmV,IAAM,MAC3B,MAKMpP,EALgCkJ,KAAKE,IACzC++B,EACAX,EAAwBnmC,EAASkW,WAChCmxB,EAAcT,EAAqB,GAAKI,GAEIE,EAChCnC,EAAAnsC,MAAM+F,OAASA,EAAS,KAC9BqB,EAAA8T,UAAYmzB,EAAyBH,EAAyB9mC,EAASkW,SACxF,CACqB6uB,EAAAnsC,MAAMiG,OAAS,GAAG8jC,SAClBoC,EAAAnsC,MAAM0uC,UAAYZ,EAAmB,KACrC3B,EAAAnsC,MAAM2uC,UAAY7lB,EAAkB,KACnD,MAAAyG,GAAAA,IACsB/e,uBAAA,IAAM67B,EAAwB/6C,SAAU,GACpE,IACK,CACD+W,EACAnU,EAAQiyC,QACRjyC,EAAQmyC,UACR8F,EACA1c,EACAroB,EACA8iC,EACAE,EACAl2C,EAAQ++B,IACR1D,IAEFhuB,GAAgB,IAAMqE,KAAY,CAACA,IACnC,MAAO8sB,EAAeC,GAAoB71B,aAC1CyE,GAAgB,KACVkuB,GAA0BkD,EAAAz3B,OAAOgJ,iBAAiBurB,GAASmD,UAC9D,CAACnD,IACJ,MAAMmf,EAA2B97C,EAAiBC,aAC/CT,IACKA,IAAwC,IAAhCg6C,EAAoBh7C,UACpBsU,IACV,MAAAklC,GAAAA,IACAwB,EAAoBh7C,SAAU,KAGlC,CAACsU,EAAUklC,IAEb,OAA0BzqC,EAAA7O,IACxBq9C,GACA,CACEr7C,MAAOoyC,EACPuG,iBACAE,0BACAyC,qBAAsBF,EACtB36C,SAA6BoM,EAAA7O,IAC3B,MACA,CACEnB,IAAK+7C,EACLpsC,MAAO,CACLqD,QAAS,OACTwoC,cAAe,SACfjmC,SAAU,QACVgtB,OAAQF,GAEVz+B,SAA6BoM,EAAA7O,IAC3BoJ,EAAU0F,IACV,IACK4rC,EACH77C,IAAKsJ,EACLqG,MAAO,CAGL+uC,UAAW,aAEXJ,UAAW,UACRzC,EAAYlsC,gBAS/ByrC,GAA0Bn3C,YAnKO,4BAoKjC,IACIk3C,GAAuB/1C,EAAgBiE,YAAC,CAACtI,EAAOsE,KAC5C,MAAAkwC,cACJA,EAAA/W,MACAA,EAAQ,QAAAK,iBACRA,EAAmB6a,MAChBmC,GACD96C,EACE80C,EAAcZ,GAAeM,GACnC,OAA0BvlC,EAAA7O,IACxBw9C,GACA,IACK9I,KACAgG,EACH77C,IAAKqF,EACLm5B,QACAK,mBACAlvB,MAAO,CAEL+uC,UAAW,gBACR7C,EAAYlsC,MAGb,0CAA2C,uCAC3C,yCAA0C,sCAC1C,0CAA2C,uCAC3C,+BAAgC,mCAChC,gCAAiC,0CAM3CwrC,GAAqBl3C,YAjCM,uBAkC3B,IAAKu6C,GAAwBI,IAA4B7J,GAAoB5W,GAAc,CAAA,GACvF1mB,GAAgB,iBAChBonC,GAAiBz5C,EAAgBiE,YACnC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,EAAA/G,MAAeA,KAAUz2B,GAAkBhX,EAC7CoiC,EAAiBwW,GAAwBliC,GAAe89B,GACxDuJ,EAAkBF,GAAyBnnC,GAAe89B,GAC1DjsC,EAAe9G,EAAgB6C,EAAc89B,EAAe9rB,kBAC5D0nC,EAAmB1zC,EAAYpC,OAAC,GACtC,OAA2B+G,EAAA5O,KAACuc,WAAU,CAAE/Z,SAAU,CAC7BoM,EAAA7O,IACjB,QACA,CACE69C,wBAAyB,CACvBC,OAAQ,6KAEVzQ,UAGYrtC,EAAGA,IAACgV,GAAW7P,KAAM,CAAEnD,MAAOoyC,EAAe3xC,SAA6BoM,EAAA7O,IACxFoJ,EAAU0F,IACV,CACE,6BAA8B,GAC9BwK,KAAM,kBACH1C,EACH/X,IAAKsJ,EACLqG,MAAO,CAIL4F,SAAU,WACV2pC,KAAM,EAKNrpC,SAAU,iBACPkC,EAAcpI,OAEnBwvC,SAAU59C,EAAqBwW,EAAconC,UAAWx9C,IACtD,MAAMoV,EAAWpV,EAAMqa,eACjB8/B,eAAEA,EAAgBE,wBAAAA,GAA4B8C,EAChD,IAAA,MAAA9C,OAAA,EAAAA,EAAyB/6C,UAAW66C,EAAgB,CACtD,MAAMsD,EAAaxgC,KAAKS,IAAI0/B,EAAiB99C,QAAU8V,EAAS8T,WAChE,GAAIu0B,EAAa,EAAG,CACZ,MAAA3mB,EAAkB5tB,OAAOkyC,YAA+B,EAAjBrD,GACvC2F,EAAelzB,WAAW2vB,EAAensC,MAAM0uC,WAC/CiB,EAAYnzB,WAAW2vB,EAAensC,MAAM+F,QAC5C6pC,EAAa3gC,KAAKE,IAAIugC,EAAcC,GAC1C,GAAIC,EAAa9mB,EAAiB,CAChC,MAAM+mB,EAAaD,EAAaH,EAC1BK,EAAoB7gC,KAAKC,IAAI4Z,EAAiB+mB,GAC9CE,EAAaF,EAAaC,EACjB3D,EAAAnsC,MAAM+F,OAAS+pC,EAAoB,KACd,QAAhC3D,EAAensC,MAAMkV,SACd9N,EAAA8T,UAAY60B,EAAa,EAAIA,EAAa,EACnD5D,EAAensC,MAAMgwC,eAAiB,WAE1D,CACA,CACA,CACYZ,EAAiB99C,QAAU8V,EAAS8T,uBAOhDg0B,GAAe56C,YAAcwT,GAC7B,IAAImoC,GAAa,eACZC,GAA4BC,IAAyB/K,GAAoB6K,IAC5Dx6C,EAAgBiE,YAChC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,KAAkBwK,GAAeh/C,EACnCi/C,EAAUj8B,KACO5iB,OAAAA,EAAGA,IAAC0+C,GAA4B,CAAE18C,MAAOoyC,EAAetxB,GAAI+7B,EAASp8C,SAA0BzC,EAAAA,IAAIoJ,EAAU0F,IAAK,CAAEwK,KAAM,QAAS,kBAAmBulC,KAAYD,EAAY//C,IAAKqF,SAGlMpB,YAAc27C,GAC1B,IAAIK,GAAa,cACbC,GAAc96C,EAAgBiE,YAChC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,KAAkB4K,GAAep/C,EACnCq/C,EAAeN,GAAsBG,GAAY1K,GACvD,aAA2BhrC,EAAU0F,IAAK,CAAEgU,GAAIm8B,EAAan8B,MAAOk8B,EAAYngD,IAAKqF,OAGzF66C,GAAYj8C,YAAcg8C,GAC1B,IAAII,GAAY,cACXC,GAA2BC,IAAwBxL,GAAoBsL,IACxEG,GAAap7C,EAAgBiE,YAC/B,CAACtI,EAAOsE,KACA,MAAAkwC,cACJA,EAAAzzC,MACAA,EAAAmhB,SACAA,GAAW,EACXw9B,UAAWC,KACRC,GACD5/C,EACE8C,EAAUsxC,GAAiBkL,GAAW9K,GACtCpS,EAAiBwW,GAAwB0G,GAAW9K,GACpDqL,EAAa/8C,EAAQ/B,QAAUA,GAC9B2+C,EAAWI,GAAgBp0C,EAAAA,SAAei0C,GAAiB,KAC3DI,EAAWC,GAAgBt0C,EAAAA,UAAe,GAC3CnD,EAAe9G,EACnB6C,GACCpD,UAAwB,OAAf,OAAe8B,EAAAo/B,EAAA2X,sBAAkB,EAAA/2C,EAAApD,KAAAwiC,EAAAlhC,EAAMH,EAAOmhB,MAEpD+9B,EAASj9B,KACT0zB,EAAiBpsC,EAAYpC,OAAC,SAC9Bg4C,EAAe,KACdh+B,IACHpf,EAAQ4xC,cAAc3zC,GACtB+B,EAAQ2X,cAAa,KAGzB,GAAc,KAAV1Z,EACF,MAAM,IAAIuC,MACR,yLAGJ,OAA0B2L,EAAA7O,IACxBm/C,GACA,CACEn9C,MAAOoyC,EACPzzC,QACAmhB,WACA+9B,SACAJ,aACAM,iBAAkBz+C,EAAAA,aAAmBR,IACnC4+C,GAAcM,GAAkBA,WAAkBl/C,WAAMof,cAAe,IAAInN,WAC1E,IACHtQ,SAA6BoM,EAAA7O,IAC3BgV,GAAWpM,SACX,CACE5G,MAAOoyC,EACPzzC,QACAmhB,WACAw9B,YACA78C,SAA6BoM,EAAA7O,IAC3BoJ,EAAU0F,IACV,CACEwK,KAAM,SACN,kBAAmBumC,EACnB,mBAAoBF,EAAY,QAAK,EACrC,gBAAiBF,GAAcE,EAC/B,aAAcF,EAAa,UAAY,YACvC,gBAAiB39B,QAAY,EAC7B,gBAAiBA,EAAW,QAAK,EACjCvI,SAAUuI,OAAW,GAAS,KAC3B09B,EACH3gD,IAAKsJ,EACL0R,QAASzZ,EAAqBo/C,EAAU3lC,SAAS,IAAM+lC,GAAa,KACpEK,OAAQ7/C,EAAqBo/C,EAAUS,QAAQ,IAAML,GAAa,KAClE5/B,QAAS5f,EAAqBo/C,EAAUx/B,SAAS,KAChB,UAA3Bs2B,EAAex2C,SAAmCggD,OAExD3hC,YAAa/d,EAAqBo/C,EAAUrhC,aAAa,KACxB,UAA3Bm4B,EAAex2C,SAAmCggD,OAExD7iC,cAAe7c,EAAqBo/C,EAAUviC,eAAgBzc,IAC5D81C,EAAex2C,QAAUU,EAAMqM,eAEjCwQ,cAAejd,EAAqBo/C,EAAUniC,eAAgB7c,UAC5D81C,EAAex2C,QAAUU,EAAMqM,YAC3BiV,EACF,OAAAlf,EAAAo/B,EAAemY,cAAfv3C,EAAApD,KAAAwiC,GACoC,UAA3BsU,EAAex2C,SACxBU,EAAMqa,cAAcvD,MAAM,CAAEwuB,eAAe,OAG/Coa,eAAgB9/C,EAAqBo/C,EAAUU,gBAAiB1/C,UAC1DA,EAAMqa,gBAAkBpP,SAASsM,gBACnC,OAAAnV,EAAAo/B,EAAemY,cAAfv3C,EAAApD,KAAAwiC,OAGJjlB,UAAW3c,EAAqBo/C,EAAUziC,WAAYvc,UACQ,MAAtC,OAAAoC,EAAAo/B,EAAeuU,gBAAf,EAAA3zC,EAA0B9C,UACb,MAAdU,EAAM5B,MACvB80C,GAAevhC,SAAS3R,EAAM5B,MAAoBkhD,IACpC,MAAdt/C,EAAM5B,KAAa4B,EAAM8N,8BAU/C+wC,GAAWv8C,YAAco8C,GACzB,IAAIiB,GAAiB,iBACjBC,GAAiBn8C,EAAgBiE,YACnC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,EAAejI,UAAAA,EAAA39B,MAAWA,KAAU6xC,GAAkBzgD,EACxD8C,EAAUsxC,GAAiBmM,GAAgB/L,GAC3CpS,EAAiBwW,GAAwB2H,GAAgB/L,GACzDkM,EAAclB,GAAqBe,GAAgB/L,GACnDmM,EAAuBrM,GAA8BiM,GAAgB/L,IACpEoM,EAAcC,GAAmBn1C,EAAAA,SAAe,MACjDnD,EAAe9G,EACnB6C,GACCpD,GAAS2/C,EAAgB3/C,IAC1Bw/C,EAAYP,kBACXj/C,UAAS,OAAA,OAAA8B,EAAAo/B,EAAe8X,0BAAf,EAAAl3C,EAAApD,KAAAwiC,EAAqClhC,EAAMw/C,EAAY3/C,MAAO2/C,EAAYx+B,aAEhF5B,EAA4B,MAAdsgC,OAAc,EAAAA,EAAAtgC,YAC5BwgC,EAAex+C,EAAaC,SAChC,IAAsBnC,EAAGA,IAAC,SAAU,CAAEW,MAAO2/C,EAAY3/C,MAAOmhB,SAAUw+B,EAAYx+B,SAAUrf,SAAUyd,GAAeogC,EAAY3/C,QACrI,CAAC2/C,EAAYx+B,SAAUw+B,EAAY3/C,MAAOuf,KAEtC21B,kBAAEA,EAAmBE,qBAAAA,GAAyBwK,EAKpD,OAJAxwC,GAAgB,KACd8lC,EAAkB6K,GACX,IAAM3K,EAAqB2K,KACjC,CAAC7K,EAAmBE,EAAsB2K,IAClB7xC,EAAA5O,KAACuc,WAAU,CAAE/Z,SAAU,CAChCzC,EAAAA,IAAIoJ,EAAU2L,KAAM,CAAE+N,GAAIw9B,EAAYT,UAAWQ,EAAexhD,IAAKsJ,IACrFm4C,EAAYb,YAAc/8C,EAAQmyC,YAAcnyC,EAAQqyC,qBAAuBr4B,EAAAA,aAAsB2jC,EAAc59C,SAAUC,EAAQmyC,WAAa,WAIxJuL,GAAet9C,YAAcq9C,GAC7B,IAAIQ,GAAsB,sBACtBC,GAAsB38C,EAAgBiE,YACxC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,KAAkByM,GAAuBjhD,EAEjD,OADoBw/C,GAAqBuB,GAAqBvM,GAC3CqL,iBAAiCr2C,EAAU2L,KAAM,CAAE,eAAe,KAAS8rC,EAAoBhiD,IAAKqF,IAAkB,QAG7I08C,GAAoB99C,YAAc69C,GAClC,IAAIG,GAAwB,uBACxBC,GAAuB98C,EAAgBiE,YAAC,CAACtI,EAAOsE,KAClD,MAAM89B,EAAiBwW,GAAwBsI,GAAuBlhD,EAAMw0C,eACtEuJ,EAAkBF,GAAyBqD,GAAuBlhD,EAAMw0C,gBACvE4M,EAAaC,GAAkB31C,EAAAA,UAAe,GAC/CnD,EAAe9G,EAAgB6C,EAAcy5C,EAAgBL,sBAcnE,OAbAvtC,GAAgB,KACV,GAAAiyB,EAAepsB,UAAYosB,EAAerD,aAAc,CAC1D,IAAIuiB,EAAgB,WACZ,MAAAC,EAAevrC,EAAS8T,UAAY,EAC1Cu3B,EAAeE,EAChB,EAED,MAAMvrC,EAAWosB,EAAepsB,SAGhC,OAFesrC,IACNtrC,EAAA7I,iBAAiB,SAAUm0C,GAC7B,IAAMtrC,EAAS9I,oBAAoB,SAAUo0C,EAC1D,IACK,CAAClf,EAAepsB,SAAUosB,EAAerD,eACrCqiB,EAAiCnyC,EAAA7O,IACtCohD,GACA,IACKxhD,EACHf,IAAKsJ,EACLk5C,aAAc,KACN,MAAAzrC,SAAEA,EAAU8iC,aAAAA,GAAiB1W,EAC/BpsB,GAAY8iC,IACL9iC,EAAA8T,UAAY9T,EAAS8T,UAAYgvB,EAAavtB,iBAI3D,QAEN41B,GAAqBj+C,YAAcg+C,GACnC,IAAIQ,GAA0B,yBAC1BC,GAAyBt9C,EAAgBiE,YAAC,CAACtI,EAAOsE,KACpD,MAAM89B,EAAiBwW,GAAwB8I,GAAyB1hD,EAAMw0C,eACxEuJ,EAAkBF,GAAyB6D,GAAyB1hD,EAAMw0C,gBACzEoN,EAAeC,GAAoBn2C,EAAAA,UAAe,GACnDnD,EAAe9G,EAAgB6C,EAAcy5C,EAAgBL,sBAenE,OAdAvtC,GAAgB,KACV,GAAAiyB,EAAepsB,UAAYosB,EAAerD,aAAc,CAC1D,IAAIuiB,EAAgB,WACZ,MAAAQ,EAAY9rC,EAASkY,aAAelY,EAAS8X,aAC7Ci0B,EAAiBlkC,KAAKmkC,KAAKhsC,EAAS8T,WAAag4B,EACvDD,EAAiBE,EAClB,EAED,MAAM/rC,EAAWosB,EAAepsB,SAGhC,OAFesrC,IACNtrC,EAAA7I,iBAAiB,SAAUm0C,GAC7B,IAAMtrC,EAAS9I,oBAAoB,SAAUo0C,EAC1D,IACK,CAAClf,EAAepsB,SAAUosB,EAAerD,eACrC6iB,EAAmC3yC,EAAA7O,IACxCohD,GACA,IACKxhD,EACHf,IAAKsJ,EACLk5C,aAAc,KACN,MAAAzrC,SAAEA,EAAU8iC,aAAAA,GAAiB1W,EAC/BpsB,GAAY8iC,IACL9iC,EAAA8T,UAAY9T,EAAS8T,UAAYgvB,EAAavtB,iBAI3D,QAENo2B,GAAuBz+C,YAAcw+C,GACrC,IAAIF,GAAyBn9C,EAAgBiE,YAAC,CAACtI,EAAOsE,KACpD,MAAMkwC,cAAEA,EAAAiN,aAAeA,KAAiBQ,GAAyBjiD,EAC3DoiC,EAAiBwW,GAAwB,qBAAsBpE,GAC/D0N,EAAqB53C,EAAYpC,OAAC,MAClC+O,EAAW5B,GAAcm/B,GACzB2N,EAAuBzgD,EAAAA,aAAkB,KACV,OAA/BwgD,EAAmBhiD,UACd4J,OAAAs4C,cAAcF,EAAmBhiD,SACxCgiD,EAAmBhiD,QAAU,QAE9B,IAQH,OAPAqK,EAAAA,WAAgB,IACP,IAAM43C,KACZ,CAACA,IACJhyC,GAAgB,WACR,MAAAkyC,EAAaprC,IAAWrS,MAAM4gC,GAASA,EAAKvmC,IAAIiB,UAAY2L,SAASsM,gBAC3E,OAAAnV,EAAA,MAAAq/C,OAAA,EAAAA,EAAYpjD,IAAIiB,UAAhB8C,EAAyBw2C,eAAe,CAAEC,MAAO,cAChD,CAACxiC,IACsBhI,EAAA7O,IACxBoJ,EAAU0F,IACV,CACE,eAAe,KACZ+yC,EACHhjD,IAAKqF,EACLsK,MAAO,CAAE0zC,WAAY,KAAML,EAAqBrzC,OAChDyO,cAAe7c,EAAqByhD,EAAqB5kC,eAAe,KACnC,OAA/B6kC,EAAmBhiD,UACrBgiD,EAAmBhiD,QAAU4J,OAAOy4C,YAAYd,EAAc,QAGlEhkC,cAAejd,EAAqByhD,EAAqBxkC,eAAe,WACtE,OAAAza,EAAAo/B,EAAemY,cAAfv3C,EAAApD,KAAAwiC,GACmC,OAA/B8f,EAAmBhiD,UACrBgiD,EAAmBhiD,QAAU4J,OAAOy4C,YAAYd,EAAc,QAGlEnB,eAAgB9/C,EAAqByhD,EAAqB3B,gBAAgB,KAClD6B,YAM1BK,GAAkBn+C,EAAgBiE,YACpC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,KAAkBiO,GAAmBziD,EACtBI,OAAAA,EAAGA,IAACoJ,EAAU0F,IAAK,CAAE,eAAe,KAASuzC,EAAgBxjD,IAAKqF,OAG7Fk+C,GAAgBt/C,YAPK,kBAQrB,IAAI++B,GAAa,cACC59B,EAAgBiE,YAChC,CAACtI,EAAOsE,KACN,MAAMkwC,cAAEA,KAAkB7Y,GAAe37B,EACnC80C,EAAcZ,GAAeM,GAC7B1xC,EAAUsxC,GAAiBnS,GAAYuS,GACvCpS,EAAiBwW,GAAwB3W,GAAYuS,GAC3D,OAAO1xC,EAAQwX,MAAoC,WAA5B8nB,EAAe5tB,WAAwCpU,IAAIsiD,GAAuB,IAAK5N,KAAgBnZ,EAAY18B,IAAKqF,IAAkB,QAGzJpB,YAAc++B,GAC1B,IACIoU,GAAoBhyC,EAAgBiE,YACtC,EAAGksC,gBAAezzC,WAAUf,GAASsE,KAC7B,MAAArF,EAAMqL,EAAYpC,OAAC,MACnBK,EAAe9G,EAAgB6C,EAAcrF,GAC7C0jD,EAAY9b,GAAY9lC,GAgB9B,OAfAwJ,EAAAA,WAAgB,KACd,MAAMo6B,EAAS1lC,EAAIiB,QACnB,IAAKykC,EAAQ,OACP,MAAAie,EAAc94C,OAAO+4C,kBAAkBnkD,UAKvCmV,EAJapV,OAAOmH,yBACxBg9C,EACA,SAE0B95C,IACxB,GAAA65C,IAAc5hD,GAAS8S,EAAU,CACnC,MAAMjT,EAAQ,IAAIkiD,MAAM,SAAU,CAAEvzC,SAAS,IACpCsE,EAAAjU,KAAK+kC,EAAQ5jC,GACtB4jC,EAAOz6B,cAActJ,EAC7B,IACO,CAAC+hD,EAAW5hD,IACWkO,EAAA7O,IACxBoJ,EAAUm7B,OACV,IACK3kC,EACH4O,MAAO,IAAK0F,KAA2BtU,EAAM4O,OAC7C3P,IAAKsJ,EACLqhC,aAAc7oC,OAMtB,SAAS02C,GAAsB12C,GACtB,MAAU,KAAVA,QAA0B,IAAVA,CACzB,CACA,SAAS+1C,GAAmBiM,GACpB,MAAAC,EAAqB74C,EAAe44C,GACpCpM,EAAYrsC,EAAYpC,OAAC,IACzB+6C,EAAW34C,EAAYpC,OAAC,GACxB0uC,EAAwBl1C,EAAiBC,aAC5C3C,IACO,MAAA+3C,EAASJ,EAAUz2C,QAAUlB,EACnCgkD,EAAmBjM,GAClB,SAASmM,EAAaniD,GACrB41C,EAAUz2C,QAAUa,EACb+I,OAAAyD,aAAa01C,EAAS/iD,SACf,KAAVa,IAAckiD,EAAS/iD,QAAU4J,OAAOwD,YAAW,IAAM41C,EAAa,KAAK,MAHhF,CAIEnM,KAEL,CAACiM,IAEGnM,EAAiBn1C,EAAAA,aAAkB,KACvCi1C,EAAUz2C,QAAU,GACb4J,OAAAyD,aAAa01C,EAAS/iD,WAC5B,IAII,OAHPqK,EAAAA,WAAgB,IACP,IAAMT,OAAOyD,aAAa01C,EAAS/iD,UACzC,IACI,CAACy2C,EAAWC,EAAuBC,EAC5C,CACA,SAASM,GAAa5R,EAAOwR,EAAQE,GACnC,MACMkM,EADapM,EAAOv1C,OAAS,GAAK2H,MAAMC,KAAK2tC,GAAQv/B,OAAO4rC,GAASA,IAASrM,EAAO,KACrDA,EAAO,GAAKA,EAC5CsM,EAAmBpM,EAAc1R,EAAMh8B,QAAQ0tC,IAAe,EACpE,IAAIqM,GAQa5c,EARYnB,EAQLge,EARY1lC,KAAKE,IAAIslC,EAAkB,GASxD3c,EAAMrlC,KAAI,CAACmiD,EAAG7gD,IAAU+jC,GAAO6c,EAAa5gD,GAAS+jC,EAAMllC,WADpE,IAAmBklC,EAAO6c,EAP+B,IAA5BJ,EAAiB3hD,SACL8hD,EAAAA,EAAa38C,QAAQ+c,GAAMA,IAAMuzB,KACxE,MAAMC,EAAWoM,EAAa1+C,MAC3B4gC,GAASA,EAAKka,UAAUz3B,cAAcw7B,WAAWN,EAAiBl7B,iBAE9D,OAAAivB,IAAaD,EAAcC,OAAW,CAC/C,CAxCAb,GAAkBnzC,YAhCM,oBA4ErB,IAACwf,GAAQ6xB,GACRmP,GAAUnN,GACVoN,GAAQ/L,GACRgM,GAAO5L,GACPnoC,GAASqoC,GACT2L,GAAWzL,GACX31B,GAAWq7B,GAEXgG,GAAQ3E,GACR4E,GAAOtE,GACPuE,GAAWxD,GACXyD,GAAgBjD,GAChBkD,GAAiB/C,GACjBgD,GAAmBxC,GACnByC,GAAY5B,GCnnCZ6B,GAAc,UACbC,GAAqBC,IAAqB3iD,EAAmByiD,KAC7DG,GAAgBC,IAAoBH,GAAoBD,IACzDK,GAAU1kD,IACN,MAAA2kD,cACJA,EAAA9hD,SACAA,EACAyX,KAAMC,EAAAC,YACNA,EAAAC,aACAA,EAAAmqC,MACAA,GAAQ,GACN5kD,EACE6kD,EAAav6C,EAAYpC,OAAC,MAC1B48C,EAAax6C,EAAYpC,OAAC,OACzBoS,EAAMK,GAAWtH,EAAqB,CAC3CC,KAAMiH,EACNhH,YAAaiH,IAAe,EAC5BhH,SAAUiH,EACVhH,OAAQ4wC,KAEV,OAA0Bp1C,EAAA7O,IACxBokD,GACA,CACEpiD,MAAOuiD,EACPE,aACAC,aACA9O,UAAWhzB,KACX+hC,QAAS/hC,KACTgiC,cAAehiC,KACf1I,OACAG,aAAcE,EACdsqC,aAAcvjD,EAAAA,aAAkB,IAAMiZ,GAASuqC,IAAcA,KAAW,CAACvqC,IACzEiqC,QACA/hD,cAIN6hD,GAAOxhD,YAAcmhD,GACrB,IAAI/N,GAAe,gBACCjyC,EAAgBiE,YAClC,CAACtI,EAAOsE,KACN,MAAMqgD,cAAEA,KAAkBnO,GAAiBx2C,EACrC8C,EAAU2hD,GAAiBnO,GAAcqO,GACzCQ,EAAqB1jD,EAAgB6C,EAAcxB,EAAQ+hD,YACjE,OAA0B51C,EAAA7O,IACxBoJ,EAAU8T,OACV,CACEvd,KAAM,SACN,gBAAiB,SACjB,gBAAiB+C,EAAQwX,KACzB,gBAAiBxX,EAAQkzC,UACzB,aAAcoP,GAAStiD,EAAQwX,SAC5Bk8B,EACHv3C,IAAKkmD,EACL/kC,QAAS5f,EAAqBR,EAAMogB,QAAStd,EAAQmiD,mBAK/C/hD,YAAcozC,GAC5B,IAAI+O,GAAc,gBACbC,GAAgBC,IAAoBjB,GAAoBe,GAAa,CACxEhrC,gBAAY,IAEVmrC,GAAgBxlD,IAClB,MAAM2kD,cAAEA,EAAAtqC,WAAeA,EAAYxX,SAAAA,EAAAiN,UAAUA,GAAc9P,EACrD8C,EAAU2hD,GAAiBY,GAAaV,GAC9C,SAA0BvkD,IAACklD,GAAgB,CAAEljD,MAAOuiD,EAAetqC,aAAYxX,SAAU4B,EAAAA,SAAepD,IAAIwB,GAAWmC,GAA0B5E,EAAGA,IAACkQ,EAAU,CAAEC,QAAS8J,GAAcvX,EAAQwX,KAAMzX,SAA6BoM,EAAA7O,IAAC+3C,EAAiB,CAAExuC,SAAS,EAAMmG,YAAWjN,SAAUmC,WAE7RwgD,GAAatiD,YAAcmiD,GAC3B,IAAII,GAAe,gBACfC,GAAgBrhD,EAAgBiE,YAClC,CAACtI,EAAOsE,KACN,MAAMqhD,EAAgBJ,GAAiBE,GAAczlD,EAAM2kD,gBACrDtqC,WAAEA,EAAasrC,EAActrC,cAAeurC,GAAiB5lD,EAC7D8C,EAAU2hD,GAAiBgB,GAAczlD,EAAM2kD,eAC9C,OAAA7hD,EAAQ8hD,YAA4Bt0C,EAAU,CAAEC,QAAS8J,GAAcvX,EAAQwX,KAAMzX,eAA8BgjD,GAAmB,IAAKD,EAAc3mD,IAAKqF,MAAqB,QAG9LohD,GAAcxiD,YAAcuiD,GAC5B,IAAIlgD,KAAkB,8BAClBsgD,GAAoBxhD,EAAgBiE,YACtC,CAACtI,EAAOsE,KACN,MAAMqgD,cAAEA,KAAkBiB,GAAiB5lD,EACrC8C,EAAU2hD,GAAiBgB,GAAcd,GAC/C,OAGkBvkD,EAAAA,IAAI2rC,GAAc,CAAEe,GAAIvnC,GAAMsnC,gBAAgB,EAAMJ,OAAQ,CAAC3pC,EAAQgiD,YAAajiD,SAA6BoM,EAAA7O,IAC7HoJ,EAAU0F,IACV,CACE,aAAck2C,GAAStiD,EAAQwX,SAC5BsrC,EACH3mD,IAAKqF,EACLsK,MAAO,CAAEC,cAAe,UAAW+2C,EAAah3C,cAMtDwuB,GAAe,gBACf0oB,GAAgBzhD,EAAgBiE,YAClC,CAACtI,EAAOsE,KACN,MAAMqhD,EAAgBJ,GAAiBnoB,GAAcp9B,EAAM2kD,gBACrDtqC,WAAEA,EAAasrC,EAActrC,cAAe+jB,GAAiBp+B,EAC7D8C,EAAU2hD,GAAiBrnB,GAAcp9B,EAAM2kD,eACrD,aAA2Br0C,EAAU,CAAEC,QAAS8J,GAAcvX,EAAQwX,KAAMzX,SAAUC,EAAQ8hD,MAAwBxkD,EAAGA,IAAC2lD,GAAoB,IAAK3nB,EAAcn/B,IAAKqF,MAAkClE,IAAI4lD,GAAuB,IAAK5nB,EAAcn/B,IAAKqF,SAG/PwhD,GAAc5iD,YAAck6B,GAC5B,IAAI2oB,GAAqB1hD,EAAgBiE,YACvC,CAACtI,EAAOsE,KACN,MAAMxB,EAAU2hD,GAAiBrnB,GAAcp9B,EAAM2kD,eAC/CG,EAAax6C,EAAYpC,OAAC,MAC1BK,EAAe9G,EAAgB6C,EAAcxB,EAAQgiD,WAAYA,GAKvE,OAJAv6C,EAAAA,WAAgB,KACd,MAAM8zB,EAAUymB,EAAW5kD,QACvB,GAAAm+B,EAAgB,OAAAqK,GAAWrK,KAC9B,IACuBpvB,EAAA7O,IACxB6lD,GACA,IACKjmD,EACHf,IAAKsJ,EACL29C,UAAWpjD,EAAQwX,KACnBpP,6BAA6B,EAC7B2tC,iBAAkBr4C,EAAqBR,EAAM64C,kBAAmBj4C,UAC9DA,EAAM8N,iBACN,OAAQ1L,EAAAF,EAAA+hD,WAAW3kD,UAAS8C,EAAA0U,WAE9BtM,qBAAsB5K,EAAqBR,EAAMoL,sBAAuBxK,IAChE,MAAAoM,EAAgBpM,EAAM0O,OAAOtC,cAC7Bm5C,EAAyC,IAAzBn5C,EAAcsQ,SAA0C,IAA1BtQ,EAAc+L,SACpB,IAAzB/L,EAAcsQ,QAAgB6oC,MAC3Bz3C,oBAE1BrD,eAAgB7K,EACdR,EAAMqL,gBACLzK,GAAUA,EAAM8N,wBAMvBs3C,GAAwB3hD,EAAgBiE,YAC1C,CAACtI,EAAOsE,KACN,MAAMxB,EAAU2hD,GAAiBrnB,GAAcp9B,EAAM2kD,eAC/CyB,EAA0B97C,EAAYpC,QAAC,GACvCm+C,EAA2B/7C,EAAYpC,QAAC,GAC9C,OAA0B+G,EAAA7O,IACxB6lD,GACA,IACKjmD,EACHf,IAAKqF,EACL4hD,WAAW,EACXh7C,6BAA6B,EAC7B2tC,iBAAmBj4C,YACjB,OAAAoC,EAAAhD,EAAM64C,mBAAmB71C,EAAApD,KAAAI,EAAAY,GACpBA,EAAMC,mBACJulD,EAAwBlmD,SAAS,OAAQ8F,EAAAlD,EAAA+hD,WAAW3kD,UAAS8F,EAAA0R,QAClE9W,EAAM8N,kBAER03C,EAAwBlmD,SAAU,EAClCmmD,EAAyBnmD,SAAU,GAErCoL,kBAAoB1K,YAClB,OAAAoC,EAAAhD,EAAMsL,oBAAoBtI,EAAApD,KAAAI,EAAAY,GACrBA,EAAMC,mBACTulD,EAAwBlmD,SAAU,EACM,gBAApCU,EAAM0O,OAAOtC,cAAcjN,OAC7BsmD,EAAyBnmD,SAAU,IAGvC,MAAM8J,EAASpJ,EAAMoJ,QACG,OAAAhE,EAAAlD,EAAQ+hD,WAAW3kD,kBAAS2N,SAAS7D,OAClC0E,iBACa,YAApC9N,EAAM0O,OAAOtC,cAAcjN,MAAsBsmD,EAAyBnmD,SAC5EU,EAAM8N,uBAOdu3C,GAAoB5hD,EAAgBiE,YACtC,CAACtI,EAAOsE,KACN,MAAMqgD,cAAEA,EAAeuB,UAAAA,EAAAI,gBAAWA,mBAAiBzN,KAAqBza,GAAiBp+B,EACnF8C,EAAU2hD,GAAiBrnB,GAAcunB,GACzCG,EAAax6C,EAAYpC,OAAC,MAC1BK,EAAe9G,EAAgB6C,EAAcwgD,GAEnD,OADgB7hB,KACWh0B,EAAA5O,KAACuc,WAAU,CAAE/Z,SAAU,CAC7BoM,EAAA7O,IACjBwjC,GACA,CACEj6B,SAAS,EACTk6B,MAAM,EACNC,QAASoiB,EACTniB,iBAAkBuiB,EAClBriB,mBAAoB4U,EACpBh2C,SAA6BoM,EAAA7O,IAC3B6K,EACA,CACEyO,KAAM,SACNwJ,GAAIpgB,EAAQkzC,UACZ,mBAAoBlzC,EAAQkiD,cAC5B,kBAAmBliD,EAAQiiD,QAC3B,aAAcK,GAAStiD,EAAQwX,SAC5B8jB,EACHn/B,IAAKsJ,EACLgD,UAAW,IAAMzI,EAAQ2X,cAAa,cAKzBmC,EAAAA,SAAU,CAAE/Z,SAAU,GACzBzC,IAAImmD,GAAc,CAAExB,QAASjiD,EAAQiiD,UACrC3kD,EAAGA,IAAComD,GAAoB,CAAE1B,aAAYE,cAAeliD,EAAQkiD,yBAKjFyB,GAAa,cACbC,GAAcriD,EAAgBiE,YAChC,CAACtI,EAAOsE,KACN,MAAMqgD,cAAEA,KAAkBllC,GAAezf,EACnC8C,EAAU2hD,GAAiBgC,GAAY9B,GAC7C,aAA2Bn7C,EAAUm9C,GAAI,CAAEzjC,GAAIpgB,EAAQiiD,WAAYtlC,EAAYxgB,IAAKqF,OAGxFoiD,GAAYxjD,YAAcujD,GAC1B,IAAIG,GAAmB,oBACnBC,GAAoBxiD,EAAgBiE,YACtC,CAACtI,EAAOsE,KACN,MAAMqgD,cAAEA,KAAkBhlC,GAAqB3f,EACzC8C,EAAU2hD,GAAiBmC,GAAkBjC,GACnD,aAA2Bn7C,EAAUzK,EAAG,CAAEmkB,GAAIpgB,EAAQkiD,iBAAkBrlC,EAAkB1gB,IAAKqF,OAGnGuiD,GAAkB3jD,YAAc0jD,GAChC,IAAI3mC,GAAa,cACb6mC,GAAcziD,EAAgBiE,YAChC,CAACtI,EAAOsE,KACN,MAAMqgD,cAAEA,KAAkBzkC,GAAelgB,EACnC8C,EAAU2hD,GAAiBxkC,GAAY0kC,GAC7C,OAA0B11C,EAAA7O,IACxBoJ,EAAU8T,OACV,CACEvd,KAAM,YACHmgB,EACHjhB,IAAKqF,EACL8b,QAAS5f,EAAqBR,EAAMogB,SAAS,IAAMtd,EAAQ2X,cAAa,UAMhF,SAAS2qC,GAAS9qC,GAChB,OAAOA,EAAO,OAAS,QACzB,CAHAwsC,GAAY5jD,YAAc+c,GAI1B,IAAI8mC,GAAqB,sBACpBC,GAAiBC,IjDrRtB,SAAwBzkD,EAAmBN,GACnC,MAAAa,EAAUZ,EAAmBO,cAACR,GAC9BU,EAAY5C,IAChB,MAAM6C,SAAEA,KAAaC,GAAY9C,EAC3Be,EAAQuB,EAAAA,SAAc,IAAMQ,GAASrE,OAAOwE,OAAOH,IACzD,SAAuB1C,IAAI2C,EAAQH,SAAU,CAAE7B,QAAO8B,cASjD,OAPPD,EAASM,YAAcV,EAAoB,WAOpC,CAACI,EANR,SAAqBO,GACb,MAAAL,EAAUM,EAAgBC,WAACN,GACjC,GAAID,EAAgB,OAAAA,EAChB,QAAmB,IAAnBZ,EAAkC,OAAAA,EACtC,MAAM,IAAIoB,MAAM,KAAKH,6BAAwCX,MACjE,EAEA,CiDsQ2CE,CAAcqkD,GAAoB,CAC3EG,YAAa9pB,GACb+pB,UAAWV,GACXW,SAAU,WAERb,GAAe,EAAGxB,cACd,MAAAsC,EAAsBJ,GAAkBF,IACxCO,EAAU,KAAKD,EAAoBH,8BAA8BG,EAAoBF,wGAEjEE,EAAoBF,gJAE4BE,EAAoBD,WAOvF,OANP78C,EAAAA,WAAgB,KACd,GAAIw6C,EAAS,CACMl5C,SAAS07C,eAAexC,EAE/C,IACK,CAACuC,EAASvC,IACN,MAGLyB,GAAqB,EAAG1B,aAAYE,oBAChC,MACAsC,EAAU,6EADkBL,GAFL,4BAG0FC,gBAQhH,OAPP38C,EAAAA,WAAgB,WACd,MAAMi9C,EAAgB,OAAAxkD,EAAA8hD,EAAW5kD,cAAX,EAAA8C,EAAoBolC,aAAa,oBACvD,GAAI4c,GAAiBwC,EAAe,CACX37C,SAAS07C,eAAevC,EAErD,IACK,CAACsC,EAASxC,EAAYE,IAClB,MAELv1C,GAAOi1C,GAEP70C,GAAS21C,GACTiC,GAAU/B,GACV9iB,GAAUkjB,GACVnjC,GAAQ+jC,GACR9jC,GAAcikC,GACd/jC,GAAQgkC", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53]}