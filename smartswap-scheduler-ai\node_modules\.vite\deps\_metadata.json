{"hash": "631a92f4", "configHash": "ec1ed2cc", "lockfileHash": "d9a4c3dd", "browserHash": "65388a43", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "125450b9", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "869bf17b", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "ba2acd30", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "b755daba", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "08f20f1c", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "faa79025", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "ff6d7091", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "b<PERSON><PERSON>b", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "be9a0f31", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "791141e7", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "7c6389b4", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "6ad0efb9", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "a1ef7c76", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../../../node_modules/@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "423648e8", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "5c6c6b38", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../../../node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "8210ac35", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "b619d179", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "3cab5351", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "11a6acc1", "needsInterop": false}, "next-themes": {"src": "../../../../node_modules/next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "2d496efe", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0d1c1da0", "needsInterop": true}, "react-hook-form": {"src": "../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "7f27037c", "needsInterop": false}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6454e66e", "needsInterop": true}, "recharts": {"src": "../../../../node_modules/recharts/es6/index.js", "file": "recharts.js", "fileHash": "e0b14d39", "needsInterop": false}, "sonner": {"src": "../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "b53790a9", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "555d0425", "needsInterop": false}}, "chunks": {"chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-2ZRVDRKD": {"file": "chunk-2ZRVDRKD.js"}, "chunk-E7H7AXL6": {"file": "chunk-E7H7AXL6.js"}, "chunk-RWF7UEWW": {"file": "chunk-RWF7UEWW.js"}, "chunk-SXX54OC7": {"file": "chunk-SXX54OC7.js"}, "chunk-GXD7YJGF": {"file": "chunk-GXD7YJGF.js"}, "chunk-6RHS4UUI": {"file": "chunk-6RHS4UUI.js"}, "chunk-EMSPKVCK": {"file": "chunk-EMSPKVCK.js"}, "chunk-BBEI5V4X": {"file": "chunk-BBEI5V4X.js"}, "chunk-WFXZHWCA": {"file": "chunk-WFXZHWCA.js"}, "chunk-43RTQ54X": {"file": "chunk-43RTQ54X.js"}, "chunk-K6YFPJWF": {"file": "chunk-K6YFPJWF.js"}, "chunk-NB24HQGR": {"file": "chunk-NB24HQGR.js"}, "chunk-IBI677K4": {"file": "chunk-IBI677K4.js"}, "chunk-6AOXWRVX": {"file": "chunk-6AOXWRVX.js"}, "chunk-DLLDB44G": {"file": "chunk-DLLDB44G.js"}, "chunk-7QGA4JVC": {"file": "chunk-7QGA4JVC.js"}, "chunk-AVJPV5ZH": {"file": "chunk-AVJPV5ZH.js"}, "chunk-JYSI5OBP": {"file": "chunk-JYSI5OBP.js"}, "chunk-7URR3GLA": {"file": "chunk-7URR3GLA.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}