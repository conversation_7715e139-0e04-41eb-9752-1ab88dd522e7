import { formatDistance } from "./nn/_lib/formatDistance.mjs";
import { formatLong } from "./nn/_lib/formatLong.mjs";
import { formatRelative } from "./nn/_lib/formatRelative.mjs";
import { localize } from "./nn/_lib/localize.mjs";
import { match } from "./nn/_lib/match.mjs";

/**
 * @category Locales
 * @summary Norwegian Nynorsk locale.
 * @language Norwegian Nynorsk
 * @iso-639-2 nno
 * <AUTHOR> [@draperunner](https://github.com/draperunner)
 */
export const nn = {
  code: "nn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default nn;
