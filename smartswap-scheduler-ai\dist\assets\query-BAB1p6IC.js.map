{"version": 3, "file": "query-BAB1p6IC.js", "sources": ["../../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../../node_modules/@tanstack/query-core/build/modern/query.js", "../../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../../node_modules/@tanstack/query-core/build/modern/queryClient.js", "../../../node_modules/@tanstack/query-core/build/modern/queryObserver.js", "../../../node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "../../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js", "../../../node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js", "../../../node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js", "../../../node_modules/@tanstack/react-query/build/modern/suspense.js", "../../../node_modules/@tanstack/react-query/build/modern/useBaseQuery.js", "../../../node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js", "../../../node_modules/@tanstack/react-query/build/modern/useQuery.js", "../../../node_modules/@tanstack/react-query/build/modern/useMutation.js"], "sourcesContent": ["// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\nexport {\n  Subscribable\n};\n//# sourceMappingURL=subscribable.js.map", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport {\n  addToEnd,\n  addToStart,\n  ensureQueryFn,\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  isPlainArray,\n  isPlainObject,\n  isServer,\n  isValidTimeout,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceData,\n  replaceEqualDeep,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  shouldThrowError,\n  skipToken,\n  sleep,\n  timeUntilStale\n};\n//# sourceMappingURL=utils.js.map", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport {\n  FocusManager,\n  focusManager\n};\n//# sourceMappingURL=focusManager.js.map", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport {\n  OnlineManager,\n  onlineManager\n};\n//# sourceMappingURL=onlineManager.js.map", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then((result) => {\n    data = result;\n    return result;\n  })?.catch(noop);\n  if (data !== void 0) {\n    return { data };\n  }\n  return void 0;\n}\nexport {\n  pendingThenable,\n  tryResolveSync\n};\n//# sourceMappingURL=thenable.js.map", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport {\n  CancelledError,\n  canFetch,\n  createRetryer,\n  isCancelledError\n};\n//# sourceMappingURL=retryer.js.map", "// src/notifyManager.ts\nvar defaultScheduler = (cb) => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport {\n  createNotifyManager,\n  defaultScheduler,\n  notifyManager\n};\n//# sourceMappingURL=notifyManager.js.map", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport {\n  Removable\n};\n//# sourceMappingURL=removable.js.map", "// src/query.ts\nimport {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  skipToken,\n  timeUntilStale\n} from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const queryFnContext = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(\n      context,\n      this\n    );\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport {\n  Query,\n  fetchState\n};\n//# sourceMappingURL=query.js.map", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({ type: \"continue\" });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport {\n  Mutation,\n  getDefaultState\n};\n//# sourceMappingURL=mutation.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const queryFnContext = {\n            client: context.client,\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? \"backward\" : \"forward\",\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const page = await queryFn(\n            queryFnContext\n          );\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map", "// src/queryObserver.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { fetchState } from \"./query.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  shallowEqualObjects,\n  timeUntilStale\n} from \"./utils.js\";\nvar QueryObserver = class extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = pendingThenable();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error(\"experimental_prefetchInRender feature flag is not enabled\")\n      );\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof resolveEnabled(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || resolveStaleTime(this.options.staleTime, this.#currentQuery) !== resolveStaleTime(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = resolveStaleTime(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return;\n    }\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (isServer || resolveEnabled(this.options.enabled, this.#currentQuery) === false || !isValidTimeout(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = replaceData(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = replaceData(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable) => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = pendingThenable();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({ listeners: shouldNotifyListeners() });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (resolveEnabled(options.enabled, query) !== false) {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || resolveEnabled(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.isStaleByTime(resolveStaleTime(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\nexport {\n  QueryObserver\n};\n//# sourceMappingURL=queryObserver.js.map", "// src/mutationObserver.ts\nimport { getDefaultState } from \"./mutation.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { hashKey, shallowEqualObjects } from \"./utils.js\";\nvar MutationObserver = class extends Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? getDefaultState();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\nexport {\n  MutationObserver\n};\n//# sourceMappingURL=mutationObserver.js.map", "\"use client\";\n\n// src/QueryClientProvider.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar QueryClientContext = React.createContext(\n  void 0\n);\nvar useQueryClient = (queryClient) => {\n  const client = React.useContext(QueryClientContext);\n  if (queryClient) {\n    return queryClient;\n  }\n  if (!client) {\n    throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n  }\n  return client;\n};\nvar QueryClientProvider = ({\n  client,\n  children\n}) => {\n  React.useEffect(() => {\n    client.mount();\n    return () => {\n      client.unmount();\n    };\n  }, [client]);\n  return /* @__PURE__ */ jsx(QueryClientContext.Provider, { value: client, children });\n};\nexport {\n  QueryClientContext,\n  QueryClientProvider,\n  useQueryClient\n};\n//# sourceMappingURL=QueryClientProvider.js.map", "\"use client\";\n\n// src/IsRestoringProvider.ts\nimport * as React from \"react\";\nvar IsRestoringContext = React.createContext(false);\nvar useIsRestoring = () => React.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\nexport {\n  IsRestoringProvider,\n  useIsRestoring\n};\n//# sourceMappingURL=IsRestoringProvider.js.map", "\"use client\";\n\n// src/QueryErrorResetBoundary.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createValue() {\n  let isReset = false;\n  return {\n    clearReset: () => {\n      isReset = false;\n    },\n    reset: () => {\n      isReset = true;\n    },\n    isReset: () => {\n      return isReset;\n    }\n  };\n}\nvar QueryErrorResetBoundaryContext = React.createContext(createValue());\nvar useQueryErrorResetBoundary = () => React.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({\n  children\n}) => {\n  const [value] = React.useState(() => createValue());\n  return /* @__PURE__ */ jsx(QueryErrorResetBoundaryContext.Provider, { value, children: typeof children === \"function\" ? children(value) : children });\n};\nexport {\n  QueryErrorResetBoundary,\n  useQueryErrorResetBoundary\n};\n//# sourceMappingURL=QueryErrorResetBoundary.js.map", "// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  const originalStaleTime = defaultedOptions.staleTime;\n  if (defaultedOptions.suspense) {\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => Math.max(originalStaleTime(...args), 1e3) : Math.max(originalStaleTime ?? 1e3, 1e3);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\nexport {\n  defaultThrowOnError,\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n};\n//# sourceMappingURL=suspense.js.map", "\"use client\";\n\n// src/useBaseQuery.ts\nimport * as React from \"react\";\nimport { isServer, noop, notify<PERSON>anager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nimport { useQueryErrorResetBoundary } from \"./QueryErrorResetBoundary.js\";\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n} from \"./errorBoundaryUtils.js\";\nimport { useIsRestoring } from \"./IsRestoringProvider.js\";\nimport {\n  ensureSuspenseTimers,\n  fetchOptimistic,\n  shouldSuspend,\n  willFetch\n} from \"./suspense.js\";\nfunction useBaseQuery(options, Observer, queryClient) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof options !== \"object\" || Array.isArray(options)) {\n      throw new Error(\n        'Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object'\n      );\n    }\n  }\n  const client = useQueryClient(queryClient);\n  const isRestoring = useIsRestoring();\n  const errorResetBoundary = useQueryErrorResetBoundary();\n  const defaultedOptions = client.defaultQueryOptions(options);\n  client.getDefaultOptions().queries?._experimental_beforeQuery?.(\n    defaultedOptions\n  );\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!defaultedOptions.queryFn) {\n      console.error(\n        `[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`\n      );\n    }\n  }\n  defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n  ensureSuspenseTimers(defaultedOptions);\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary);\n  useClearResetErrorBoundary(errorResetBoundary);\n  const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n  const [observer] = React.useState(\n    () => new Observer(\n      client,\n      defaultedOptions\n    )\n  );\n  const result = observer.getOptimisticResult(defaultedOptions);\n  const shouldSubscribe = !isRestoring && options.subscribed !== false;\n  React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = shouldSubscribe ? observer.subscribe(notifyManager.batchCalls(onStoreChange)) : noop;\n        observer.updateResult();\n        return unsubscribe;\n      },\n      [observer, shouldSubscribe]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  React.useEffect(() => {\n    observer.setOptions(defaultedOptions);\n  }, [defaultedOptions, observer]);\n  if (shouldSuspend(defaultedOptions, result)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary);\n  }\n  if (getHasError({\n    result,\n    errorResetBoundary,\n    throwOnError: defaultedOptions.throwOnError,\n    query: client.getQueryCache().get(defaultedOptions.queryHash),\n    suspense: defaultedOptions.suspense\n  })) {\n    throw result.error;\n  }\n  ;\n  client.getDefaultOptions().queries?._experimental_afterQuery?.(\n    defaultedOptions,\n    result\n  );\n  if (defaultedOptions.experimental_prefetchInRender && !isServer && willFetch(result, isRestoring)) {\n    const promise = isNewCacheEntry ? (\n      // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n      fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n    ) : (\n      // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n      client.getQueryCache().get(defaultedOptions.queryHash)?.promise\n    );\n    promise?.catch(noop).finally(() => {\n      observer.updateResult();\n    });\n  }\n  return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\nexport {\n  useBaseQuery\n};\n//# sourceMappingURL=useBaseQuery.js.map", "\"use client\";\n\n// src/errorBoundaryUtils.ts\nimport * as React from \"react\";\nimport { shouldThrowError } from \"@tanstack/query-core\";\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary) => {\n  if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false;\n    }\n  }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset();\n  }, [errorResetBoundary]);\n};\nvar getHasError = ({\n  result,\n  errorResetBoundary,\n  throwOnError,\n  query,\n  suspense\n}) => {\n  return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || shouldThrowError(throwOnError, [result.error, query]));\n};\nexport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary\n};\n//# sourceMappingURL=errorBoundaryUtils.js.map", "\"use client\";\n\n// src/useQuery.ts\nimport { QueryObserver } from \"@tanstack/query-core\";\nimport { useBaseQuery } from \"./useBaseQuery.js\";\nfunction useQuery(options, queryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient);\n}\nexport {\n  useQuery\n};\n//# sourceMappingURL=useQuery.js.map", "\"use client\";\n\n// src/useMutation.ts\nimport * as React from \"react\";\nimport {\n  MutationObserver,\n  noop,\n  notify<PERSON><PERSON><PERSON>,\n  shouldThrowError\n} from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction useMutation(options, queryClient) {\n  const client = useQueryClient(queryClient);\n  const [observer] = React.useState(\n    () => new MutationObserver(\n      client,\n      options\n    )\n  );\n  React.useEffect(() => {\n    observer.setOptions(options);\n  }, [observer, options]);\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer]\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult()\n  );\n  const mutate = React.useCallback(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop);\n    },\n    [observer]\n  );\n  if (result.error && shouldThrowError(observer.options.throwOnError, [result.error])) {\n    throw result.error;\n  }\n  return { ...result, mutate, mutateAsync: result.mutate };\n}\nexport {\n  useMutation\n};\n//# sourceMappingURL=useMutation.js.map"], "names": ["Subscribable", "constructor", "this", "listeners", "Set", "subscribe", "bind", "listener", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "globalThis", "noop", "isValidTimeout", "value", "Infinity", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "resolveStaleTime", "query", "resolveEnabled", "enabled", "matchQuery", "filters", "type", "exact", "fetchStatus", "predicate", "query<PERSON><PERSON>", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "status", "<PERSON><PERSON><PERSON>", "hash<PERSON><PERSON>", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "every", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aItems", "aSize", "length", "bItems", "bSize", "copy", "equalItems", "i", "includes", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "prot", "prototype", "hasOwnProperty", "getPrototypeOf", "toString", "call", "replaceData", "prevData", "data", "structuralSharing", "addToEnd", "items", "item", "newItems", "slice", "addToStart", "skipToken", "Symbol", "ensureQueryFn", "fetchOptions", "queryFn", "initialPromise", "Promise", "reject", "Error", "shouldThrowError", "throwOnError", "params", "focusManager", "_a", "super", "__privateAdd", "_focused", "_cleanup", "_setup", "__privateSet", "onFocus", "addEventListener", "removeEventListener", "__privateGet", "setEventListener", "setup", "focused", "setFocused", "isFocused", "for<PERSON>ach", "document", "visibilityState", "WeakMap", "onlineManager", "_b", "_online", "onOnline", "onlineListener", "offlineListener", "setOnline", "online", "isOnline", "pendingThenable", "resolve", "thenable", "_resolve", "_reject", "finalize", "assign", "catch", "reason", "defaultRetryDelay", "failureCount", "min", "canFetch", "networkMode", "CancelledError", "revert", "silent", "isCancelledError", "createRetryer", "config", "continueFn", "isRetryCancelled", "isResolved", "canContinue", "canRun", "canStart", "onSuccess", "onError", "pause", "continueResolve", "onPause", "then", "onContinue", "run", "promiseOrValue", "fn", "error", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "timeout", "onFail", "setTimeout", "promise", "cancel", "cancelOptions", "abort", "continue", "cancelRetry", "continueRetry", "start", "defaultScheduler", "cb", "notify<PERSON><PERSON>ger", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "scheduleFn", "schedule", "push", "batch", "originalQueue", "flush", "batchCalls", "args", "setNotifyFunction", "setBatchNotifyFunction", "setScheduler", "createNotifyManager", "Removable", "_c", "_gcTimeout", "destroy", "clearGcTimeout", "scheduleGc", "gcTime", "optionalRemove", "updateGcTime", "newGcTime", "clearTimeout", "Query", "_d", "_Query_instances", "_initialState", "_revertState", "_cache", "_client", "_retryer", "_defaultOptions", "_abortSignalConsumed", "defaultOptions", "setOptions", "observers", "client", "get<PERSON><PERSON><PERSON><PERSON>ache", "initialData", "hasData", "initialDataUpdatedAt", "dataUpdateCount", "dataUpdatedAt", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "fetchFailureReason", "fetchMeta", "isInvalidated", "getDefaultState", "meta", "remove", "setData", "newData", "__privateMethod", "manual", "setState", "setStateOptions", "reset", "some", "observer", "isDisabled", "getObserversCount", "getCurrentResult", "isStaleByTime", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "filter", "invalidate", "dispatch_fn", "fetch", "abortController", "AbortController", "addSignalProperty", "object", "defineProperty", "enumerable", "get", "signal", "context", "fetchFn", "queryFnContext", "persister", "behavior", "onFetch", "onSettled", "WeakSet", "action", "fetchState", "reducer", "onQueryUpdate", "Query<PERSON>ache", "_e", "_queries", "Map", "build", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "set", "queryInMap", "clear", "getAll", "values", "defaultedFilters", "findAll", "queries", "event", "Mutation", "_f", "_Mutation_instances", "_observers", "_mutationCache", "mutationId", "mutationCache", "failureReason", "isPaused", "variables", "submittedAt", "execute", "mutationFn", "restored", "onMutate", "_h", "_g", "_j", "_i", "_l", "_k", "_n", "_m", "_p", "_o", "_r", "_q", "_t", "_s", "runNext", "onMutationUpdate", "MutationCache", "_mutations", "_scopes", "_mutationId", "__privateWrapper", "defaultMutationOptions", "scope", "scopeFor", "scopedMutations", "index", "indexOf", "splice", "mutationsWithSameScope", "firstPendingMutation", "m", "foundMutation", "from", "resumePausedMutations", "pausedMutations", "all", "map", "id", "infiniteQueryBehavior", "pages", "direction", "fetchMore", "oldPages", "oldPageParams", "pageParams", "currentPage", "async", "cancelled", "fetchPage", "param", "previous", "pageParam", "aborted", "page", "maxPages", "addTo", "oldData", "getPreviousPageParam", "getNextPageParam", "remainingPages", "initialPageParam", "lastIndex", "QueryClient", "_queryCache", "_queryDefaults", "_mutationDefaults", "_mountCount", "_unsubscribeFocus", "_unsubscribeOnline", "queryCache", "mount", "unmount", "isFetching", "isMutating", "getQueryData", "ensureQueryData", "defaultedOptions", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "revalidateIfStale", "prefetch<PERSON><PERSON>y", "getQueriesData", "setQueryData", "updater", "input", "functionalUpdate", "setQueriesData", "getQueryState", "removeQueries", "resetQueries", "refetchQueries", "cancelQueries", "defaultedCancelOptions", "promises", "invalidateQueries", "refetchType", "fetchInfiniteQuery", "prefetchInfiniteQuery", "ensureInfiniteQueryData", "getMutationCache", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaults", "query<PERSON><PERSON><PERSON>", "setMutationDefaults", "getMutationDefaults", "_defaulted", "refetchOnReconnect", "suspense", "mutations", "QueryObserver", "_QueryObserver_instances", "_current<PERSON><PERSON>y", "_currentQueryInitialState", "_currentResult", "_currentResultState", "_currentResultOptions", "_currentThenable", "_selectError", "_selectFn", "_selectResult", "_lastQueryWithDefinedData", "_staleTimeoutId", "_refetchIntervalId", "_currentRefetchInterval", "_trackedProps", "experimental_prefetchInRender", "bindMethods", "shouldFetchOnMount", "executeFetch_fn", "updateResult", "updateTimers_fn", "shouldFetchOn", "refetchOnWindowFocus", "clearStaleTimeout_fn", "clearRefetchInterval_fn", "prevOptions", "prev<PERSON><PERSON><PERSON>", "updateQuery_fn", "mounted", "shouldFetchOptionally", "updateStaleTimeout_fn", "nextRefetchInterval", "computeRefetchInterval_fn", "getOptimisticResult", "createResult", "optimisticResult", "shouldAssignObserverCurrentProperties", "trackResult", "onPropTracked", "Proxy", "target", "trackProp", "Reflect", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchOptimistic", "prevResult", "prevResultState", "prevResultOptions", "queryInitialState", "newState", "isPlaceholderData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "skipSelect", "placeholderData", "select", "selectError", "isPending", "isError", "isLoading", "nextResult", "isSuccess", "isInitialLoading", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "finalizeThenableIfPossible", "recreateThenable", "pending", "prevThenable", "notify_fn", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "<PERSON><PERSON><PERSON>", "shouldNotifyListeners", "time", "refetchInterval", "updateRefetchInterval_fn", "nextInterval", "setInterval", "refetchIntervalInBackground", "clearInterval", "notifyOptions", "retryOnMount", "shouldLoadOnMount", "refetchOnMount", "field", "MutationObserver", "_MutationObserver_instances", "_currentMutation", "_mutateOptions", "updateResult_fn", "mutate", "isIdle", "QueryClientContext", "React.createContext", "createContext", "useQueryClient", "queryClient", "React.useContext", "useContext", "QueryClientProvider", "children", "React.useEffect", "jsx", "Provider", "IsRestoringContext", "QueryErrorResetBoundaryContext", "isReset", "clear<PERSON><PERSON>t", "createValue", "errorResetBoundary", "useBaseQuery", "Observer", "isRestoring", "_experimental_beforeQ<PERSON>y", "originalStaleTime", "ensureSuspenseTimers", "ensurePreventErrorBoundaryRetry", "useClearResetErrorBoundary", "isNewCacheEntry", "React.useState", "useState", "shouldSubscribe", "subscribed", "reactExports", "useSyncExternalStore", "React.useCallback", "useCallback", "onStoreChange", "unsubscribe", "shouldSuspend", "getHasError", "_experimental_afterQuery", "<PERSON><PERSON><PERSON><PERSON>", "finally", "useQuery", "useMutation", "React.useSyncExternalStore", "mutateOptions", "mutateAsync"], "mappings": "yrBACA,IAAIA,GAAe,MACjB,WAAAC,GACOC,KAAAC,cAAgCC,IACrCF,KAAKG,UAAYH,KAAKG,UAAUC,KAAKJ,KACzC,CACE,SAAAG,CAAUE,GAGR,OAFKL,KAAAC,UAAUK,IAAID,GACnBL,KAAKO,cACE,KACAP,KAAAC,UAAUO,OAAOH,GACtBL,KAAKS,gBAEX,CACE,YAAAC,GACS,OAAAV,KAAKC,UAAUU,KAAO,CACjC,CACE,WAAAJ,GACF,CACE,aAAAE,GACF,GCnBIG,GAA6B,oBAAXC,QAA0B,SAAUC,WAC1D,SAASC,KACT,CAIA,SAASC,GAAeC,GACtB,MAAwB,iBAAVA,GAAsBA,GAAS,GAAKA,IAAUC,GAC9D,CACA,SAASC,GAAeC,EAAWC,GAC1B,OAAAC,KAAKC,IAAIH,GAAaC,GAAa,GAAKG,KAAKC,MAAO,EAC7D,CACA,SAASC,GAAiBL,EAAWM,GACnC,MAA4B,mBAAdN,EAA2BA,EAAUM,GAASN,CAC9D,CACA,SAASO,GAAeC,EAASF,GAC/B,MAA0B,mBAAZE,EAAyBA,EAAQF,GAASE,CAC1D,CACA,SAASC,GAAWC,EAASJ,GACrB,MAAAK,KACJA,EAAO,MAAAC,MACPA,EAAAC,YACAA,EAAAC,UACAA,EAAAC,SACAA,EAAAC,MACAA,GACEN,EACJ,GAAIK,EACF,GAAIH,GACF,GAAIN,EAAMW,YAAcC,GAAsBH,EAAUT,EAAMa,SACrD,OAAA,WAECC,GAAgBd,EAAMS,SAAUA,GACnC,OAAA,EAGX,GAAa,QAATJ,EAAgB,CACZ,MAAAU,EAAWf,EAAMe,WACnB,GAAS,WAATV,IAAsBU,EACjB,OAAA,EAEL,GAAS,aAATV,GAAuBU,EAClB,OAAA,CACT,CAEF,OAAqB,kBAAVL,GAAuBV,EAAMgB,YAAcN,OAGlDH,GAAeA,IAAgBP,EAAMiB,MAAMV,gBAG3CC,IAAcA,EAAUR,IAI9B,CACA,SAASkB,GAAcd,EAASe,GAC9B,MAAMb,MAAEA,EAAAc,OAAOA,EAAQZ,UAAAA,EAAAa,YAAWA,GAAgBjB,EAClD,GAAIiB,EAAa,CACX,IAACF,EAASN,QAAQQ,YACb,OAAA,EAET,GAAIf,GACF,GAAIgB,GAAQH,EAASN,QAAQQ,eAAiBC,GAAQD,GAC7C,OAAA,WAECP,GAAgBK,EAASN,QAAQQ,YAAaA,GACjD,OAAA,CACT,CAEF,QAAID,GAAUD,EAASF,MAAMG,SAAWA,MAGpCZ,IAAcA,EAAUW,GAI9B,CACA,SAASP,GAAsBH,EAAUI,GAEvC,cADeA,WAASU,iBAAkBD,IAC5Bb,EAChB,CACA,SAASa,GAAQb,GACf,OAAOe,KAAKC,UACVhB,GACA,CAACiB,EAAGC,IAAQC,GAAcD,GAAOE,OAAOC,KAAKH,GAAKI,OAAOC,QAAO,CAACC,EAAQC,KAChED,EAAAC,GAAOP,EAAIO,GACXD,IACN,CAAE,GAAIN,GAEb,CACA,SAASb,GAAgBqB,EAAGC,GAC1B,OAAID,IAAMC,UAGCD,UAAaC,OAGpBD,IAAKC,GAAkB,iBAAND,GAA+B,iBAANC,IACrCP,OAAOC,KAAKM,GAAGC,OAAOH,GAAQpB,GAAgBqB,EAAED,GAAME,EAAEF,MAGnE,CACA,SAASI,GAAiBH,EAAGC,GAC3B,GAAID,IAAMC,EACD,OAAAD,EAET,MAAMI,EAAQC,GAAaL,IAAMK,GAAaJ,GAC9C,GAAIG,GAASX,GAAcO,IAAMP,GAAcQ,GAAI,CACjD,MAAMK,EAASF,EAAQJ,EAAIN,OAAOC,KAAKK,GACjCO,EAAQD,EAAOE,OACfC,EAASL,EAAQH,EAAIP,OAAOC,KAAKM,GACjCS,EAAQD,EAAOD,OACfG,EAAOP,EAAQ,GAAK,CAAC,EAC3B,IAAIQ,EAAa,EACjB,IAAA,IAASC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,MAAMd,EAAMK,EAAQS,EAAIJ,EAAOI,KACzBT,GAASE,EAAOQ,SAASf,IAAQK,SAAqB,IAAXJ,EAAED,SAA8B,IAAXE,EAAEF,IACtEY,EAAKZ,QAAO,EACZa,MAEKD,EAAAZ,GAAOI,GAAiBH,EAAED,GAAME,EAAEF,IACnCY,EAAKZ,KAASC,EAAED,SAAmB,IAAXC,EAAED,IAC5Ba,IAEJ,CAEF,OAAOL,IAAUG,GAASE,IAAeL,EAAQP,EAAIW,CAAA,CAEhD,OAAAV,CACT,CACA,SAASc,GAAoBf,EAAGC,GAC1B,IAACA,GAAKP,OAAOC,KAAKK,GAAGQ,SAAWd,OAAOC,KAAKM,GAAGO,OAC1C,OAAA,EAET,IAAA,MAAWT,KAAOC,EAChB,GAAIA,EAAED,KAASE,EAAEF,GACR,OAAA,EAGJ,OAAA,CACT,CACA,SAASM,GAAalD,GACb,OAAA6D,MAAMC,QAAQ9D,IAAUA,EAAMqD,SAAWd,OAAOC,KAAKxC,GAAOqD,MACrE,CACA,SAASf,GAAcyB,GACjB,IAACC,GAAmBD,GACf,OAAA,EAET,MAAME,EAAOF,EAAEjF,YACf,QAAa,IAATmF,EACK,OAAA,EAET,MAAMC,EAAOD,EAAKE,UACd,QAACH,GAAmBE,OAGnBA,EAAKE,eAAe,kBAGrB7B,OAAO8B,eAAeN,KAAOxB,OAAO4B,UAI1C,CACA,SAASH,GAAmBD,GAC1B,MAA6C,oBAAtCxB,OAAO4B,UAAUG,SAASC,KAAKR,EACxC,CAMA,SAASS,GAAYC,EAAUC,EAAMnD,GAC/B,MAAqC,mBAA9BA,EAAQoD,kBACVpD,EAAQoD,kBAAkBF,EAAUC,IACJ,IAA9BnD,EAAQoD,kBAWV3B,GAAiByB,EAAUC,GAE7BA,CACT,CAIA,SAASE,GAASC,EAAOC,EAAMxE,EAAM,GACnC,MAAMyE,EAAW,IAAIF,EAAOC,GAC5B,OAAOxE,GAAOyE,EAAS1B,OAAS/C,EAAMyE,EAASC,MAAM,GAAKD,CAC5D,CACA,SAASE,GAAWJ,EAAOC,EAAMxE,EAAM,GACrC,MAAMyE,EAAW,CAACD,KAASD,GACpB,OAAAvE,GAAOyE,EAAS1B,OAAS/C,EAAMyE,EAASC,MAAM,MAASD,CAChE,CACA,IAAIG,GAAYC,SAChB,SAASC,GAAc7D,EAAS8D,GAQ9B,OAAK9D,EAAQ+D,UAAW,MAAAD,OAAA,EAAAA,EAAcE,gBAC7B,IAAMF,EAAaE,eAEvBhE,EAAQ+D,SAAW/D,EAAQ+D,UAAYJ,GAGrC3D,EAAQ+D,QAFN,IAAME,QAAQC,OAAO,IAAIC,MAAM,qBAAqBnE,EAAQF,cAGvE,CACA,SAASsE,GAAiBC,EAAcC,GAClC,MAAwB,mBAAjBD,EACFA,KAAgBC,KAEhBD,CACX,CC9NA,IA2DIE,GAAe,IA3DAC,gBAAclH,GAI/B,WAAAC,GACSkH,QAJTC,GAAAlH,KAAAmH,GACAD,GAAAlH,KAAAoH,GACAF,GAAAlH,KAAAqH,GAGOC,GAAAtH,KAAAqH,GAAUE,IACT,IAAC3G,IAAYC,OAAO2G,iBAAkB,CAClC,MAAAnH,EAAW,IAAMkH,IAEvB,OADO1G,OAAA2G,iBAAiB,mBAAoBnH,GAAU,GAC/C,KACEQ,OAAA4G,oBAAoB,mBAAoBpH,GAEzD,IAGA,CACE,WAAAE,GACOmH,QAAKN,IACHpH,KAAA2H,iBAAiBD,QAAKL,GAEjC,CACE,aAAA5G,SACOT,KAAKU,iBACR,OAAAsG,EAAAU,GAAA1H,KAAKoH,KAALJ,EAAAxB,KAAAxF,MACAsH,GAAAtH,KAAKoH,OAAW,GAEtB,CACE,gBAAAO,CAAiBC,SACfN,GAAAtH,KAAKqH,EAASO,GACd,OAAAZ,EAAAU,GAAA1H,KAAKoH,KAALJ,EAAAxB,KAAAxF,MACKsH,GAAAtH,KAAAoH,EAAWQ,GAAOC,IACE,kBAAZA,EACT7H,KAAK8H,WAAWD,GAEhB7H,KAAKuH,aAGb,CACE,UAAAO,CAAWD,GACOH,QAAKP,KAAaU,IAEhCP,GAAAtH,KAAKmH,EAAWU,GAChB7H,KAAKuH,UAEX,CACE,OAAAA,GACQ,MAAAQ,EAAY/H,KAAK+H,YAClB/H,KAAAC,UAAU+H,SAAS3H,IACtBA,EAAS0H,KAEf,CACE,SAAAA,SACM,MAAyB,kBAAlBL,GAAK1H,KAAAmH,GACPO,GAAK1H,KAAAmH,GAEkC,YAAzC,OAAAH,EAAAlG,WAAWmH,eAAX,EAAAjB,EAAqBkB,gBAChC,GAxDEf,EAAA,IAAAgB,QACAf,cACAC,EAHiB,IAAAc,QAAAnB,GCiDfoB,GAAgB,IAjDAC,gBAAcvI,GAIhC,WAAAC,GACSkH,QAJCC,GAAAlH,KAAAsI,GAAA,GACVlB,GAAAA,KAAAA,GACAC,GAAAA,KAAAA,GAGOA,GAAAA,KAAAA,GAAUkB,IACT,IAAC3H,IAAYC,OAAO2G,iBAAkB,CAClC,MAAAgB,EAAiB,IAAMD,GAAS,GAChCE,EAAkB,IAAMF,GAAS,GAGvC,OAFO1H,OAAA2G,iBAAiB,SAAUgB,GAAgB,GAC3C3H,OAAA2G,iBAAiB,UAAWiB,GAAiB,GAC7C,KACE5H,OAAA4G,oBAAoB,SAAUe,GAC9B3H,OAAA4G,oBAAoB,UAAWgB,GAEhD,IAGA,CACE,WAAAlI,GACOmH,QAAKN,IACHpH,KAAA2H,iBAAiBD,QAAKL,GAEjC,CACE,aAAA5G,SACOT,KAAKU,iBACR,OAAAsG,EAAAU,GAAA1H,KAAKoH,KAALJ,EAAAxB,KAAAxF,MACAsH,GAAAtH,KAAKoH,OAAW,GAEtB,CACE,gBAAAO,CAAiBC,SACfN,GAAAtH,KAAKqH,EAASO,GACd,OAAAZ,EAAAU,GAAA1H,KAAKoH,KAALJ,EAAAxB,KAAAxF,MACAsH,GAAAtH,KAAKoH,EAAWQ,EAAM5H,KAAK0I,UAAUtI,KAAKJ,OAC9C,CACE,SAAA0I,CAAUC,GACQjB,QAAKY,KAAYK,IAE/BrB,GAAAtH,KAAKsI,EAAUK,GACV3I,KAAAC,UAAU+H,SAAS3H,IACtBA,EAASsI,MAGjB,CACE,QAAAC,GACE,OAAOlB,GAAK1H,KAAAsI,EAChB,GA9CEA,EACAlB,IAAAA,QAAAA,EAAA,IACAC,QAAAA,EAAA,IAHkBc,QAAAE,GCDpB,SAASQ,KACH,IAAAC,EACApC,EACJ,MAAMqC,EAAW,IAAItC,SAAQ,CAACuC,EAAUC,KAC5BH,EAAAE,EACDtC,EAAAuC,KAKX,SAASC,EAASvD,GACTnC,OAAA2F,OAAOJ,EAAUpD,UACjBoD,EAASD,eACTC,EAASrC,MACpB,CAeS,OAtBPqC,EAAShG,OAAS,UAClBgG,EAASK,OAAM,SAONL,EAAAD,QAAW7H,IACTiI,EAAA,CACPnG,OAAQ,YACR9B,UAEF6H,EAAQ7H,IAED8H,EAAArC,OAAU2C,IACRH,EAAA,CACPnG,OAAQ,WACRsG,WAEF3C,EAAO2C,IAEFN,CACT,CC3BA,SAASO,GAAkBC,GACzB,OAAOjI,KAAKkI,IAAI,IAAM,GAAKD,EAAc,IAC3C,CACA,SAASE,GAASC,GAChB,MAAqC,YAA7BA,GAAe,WAAyBtB,GAAcQ,UAChE,CACA,IAAIe,GAAiB,cAAchD,MACjC,WAAA5G,CAAYyC,GACVyE,MAAM,kBACNjH,KAAK4J,OAAkB,MAATpH,OAAS,EAAAA,EAAAoH,OACvB5J,KAAK6J,OAAkB,MAATrH,OAAS,EAAAA,EAAAqH,MAC3B,GAEA,SAASC,GAAiB7I,GACxB,OAAOA,aAAiB0I,EAC1B,CACA,SAASI,GAAcC,GACrB,IAGIC,EAHAC,GAAmB,EACnBX,EAAe,EACfY,GAAa,EAEjB,MAAMpB,EAAWF,KAaXuB,EAAc,IAAMrD,GAAagB,cAAuC,WAAvBiC,EAAON,aAA4BtB,GAAcQ,aAAeoB,EAAOK,SACxHC,EAAW,IAAMb,GAASO,EAAON,cAAgBM,EAAOK,SACxDvB,EAAW7H,UACVkJ,IACUA,GAAA,EACb,OAAAnD,EAAAgD,EAAOO,YAAPvD,EAAAxB,KAAmBwE,EAAA/I,GACnB,MAAAgJ,GAAAA,IACAlB,EAASD,QAAQ7H,KAGfyF,EAAUzF,UACTkJ,IACUA,GAAA,EACb,OAAAnD,EAAAgD,EAAOQ,UAAPxD,EAAAxB,KAAiBwE,EAAA/I,GACjB,MAAAgJ,GAAAA,IACAlB,EAASrC,OAAOzF,KAGdwJ,EAAQ,IACL,IAAIhE,SAASiE,UAClBT,EAAchJ,KACRkJ,GAAcC,MAChBM,EAAgBzJ,IAGpB,OAAA+F,EAAAgD,EAAOW,UAAP3D,EAAAxB,KAAAwE,MACCY,MAAK,WACOX,OAAA,EACRE,GACH,OAAAnD,EAAAgD,EAAOa,aAAP7D,EAAAxB,KAAAwE,MAIAc,EAAM,KACV,GAAIX,EACF,OAEE,IAAAY,EACJ,MAAMvE,EAAkC,IAAjB+C,EAAqBS,EAAOxD,oBAAiB,EAChE,IACeuE,EAAAvE,GAAkBwD,EAAOgB,IAC3C,OAAQC,GACUF,EAAAtE,QAAQC,OAAOuE,EACtC,CACYxE,QAAAqC,QAAQiC,GAAgBH,KAAK9B,GAASM,OAAO6B,UACnD,GAAId,EACF,OAEF,MAAMe,EAAQlB,EAAOkB,QAAUtK,GAAW,EAAI,GACxCuK,EAAanB,EAAOmB,YAAc7B,GAClC8B,EAA8B,mBAAfD,EAA4BA,EAAW5B,EAAc0B,GAASE,EAC7EE,GAAwB,IAAVH,GAAmC,iBAAVA,GAAsB3B,EAAe2B,GAA0B,mBAAVA,GAAwBA,EAAM3B,EAAc0B,GJ+EpJ,IAAeK,GI9ELpB,GAAqBmB,GAIzB9B,IACA,OAAAvC,EAAAgD,EAAOuB,SAAPvE,EAAAxB,OAAgB+D,EAAc0B,IJyErBK,EIxEHF,EJyEH,IAAI3E,SAASqC,IAClB0C,WAAW1C,EAASwC,OI1ELV,MAAK,IACTR,SAAgB,EAASK,MAC/BG,MAAK,KACFV,EACFxD,EAAOuE,GAEFH,QAXPpE,EAAOuE,OAgBN,MAAA,CACLQ,QAAS1C,EACT2C,OAnFcC,UACTxB,IACIzD,EAAA,IAAIiD,GAAegC,IAC1B,OAAA3E,EAAAgD,EAAO4B,QAAP5E,EAAAxB,KAAAwE,KAiFF6B,SAAU,KACR,MAAA5B,GAAAA,IACOlB,GAET+C,YAlFkB,KACC5B,GAAA,GAkFnB6B,cAhFoB,KACD7B,GAAA,GAgFnBI,WACA0B,MAAO,KACD1B,IACGQ,IAEEL,IAACG,KAAKE,GAER/B,GAGb,CC9HA,IAAIkD,GAAoBC,GAAOV,WAAWU,EAAI,GA6E9C,IAAIC,GA5EJ,WACE,IAAIC,EAAQ,GACRC,EAAe,EACfC,EAAYC,IACJA,KAERC,EAAiBD,IACTA,KAERE,EAAaR,GACX,MAAAS,EAAYH,IACZF,EACFD,EAAMO,KAAKJ,GAEXE,GAAW,KACTH,EAASC,OAiBR,MAAA,CACLK,MAAQL,IACF,IAAA3I,EACJyI,IACI,IACFzI,EAAS2I,GACjB,CAAgB,QACRF,IACKA,GArBG,MACZ,MAAMQ,EAAgBT,EACtBA,EAAQ,GACJS,EAAcvI,QAChBmI,GAAW,KACTD,GAAc,KACEK,EAAA7E,SAASuE,IACrBD,EAASC,aAeJO,EAEjB,CACa,OAAAlJ,GAKTmJ,WAAaR,GACJ,IAAIS,KACTN,GAAS,KACPH,KAAYS,OAIlBN,WAKAO,kBAAoBjC,IACPsB,EAAAtB,GAMbkC,uBAAyBlC,IACPwB,EAAAxB,GAElBmC,aAAenC,IACAyB,EAAAzB,GAGnB,CACoBoC,GC5EhBC,IAAYC,EAAM,MAAN,WAAAvN,GACdmH,GAAAlH,KAAAuN,EAAA,CACA,OAAAC,GACExN,KAAKyN,gBACT,CACE,UAAAC,GACE1N,KAAKyN,iBACDzM,GAAehB,KAAK2N,SACjBrG,GAAAtH,KAAAuN,EAAa/B,YAAW,KAC3BxL,KAAK4N,mBACJ5N,KAAK2N,QAEd,CACE,YAAAE,CAAaC,GACX9N,KAAK2N,OAASrM,KAAKC,IACjBvB,KAAK2N,QAAU,EACfG,IAAclN,GAAWM,IAAW,KAE1C,CACE,cAAAuM,GACM/F,QAAK6F,KACPQ,aAAarG,QAAK6F,IAClBjG,GAAAtH,KAAKuN,OAAa,GAExB,GAvBEA,EADc,IAAApF,QAAAmF,GCUZU,IAAQC,gBAAcZ,GAQxB,WAAAtN,CAAYiK,GACJ/C,QATEC,GAAAlH,KAAAkO,GACVhH,GAAAlH,KAAAmO,GACAjH,GAAAlH,KAAAoO,GACAlH,GAAAlH,KAAAqO,GACAnH,GAAAlH,KAAAsO,GACApH,GAAAlH,KAAAuO,GACArH,GAAAlH,KAAAwO,GACAtH,GAAAlH,KAAAyO,GAGEnH,GAAAtH,KAAKyO,GAAuB,GAC5BnH,GAAAtH,KAAKwO,EAAkBxE,EAAO0E,gBACzB1O,KAAA2O,WAAW3E,EAAOxH,SACvBxC,KAAK4O,UAAY,GACjBtH,GAAAtH,KAAKsO,EAAUtE,EAAO6E,QACjBvH,GAAAtH,KAAAqO,EAAS3G,GAAK1H,KAAAsO,GAAQQ,iBAC3B9O,KAAKoC,SAAW4H,EAAO5H,SACvBpC,KAAKsC,UAAY0H,EAAO1H,UACnBgF,GAAAtH,KAAAmO,EA6UT,SAAyB3L,GACjB,MAAAmD,EAAsC,mBAAxBnD,EAAQuM,YAA6BvM,EAAQuM,cAAgBvM,EAAQuM,YACnFC,OAAmB,IAATrJ,EACVsJ,EAAuBD,EAAkD,mBAAjCxM,EAAQyM,qBAAsCzM,EAAQyM,uBAAyBzM,EAAQyM,qBAAuB,EACrJ,MAAA,CACLtJ,OACAuJ,gBAAiB,EACjBC,cAAeH,EAAUC,GAAwBzN,KAAKC,MAAQ,EAC9DwJ,MAAO,KACPmE,iBAAkB,EAClBC,eAAgB,EAChBC,kBAAmB,EACnBC,mBAAoB,KACpBC,UAAW,KACXC,eAAe,EACf1M,OAAQiM,EAAU,UAAY,UAC9B9M,YAAa,OAEjB,CA/VyBwN,CAAgB1P,KAAKwC,UACrCxC,KAAA4C,MAAQoH,EAAOpH,OAAS8E,GAAK1H,KAAAmO,GAClCnO,KAAK0N,YAAW,CAElB,QAAIiC,GACF,OAAO3P,KAAKwC,QAAQmN,IAAA,CAEtB,WAAIlE,SACF,OAAO,OAAAzE,EAAAU,GAAA1H,KAAKuO,SAAL,EAAAvH,EAAeyE,OAAA,CAExB,UAAAkD,CAAWnM,GACTxC,KAAKwC,QAAU,IAAKkF,GAAK1H,KAAAwO,MAAoBhM,GACxCxC,KAAA6N,aAAa7N,KAAKwC,QAAQmL,OAAM,CAEvC,cAAAC,GACO5N,KAAK4O,UAAUtK,QAAqC,SAA3BtE,KAAK4C,MAAMV,aAClCwF,GAAA1H,KAAAqO,GAAOuB,OAAO5P,KACrB,CAEF,OAAA6P,CAAQC,EAAStN,GACf,MAAMmD,EAAOF,GAAYzF,KAAK4C,MAAM+C,KAAMmK,EAAS9P,KAAKwC,SAOjD,OANPuN,GAAA/P,KAAKkO,KAAL1I,KAAexF,KAAA,CACb2F,OACA3D,KAAM,UACNmN,cAAwB,MAAT3M,OAAS,EAAAA,EAAApB,UACxB4O,OAAiB,MAATxN,OAAS,EAAAA,EAAAwN,SAEZrK,CAAA,CAET,QAAAsK,CAASrN,EAAOsN,GACdH,GAAA/P,KAAKkO,KAAL1I,KAAexF,KAAA,CAAEgC,KAAM,WAAYY,QAAOsN,mBAAiB,CAE7D,MAAAxE,CAAOlJ,WACC,MAAAiJ,EAAU,OAAAzE,EAAAU,GAAK1H,KAAAuO,WAALvH,EAAeyE,QAExB,OADF,OAAApD,EAAAX,GAAA1H,KAAAuO,KAAAlG,EAAUqD,OAAOlJ,GACfiJ,EAAUA,EAAQb,KAAK7J,IAAMqI,MAAMrI,IAAQ0F,QAAQqC,SAAQ,CAEpE,OAAA0E,GACEvG,MAAMuG,UACNxN,KAAK0L,OAAO,CAAE7B,QAAQ,GAAM,CAE9B,KAAAsG,GACEnQ,KAAKwN,UACAxN,KAAAiQ,SAASvI,QAAKyG,GAAa,CAElC,QAAAzL,GACE,OAAO1C,KAAK4O,UAAUwB,MACnBC,IAAgE,IAAnDzO,GAAeyO,EAAS7N,QAAQX,QAAS7B,OACzD,CAEF,UAAAsQ,GACM,OAAAtQ,KAAKuQ,oBAAsB,GACrBvQ,KAAK0C,WAER1C,KAAKwC,QAAQ+D,UAAYJ,IAAanG,KAAK4C,MAAMsM,gBAAkBlP,KAAK4C,MAAMwM,mBAAqB,CAAA,CAE5G,OAAAzM,GACM,QAAA3C,KAAK4C,MAAM6M,gBAGXzP,KAAKuQ,oBAAsB,EACtBvQ,KAAK4O,UAAUwB,MACnBC,GAAaA,EAASG,mBAAmB7N,eAGnB,IAApB3C,KAAK4C,MAAM+C,KAAS,CAE7B,aAAA8K,CAAcpP,EAAY,GACxB,OAAOrB,KAAK4C,MAAM6M,oBAAqC,IAApBzP,KAAK4C,MAAM+C,OAAoBxE,GAAenB,KAAK4C,MAAMuM,cAAe9N,EAAS,CAEtH,OAAAkG,SACQ,MAAA8I,EAAWrQ,KAAK4O,UAAU8B,MAAMC,GAAMA,EAAEC,6BACpC,MAAAP,GAAAA,EAAAQ,QAAQ,CAAEC,eAAe,IACnC,OAAA9J,EAAAU,GAAA1H,KAAKuO,KAALvH,EAAe6E,UAAS,CAE1B,QAAAtD,SACQ,MAAA8H,EAAWrQ,KAAK4O,UAAU8B,MAAMC,GAAMA,EAAEI,2BACpC,MAAAV,GAAAA,EAAAQ,QAAQ,CAAEC,eAAe,IACnC,OAAA9J,EAAAU,GAAA1H,KAAKuO,KAALvH,EAAe6E,UAAS,CAE1B,WAAAmF,CAAYX,GACLrQ,KAAK4O,UAAUhK,SAASyL,KACtBrQ,KAAA4O,UAAUjC,KAAK0D,GACpBrQ,KAAKyN,iBACA/F,GAAA1H,KAAAqO,GAAO4C,OAAO,CAAEjP,KAAM,gBAAiBL,MAAO3B,KAAMqQ,aAC3D,CAEF,cAAAa,CAAeb,GACTrQ,KAAK4O,UAAUhK,SAASyL,KAC1BrQ,KAAK4O,UAAY5O,KAAK4O,UAAUuC,QAAQR,GAAMA,IAAMN,IAC/CrQ,KAAK4O,UAAUtK,SACdoD,QAAK6G,KACH7G,QAAK+G,GACP/G,GAAA1H,KAAKuO,GAAS7C,OAAO,CAAE9B,QAAQ,IAE/BlC,GAAA1H,KAAKuO,GAASzC,eAGlB9L,KAAK0N,cAEFhG,GAAA1H,KAAAqO,GAAO4C,OAAO,CAAEjP,KAAM,kBAAmBL,MAAO3B,KAAMqQ,aAC7D,CAEF,iBAAAE,GACE,OAAOvQ,KAAK4O,UAAUtK,MAAA,CAExB,UAAA8M,GACOpR,KAAK4C,MAAM6M,eACdM,GAAA/P,KAAKkO,EAALmD,GAAA7L,KAAAxF,KAAe,CAAEgC,KAAM,cACzB,CAEF,KAAAsP,CAAM9O,EAAS8D,aACT,GAA2B,SAA3BtG,KAAK4C,MAAMV,YACb,QAAwB,IAApBlC,KAAK4C,MAAM+C,aAAmBW,WAAcwK,eAC9C9Q,KAAK0L,OAAO,CAAE7B,QAAQ,SAAM,GACnBnC,QAAK6G,GAEd,OADA7G,GAAA1H,KAAKuO,GAASxC,gBACPrE,QAAK6G,GAAS9C,QAMrB,GAHAjJ,GACFxC,KAAK2O,WAAWnM,IAEbxC,KAAKwC,QAAQ+D,QAAS,CACnB,MAAA8J,EAAWrQ,KAAK4O,UAAU8B,MAAMC,GAAMA,EAAEnO,QAAQ+D,UAClD8J,GACGrQ,KAAA2O,WAAW0B,EAAS7N,QAC3B,CASI,MAAA+O,EAAkB,IAAIC,gBACtBC,EAAqBC,IAClBlO,OAAAmO,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACHvK,GAAAtH,KAAKyO,GAAuB,GACrB8C,EAAgBO,WAsBvBC,EAAU,CACdzL,eACA9D,QAASxC,KAAKwC,QACdJ,SAAUpC,KAAKoC,SACfyM,OAAQnH,GAAK1H,KAAAsO,GACb1L,MAAO5C,KAAK4C,MACZoP,QAxBc,KACd,MAAMzL,EAAUF,GAAcrG,KAAKwC,QAAS8D,GACtC2L,EAAiB,CACrBpD,OAAQnH,GAAK1H,KAAAsO,GACblM,SAAUpC,KAAKoC,SACfuN,KAAM3P,KAAK2P,MAIT,OAFJ8B,EAAkBQ,GAClB3K,GAAAtH,KAAKyO,GAAuB,GACxBzO,KAAKwC,QAAQ0P,UACRlS,KAAKwC,QAAQ0P,UAClB3L,EACA0L,EACAjS,MAGGuG,EAAQ0L,KAUjBR,EAAkBM,GAClB,OAAA/K,EAAAhH,KAAKwC,QAAQ2P,WAAbnL,EAAuBoL,QACrBL,EACA/R,MAEFsH,GAAAtH,KAAKoO,EAAepO,KAAK4C,OACM,SAA3B5C,KAAK4C,MAAMV,aAA0BlC,KAAK4C,MAAM4M,aAAc,OAAAnH,EAAA0J,EAAQzL,mBAAR+B,EAAAA,EAAsBsH,OACjFI,GAAA/P,KAAAkO,EAAAmD,GAAA7L,KAAUxF,KAAA,CAAEgC,KAAM,QAAS2N,KAAM,OAAArC,EAAAyE,EAAQzL,mBAARgH,EAAAA,EAAsBqC,OAExD,MAAAnF,EAAWS,gBACTnB,GAAiBmB,IAAUA,EAAMpB,QACrCkG,GAAA/P,KAAKkO,KAAL1I,KAAexF,KAAA,CACbgC,KAAM,QACNiJ,UAGCnB,GAAiBmB,KACpB,OAAA5C,GAAArB,EAAAU,GAAA1H,KAAKqO,GAAOrE,QAAOQ,UAAnBnC,EAAA7C,KAAAwB,EACEiE,EACAjL,MAEF,OAAAiO,GAAAX,EAAA5F,GAAA1H,KAAKqO,GAAOrE,QAAOqI,YAAnBpE,EAAAzI,KAAA8H,EACEtN,KAAK4C,MAAM+C,KACXsF,EACAjL,OAGJA,KAAK0N,cA6CA,OA3CPpG,GAAAtH,KAAKuO,EAAWxE,GAAc,CAC5BvD,eAA8B,MAAdF,OAAc,EAAAA,EAAAE,eAC9BwE,GAAI+G,EAAQC,QACZpG,MAAO2F,EAAgB3F,MAAMxL,KAAKmR,GAClChH,UAAY5E,gBACV,QAAa,IAATA,EAAJ,CASI,IACF3F,KAAK6P,QAAQlK,SACNsF,GAEP,YADAT,EAAQS,EACR,CAEF,OAAA5C,GAAArB,EAAAU,GAAA1H,KAAKqO,GAAOrE,QAAOO,YAAnBlC,EAAA7C,KAAAwB,EAA+BrB,EAAM3F,MACrC,OAAAiO,GAAAX,EAAA5F,GAAA1H,KAAKqO,GAAOrE,QAAOqI,YAAnBpE,EAAAzI,KAAA8H,EACE3H,EACA3F,KAAK4C,MAAMqI,MACXjL,MAEFA,KAAK0N,YAdH,MADAlD,EAAQ,IAAI7D,MAAM,GAAG3G,KAAKsC,iCAiB9BkI,UACAe,OAAQ,CAAChC,EAAc0B,KACrB8E,GAAA/P,KAAKkO,KAAL1I,KAAexF,KAAA,CAAEgC,KAAM,SAAUuH,eAAc0B,WAEjDN,QAAS,KACPoF,GAAA/P,KAAKkO,EAALmD,GAAA7L,KAAAxF,KAAe,CAAEgC,KAAM,WAEzB6I,WAAY,KACVkF,GAAA/P,KAAKkO,EAALmD,GAAA7L,KAAAxF,KAAe,CAAEgC,KAAM,cAEzBkJ,MAAO6G,EAAQvP,QAAQ0I,MACvBC,WAAY4G,EAAQvP,QAAQ2I,WAC5BzB,YAAaqI,EAAQvP,QAAQkH,YAC7BW,OAAQ,KAAM,KAET3C,GAAA1H,KAAKuO,GAASvC,OAAM,GArQ7BmC,EAAA,IAAAhG,QACAiG,EACA,IAAAjG,QAAAkG,EAAA,IAAAlG,QACAmG,EACA,IAAAnG,QAAAoG,EAAA,IAAApG,QACAqG,cACAC,EAPU,IAAAtG,QAAA+F,EAAA,IAAAoE,QAwQVjB,WAAUkB,GAmEHvS,KAAA4C,MAlEW,CAACA,IACf,OAAQ2P,EAAOvQ,MACb,IAAK,SACI,MAAA,IACFY,EACH0M,kBAAmBiD,EAAOhJ,aAC1BgG,mBAAoBgD,EAAOtH,OAE/B,IAAK,QACI,MAAA,IACFrI,EACHV,YAAa,UAEjB,IAAK,WACI,MAAA,IACFU,EACHV,YAAa,YAEjB,IAAK,QACI,MAAA,IACFU,KACA4P,GAAW5P,EAAM+C,KAAM3F,KAAKwC,SAC/BgN,UAAW+C,EAAO5C,MAAQ,MAE9B,IAAK,UACI,MAAA,IACF/M,EACH+C,KAAM4M,EAAO5M,KACbuJ,gBAAiBtM,EAAMsM,gBAAkB,EACzCC,cAAeoD,EAAOpD,eAAiB3N,KAAKC,MAC5CwJ,MAAO,KACPwE,eAAe,EACf1M,OAAQ,cACJwP,EAAOvC,QAAU,CACnB9N,YAAa,OACboN,kBAAmB,EACnBC,mBAAoB,OAG1B,IAAK,QACH,MAAMtE,EAAQsH,EAAOtH,MACrB,OAAInB,GAAiBmB,IAAUA,EAAMrB,QAAUlC,QAAK0G,GAC3C,IAAK1G,GAAK1H,KAAAoO,GAAclM,YAAa,QAEvC,IACFU,EACHqI,QACAmE,iBAAkBxM,EAAMwM,iBAAmB,EAC3CC,eAAgB7N,KAAKC,MACrB6N,kBAAmB1M,EAAM0M,kBAAoB,EAC7CC,mBAAoBtE,EACpB/I,YAAa,OACba,OAAQ,SAEZ,IAAK,aACI,MAAA,IACFH,EACH6M,eAAe,GAEnB,IAAK,WACI,MAAA,IACF7M,KACA2P,EAAO3P,SAIL6P,CAAQzS,KAAK4C,OAC1BuJ,GAAcS,OAAM,KACb5M,KAAA4O,UAAU5G,SAASqI,IACtBA,EAASqC,mBAENhL,GAAA1H,KAAAqO,GAAO4C,OAAO,CAAEtP,MAAO3B,KAAMgC,KAAM,UAAWuQ,aACpD,EAjVOtE,GAoVZ,SAASuE,GAAW7M,EAAMnD,GACjB,MAAA,CACL8M,kBAAmB,EACnBC,mBAAoB,KACpBrN,YAAauH,GAASjH,EAAQkH,aAAe,WAAa,iBAC9C,IAAT/D,GAAmB,CACpBsF,MAAO,KACPlI,OAAQ,WAGd,CCrWA,IAAI4P,IAAaC,gBAAc9S,GAC7B,WAAAC,CAAYiK,EAAS,IACZ/C,QAITC,GAAAlH,KAAA6S,GAHE7S,KAAKgK,OAASA,EACT1C,GAAAtH,KAAA6S,MAA+BC,IACxC,CAEE,KAAAC,CAAMlE,EAAQrM,EAASI,GACrB,MAAMR,EAAWI,EAAQJ,SACnBE,EAAYE,EAAQF,WAAaC,GAAsBH,EAAUI,GACnE,IAAAb,EAAQ3B,KAAK6R,IAAIvP,GAYd,OAXFX,IACHA,EAAQ,IAAIqM,GAAM,CAChBa,SACAzM,WACAE,YACAE,QAASqM,EAAOmE,oBAAoBxQ,GACpCI,QACA8L,eAAgBG,EAAOoE,iBAAiB7Q,KAE1CpC,KAAKM,IAAIqB,IAEJA,CACX,CACE,GAAArB,CAAIqB,GACG+F,GAAK1H,KAAA6S,GAASK,IAAIvR,EAAMW,aAC3BoF,GAAA1H,KAAK6S,GAASM,IAAIxR,EAAMW,UAAWX,GACnC3B,KAAKiR,OAAO,CACVjP,KAAM,QACNL,UAGR,CACE,MAAAiO,CAAOjO,GACL,MAAMyR,EAAa1L,GAAA1H,KAAK6S,GAAShB,IAAIlQ,EAAMW,WACvC8Q,IACFzR,EAAM6L,UACF4F,IAAezR,GACZ+F,GAAA1H,KAAA6S,GAASrS,OAAOmB,EAAMW,WAE7BtC,KAAKiR,OAAO,CAAEjP,KAAM,UAAWL,UAErC,CACE,KAAA0R,GACElH,GAAcS,OAAM,KAClB5M,KAAKsT,SAAStL,SAASrG,IACrB3B,KAAK4P,OAAOjO,QAGpB,CACE,GAAAkQ,CAAIvP,GACK,OAAAoF,GAAA1H,KAAK6S,GAAShB,IAAIvP,EAC7B,CACE,MAAAgR,GACE,MAAO,IAAI5L,GAAK1H,KAAA6S,GAASU,SAC7B,CACE,IAAA7C,CAAK3O,GACH,MAAMyR,EAAmB,CAAEvR,OAAO,KAASF,GACpC,OAAA/B,KAAKsT,SAAS5C,MAClB/O,GAAUG,GAAW0R,EAAkB7R,IAE9C,CACE,OAAA8R,CAAQ1R,EAAU,IACV,MAAA2R,EAAU1T,KAAKsT,SACrB,OAAO9P,OAAOC,KAAK1B,GAASuC,OAAS,EAAIoP,EAAQvC,QAAQxP,GAAUG,GAAWC,EAASJ,KAAU+R,CACrG,CACE,MAAAzC,CAAO0C,GACLxH,GAAcS,OAAM,KACb5M,KAAAC,UAAU+H,SAAS3H,IACtBA,EAASsT,QAGjB,CACE,OAAApM,GACE4E,GAAcS,OAAM,KAClB5M,KAAKsT,SAAStL,SAASrG,IACrBA,EAAM4F,eAGd,CACE,QAAAgB,GACE4D,GAAcS,OAAM,KAClB5M,KAAKsT,SAAStL,SAASrG,IACrBA,EAAM4G,gBAGd,GAhFEsK,EANe,IAAA1K,QAAAyK,GCDbgB,IAAWC,gBAAcxG,GAI3B,WAAAtN,CAAYiK,GACH/C,QALIC,GAAAlH,KAAA8T,GACb5M,GAAAlH,KAAA+T,GACA7M,GAAAlH,KAAAgU,GACAzF,GAAAA,KAAAA,GAGEvO,KAAKiU,WAAajK,EAAOiK,WACzB3M,GAAAtH,KAAKgU,EAAiBhK,EAAOkK,eAC7B5M,GAAAtH,KAAK+T,EAAa,IACb/T,KAAA4C,MAAQoH,EAAOpH,OAgNf,CACLmP,aAAS,EACTpM,UAAM,EACNsF,MAAO,KACP1B,aAAc,EACd4K,cAAe,KACfC,UAAU,EACVrR,OAAQ,OACRsR,eAAW,EACXC,YAAa,GAxNRtU,KAAA2O,WAAW3E,EAAOxH,SACvBxC,KAAK0N,YACT,CACE,UAAAiB,CAAWnM,GACTxC,KAAKwC,QAAUA,EACVxC,KAAA6N,aAAa7N,KAAKwC,QAAQmL,OACnC,CACE,QAAIgC,GACF,OAAO3P,KAAKwC,QAAQmN,IACxB,CACE,WAAAqB,CAAYX,GACL3I,GAAA1H,KAAK+T,GAAWnP,SAASyL,KACvB3I,GAAA1H,KAAA+T,GAAWpH,KAAK0D,GACrBrQ,KAAKyN,iBACL/F,GAAA1H,KAAKgU,GAAe/C,OAAO,CACzBjP,KAAM,gBACNc,SAAU9C,KACVqQ,aAGR,CACE,cAAAa,CAAeb,GACb/I,GAAAtH,KAAK+T,EAAarM,GAAK1H,KAAA+T,GAAW5C,QAAQR,GAAMA,IAAMN,KACtDrQ,KAAK0N,aACLhG,GAAA1H,KAAKgU,GAAe/C,OAAO,CACzBjP,KAAM,kBACNc,SAAU9C,KACVqQ,YAEN,CACE,cAAAzC,GACOlG,GAAK1H,KAAA+T,GAAWzP,SACO,YAAtBtE,KAAK4C,MAAMG,OACb/C,KAAK0N,aAEAhG,GAAA1H,KAAAgU,GAAepE,OAAO5P,MAGnC,CACE,iBACE,OAAO,OAAAgH,EAAAU,GAAA1H,KAAKuO,SAAL,EAAAvH,EAAe6E,aACtB7L,KAAKuU,QAAQvU,KAAK4C,MAAMyR,UAC5B,CACE,aAAME,CAAQF,+CACZ,MAAMxJ,EAAa,KACjBkF,GAAA/P,KAAK8T,EAAAzC,GAAL7L,KAAexF,KAAA,CAAEgC,KAAM,cAEzBsF,GAAAtH,KAAKuO,EAAWxE,GAAc,CAC5BiB,GAAI,IACGhL,KAAKwC,QAAQgS,WAGXxU,KAAKwC,QAAQgS,WAAWH,GAFtB5N,QAAQC,OAAO,IAAIC,MAAM,wBAIpC4E,OAAQ,CAAChC,EAAc0B,KACrB8E,GAAA/P,KAAK8T,EAAAzC,GAAL7L,KAAAxF,KAAe,CAAEgC,KAAM,SAAUuH,eAAc0B,WAEjDN,QAAS,KACPoF,GAAA/P,KAAK8T,EAAAzC,GAAL7L,KAAexF,KAAA,CAAEgC,KAAM,WAEzB6I,aACAK,MAAOlL,KAAKwC,QAAQ0I,OAAS,EAC7BC,WAAYnL,KAAKwC,QAAQ2I,WACzBzB,YAAa1J,KAAKwC,QAAQkH,YAC1BW,OAAQ,IAAM3C,GAAK1H,KAAAgU,GAAe3J,OAAOrK,SAErC,MAAAyU,EAAiC,YAAtBzU,KAAK4C,MAAMG,OACtBqR,GAAY1M,GAAK6G,KAAAA,GAASjE,WAC5B,IACF,GAAImK,EACU5J,QACP,CACLkF,GAAA/P,KAAK8T,EAAAzC,GAAL7L,KAAAxF,KAAe,CAAEgC,KAAM,UAAWqS,YAAWD,mBACvC,OAAA/L,GAAArB,EAAAU,GAAA1H,KAAKgU,GAAehK,QAAO0K,iBAA3BrM,EAAA7C,KAAAwB,EACJqN,EACArU,OAEI,MAAA+R,QAAgB,OAAA9D,GAAAX,EAAAtN,KAAKwC,SAAQkS,eAAb,EAAAzG,EAAAzI,KAAA8H,EAAwB+G,IAC1CtC,IAAY/R,KAAK4C,MAAMmP,SACpBhC,GAAA/P,KAAA8T,EAAAzC,GAAL7L,KAAexF,KAAA,CACbgC,KAAM,UACN+P,UACAsC,YACAD,YAGZ,CACM,MAAMzO,QAAa+B,GAAK6G,KAAAA,GAASvC,QAiB1B,aAhBD,OAAA6H,GAAAjB,EAAAlL,GAAA1H,KAAKgU,GAAehK,QAAOO,kBAA3BsJ,EAAArO,KAAAoN,EACJjN,EACA0O,EACArU,KAAK4C,MAAMmP,QACX/R,aAEI,OAAA2U,GAAAC,EAAA5U,KAAKwC,SAAQ+H,gBAAb,EAAAoK,EAAAnP,KAAAoP,EAAyBjP,EAAM0O,EAAWrU,KAAK4C,MAAMmP,gBACrD,OAAA8C,GAAAC,EAAApN,GAAA1H,KAAKgU,GAAehK,QAAOqI,kBAA3BwC,EAAArP,KAAAsP,EACJnP,EACA,KACA3F,KAAK4C,MAAMyR,UACXrU,KAAK4C,MAAMmP,QACX/R,aAEI,OAAA+U,GAAAC,EAAAhV,KAAKwC,SAAQ6P,gBAAb,EAAA0C,EAAAvP,KAAAwP,EAAyBrP,EAAM,KAAM0O,EAAWrU,KAAK4C,MAAMmP,UACjEhC,GAAA/P,KAAK8T,EAAAzC,GAAL7L,KAAAxF,KAAe,CAAEgC,KAAM,UAAW2D,SAC3BA,CACR,OAAQsF,GACH,IAyBI,YAxBA,OAAAgK,GAAAC,EAAAxN,GAAA1H,KAAKgU,GAAehK,QAAOQ,cAA3B,EAAAyK,EAAAzP,KAAA0P,EACJjK,EACAoJ,EACArU,KAAK4C,MAAMmP,QACX/R,aAEI,OAAAmV,GAAAC,EAAApV,KAAKwC,SAAQgI,cAAb,EAAA2K,EAAA3P,KAAA4P,EACJnK,EACAoJ,EACArU,KAAK4C,MAAMmP,gBAEP,OAAAsD,GAAAC,EAAA5N,GAAA1H,KAAKgU,GAAehK,QAAOqI,gBAA3B,EAAAgD,EAAA7P,KAAA8P,OACJ,EACArK,EACAjL,KAAK4C,MAAMyR,UACXrU,KAAK4C,MAAMmP,QACX/R,aAEI,OAAAuV,GAAAC,EAAAxV,KAAKwC,SAAQ6P,gBAAb,EAAAkD,EAAA/P,KAAAgQ,OACJ,EACAvK,EACAoJ,EACArU,KAAK4C,MAAMmP,UAEP9G,CACd,CAAgB,QACR8E,GAAA/P,KAAK8T,EAAAzC,GAAL7L,KAAAxF,KAAe,CAAEgC,KAAM,QAASiJ,SACxC,CACA,CAAc,QACHvD,GAAA1H,KAAAgU,GAAeyB,QAAQzV,KAClC,CACA,GAnJE+T,cACAC,EACAzF,IAAAA,QAAAA,EAAA,YAHauF,EAqJbzC,IAAAA,QAAAA,EAAS,SAACkB,GAsDHvS,KAAA4C,MArDW,CAACA,IACf,OAAQ2P,EAAOvQ,MACb,IAAK,SACI,MAAA,IACFY,EACH2G,aAAcgJ,EAAOhJ,aACrB4K,cAAe5B,EAAOtH,OAE1B,IAAK,QACI,MAAA,IACFrI,EACHwR,UAAU,GAEd,IAAK,WACI,MAAA,IACFxR,EACHwR,UAAU,GAEd,IAAK,UACI,MAAA,IACFxR,EACHmP,QAASQ,EAAOR,QAChBpM,UAAM,EACN4D,aAAc,EACd4K,cAAe,KACflJ,MAAO,KACPmJ,SAAU7B,EAAO6B,SACjBrR,OAAQ,UACRsR,UAAW9B,EAAO8B,UAClBC,YAAa9S,KAAKC,OAEtB,IAAK,UACI,MAAA,IACFmB,EACH+C,KAAM4M,EAAO5M,KACb4D,aAAc,EACd4K,cAAe,KACflJ,MAAO,KACPlI,OAAQ,UACRqR,UAAU,GAEd,IAAK,QACI,MAAA,IACFxR,EACH+C,UAAM,EACNsF,MAAOsH,EAAOtH,MACd1B,aAAc3G,EAAM2G,aAAe,EACnC4K,cAAe5B,EAAOtH,MACtBmJ,UAAU,EACVrR,OAAQ,WAIH0P,CAAQzS,KAAK4C,OAC1BuJ,GAAcS,OAAM,KACblF,GAAA1H,KAAA+T,GAAW/L,SAASqI,IACvBA,EAASqF,iBAAiBnD,MAE5B7K,GAAA1H,KAAKgU,GAAe/C,OAAO,CACzBnO,SAAU9C,KACVgC,KAAM,UACNuQ,aAGR,EAtNesB,GCCf,IAAI8B,IAAgBf,gBAAc9U,GAChC,WAAAC,CAAYiK,EAAS,IACZ/C,QAMTC,GAAAlH,KAAA4V,GACA1O,GAAAlH,KAAA6V,GACA3O,GAAAlH,KAAA8V,GAPE9V,KAAKgK,OAASA,EACT1C,GAAAtH,KAAA4V,MAAiC1V,KACjCoH,GAAAtH,KAAA6V,MAA8B/C,KACnCxL,GAAAtH,KAAK8V,EAAc,EACvB,CAIE,KAAA/C,CAAMlE,EAAQrM,EAASI,GACf,MAAAE,EAAW,IAAI8Q,GAAS,CAC5BM,cAAelU,KACfiU,aAAc8B,GAAA/V,KAAK8V,GAALzS,EACdb,QAASqM,EAAOmH,uBAAuBxT,GACvCI,UAGK,OADP5C,KAAKM,IAAIwC,GACFA,CACX,CACE,GAAAxC,CAAIwC,GACG4E,GAAA1H,KAAA4V,GAAWtV,IAAIwC,GACd,MAAAmT,EAAQC,GAASpT,GACnB,GAAiB,iBAAVmT,EAAoB,CAC7B,MAAME,EAAkBzO,GAAA1H,KAAK6V,GAAQhE,IAAIoE,GACrCE,EACFA,EAAgBxJ,KAAK7J,GAErB4E,GAAA1H,KAAK6V,GAAQ1C,IAAI8C,EAAO,CAACnT,GAEjC,CACI9C,KAAKiR,OAAO,CAAEjP,KAAM,QAASc,YACjC,CACE,MAAA8M,CAAO9M,GACL,GAAI4E,GAAK1H,KAAA4V,GAAWpV,OAAOsC,GAAW,CAC9B,MAAAmT,EAAQC,GAASpT,GACnB,GAAiB,iBAAVmT,EAAoB,CAC7B,MAAME,EAAkBzO,GAAA1H,KAAK6V,GAAQhE,IAAIoE,GACzC,GAAIE,EACE,GAAAA,EAAgB7R,OAAS,EAAG,CACxB,MAAA8R,EAAQD,EAAgBE,QAAQvT,IACpB,IAAdsT,GACcD,EAAAG,OAAOF,EAAO,EAEjC,MAAUD,EAAgB,KAAOrT,GAC3B4E,GAAA1H,KAAA6V,GAAQrV,OAAOyV,EAGhC,CACA,CACIjW,KAAKiR,OAAO,CAAEjP,KAAM,UAAWc,YACnC,CACE,MAAAuH,CAAOvH,GACC,MAAAmT,EAAQC,GAASpT,GACnB,GAAiB,iBAAVmT,EAAoB,CAC7B,MAAMM,EAAyB7O,GAAA1H,KAAK6V,GAAQhE,IAAIoE,GAC1CO,EAA+C,MAAxBD,OAAwB,EAAAA,EAAA7F,MAClD+F,GAAyB,YAAnBA,EAAE7T,MAAMG,SAEV,OAACyT,GAAwBA,IAAyB1T,CAC/D,CACa,OAAA,CAEb,CACE,OAAA2S,CAAQ3S,SACA,MAAAmT,EAAQC,GAASpT,GACnB,GAAiB,iBAAVmT,EAAoB,CAC7B,MAAMS,EAAgB,OAAA1P,EAAAU,GAAK1H,KAAA6V,GAAQhE,IAAIoE,SAAjBjP,EAAAA,EAAyB0J,MAAM+F,GAAMA,IAAM3T,GAAY2T,EAAE7T,MAAMwR,WAC9E,OAAA,MAAAsC,OAAA,EAAAA,EAAe7K,aAAcpF,QAAQqC,SAClD,CACM,OAAOrC,QAAQqC,SAErB,CACE,KAAAuK,GACElH,GAAcS,OAAM,KACblF,GAAA1H,KAAA4V,GAAW5N,SAASlF,IACvB9C,KAAKiR,OAAO,CAAEjP,KAAM,UAAWc,gBAEjC4E,GAAA1H,KAAK4V,GAAWvC,QAChB3L,GAAA1H,KAAK6V,GAAQxC,UAEnB,CACE,MAAAC,GACS,OAAAxO,MAAM6R,KAAKjP,GAAA1H,KAAK4V,GAC3B,CACE,IAAAlF,CAAK3O,GACH,MAAMyR,EAAmB,CAAEvR,OAAO,KAASF,GACpC,OAAA/B,KAAKsT,SAAS5C,MAClB5N,GAAaD,GAAc2Q,EAAkB1Q,IAEpD,CACE,OAAA2Q,CAAQ1R,EAAU,IACT,OAAA/B,KAAKsT,SAASnC,QAAQrO,GAAaD,GAAcd,EAASe,IACrE,CACE,MAAAmO,CAAO0C,GACLxH,GAAcS,OAAM,KACb5M,KAAAC,UAAU+H,SAAS3H,IACtBA,EAASsT,QAGjB,CACE,qBAAAiD,GACQ,MAAAC,EAAkB7W,KAAKsT,SAASnC,QAAQR,GAAMA,EAAE/N,MAAMwR,WAC5D,OAAOjI,GAAcS,OACnB,IAAMnG,QAAQqQ,IACZD,EAAgBE,KAAKjU,GAAaA,EAAS+I,WAAWzC,MAAMrI,QAGpE,GArGE6U,EAAA,IAAAzN,QACA0N,cACAC,EAVkB,IAAA3N,QAAAyM,GA+GpB,SAASsB,GAASpT,SAChB,OAAO,OAAAkE,EAAAlE,EAASN,QAAQyT,cAAjBjP,EAAwBgQ,EACjC,CCpHA,SAASC,GAAsBC,GACtB,MAAA,CACL9E,QAAS,CAACL,EAASpQ,mBACjB,MAAMa,EAAUuP,EAAQvP,QAClB2U,EAAY,OAAA7J,EAAA,OAAAjF,EAAA,OAAArB,EAAA+K,EAAQzL,mBAARU,EAAAA,EAAsB2I,WAAtB,EAAAtH,EAA4B+O,kBAA5B9J,EAAuC6J,UACnDE,GAAW,OAAApJ,EAAA8D,EAAQnP,MAAM+C,WAAdsI,EAAAA,EAAoBiJ,QAAS,GACxCI,GAAgB,OAAA1E,EAAAb,EAAQnP,MAAM+C,WAAdiN,EAAAA,EAAoB2E,aAAc,GACxD,IAAI3T,EAAS,CAAEsT,MAAO,GAAIK,WAAY,IAClCC,EAAc,EAClB,MAAMxF,EAAUyF,UACd,IAAIC,GAAY,EACV,MAeAnR,EAAUF,GAAc0L,EAAQvP,QAASuP,EAAQzL,cACjDqR,EAAYF,MAAO9R,EAAMiS,EAAOC,KACpC,GAAIH,EACF,OAAOjR,QAAQC,SAEjB,GAAa,MAATkR,GAAiBjS,EAAKuR,MAAM5S,OACvB,OAAAmC,QAAQqC,QAAQnD,GAEzB,MAAMsM,EAAiB,CACrBpD,OAAQkD,EAAQlD,OAChBzM,SAAU2P,EAAQ3P,SAClB0V,UAAWF,EACXT,UAAWU,EAAW,WAAa,UACnClI,KAAMoC,EAAQvP,QAAQmN,MA5BA,IAAC+B,IA8BPO,EA7BXzO,OAAAmO,eAAeD,EAAQ,SAAU,CACtCE,YAAY,EACZC,IAAK,KACCE,EAAQD,OAAOiG,QACLL,GAAA,EAEJ3F,EAAAD,OAAOtK,iBAAiB,SAAS,KAC3BkQ,GAAA,KAGT3F,EAAQD,UAoBnB,MAAMkG,QAAazR,EACjB0L,IAEIgG,SAAEA,GAAalG,EAAQvP,QACvB0V,EAAQL,EAAW3R,GAAaL,GAC/B,MAAA,CACLqR,MAAOgB,EAAMvS,EAAKuR,MAAOc,EAAMC,GAC/BV,WAAYW,EAAMvS,EAAK4R,WAAYK,EAAOK,KAG1C,GAAAd,GAAaE,EAAS/S,OAAQ,CAChC,MAAMuT,EAAyB,aAAdV,EAEXgB,EAAU,CACdjB,MAAOG,EACPE,WAAYD,GAERM,GALcC,EAAWO,GAAuBC,IAK5B7V,EAAS2V,GACnCvU,QAAe+T,EAAUQ,EAASP,EAAOC,EACnD,KAAe,CACC,MAAAS,EAAiBpB,GAASG,EAAS/S,OACtC,EAAA,CACK,MAAAsT,EAAwB,IAAhBJ,EAAoBF,EAAc,IAAM9U,EAAQ+V,iBAAmBF,GAAiB7V,EAASoB,GACvG,GAAA4T,EAAc,GAAc,MAATI,EACrB,MAEOhU,QAAM+T,EAAU/T,EAAQgU,GACjCJ,GACD,OAAQA,EAAcc,EACjC,CACe,OAAA1U,GAELmO,EAAQvP,QAAQ0P,UAClBH,EAAQC,QAAU,aAChB,OAAO,OAAA3J,GAAArB,EAAA+K,EAAQvP,SAAQ0P,kBAAhB7J,EAAA7C,KAAAwB,EACLgL,EACA,CACEnD,OAAQkD,EAAQlD,OAChBzM,SAAU2P,EAAQ3P,SAClBuN,KAAMoC,EAAQvP,QAAQmN,KACtBmC,OAAQC,EAAQD,QAElBnQ,IAIJoQ,EAAQC,QAAUA,GAI1B,CACA,SAASqG,GAAiB7V,GAAS0U,MAAEA,EAAAK,WAAOA,IACpC,MAAAiB,EAAYtB,EAAM5S,OAAS,EAC1B,OAAA4S,EAAM5S,OAAS,EAAI9B,EAAQ6V,iBAChCnB,EAAMsB,GACNtB,EACAK,EAAWiB,GACXjB,QACE,CACN,CACA,SAASa,GAAqB5V,GAAS0U,MAAEA,EAAAK,WAAOA,UAC9C,OAAOL,EAAM5S,OAAS,EAAI,OAAA0C,EAAAxE,EAAQ4V,2BAARpR,EAAAA,EAAAxB,KAA+BhD,EAAA0U,EAAM,GAAIA,EAAOK,EAAW,GAAIA,QAAc,CACzG,CC1FG,IAACkB,IAAc9D,EAAM,MAStB,WAAA5U,CAAYiK,EAAS,IARrB9C,GAAAlH,KAAA0Y,GACA1E,GAAAA,KAAAA,GACAxF,GAAAA,KAAAA,GACAtH,GAAAlH,KAAA2Y,GACAzR,GAAAlH,KAAA4Y,GACA1R,GAAAlH,KAAA6Y,GACA3R,GAAAlH,KAAA8Y,GACA5R,GAAAlH,KAAA+Y,GAEEzR,GAAAtH,KAAK0Y,EAAc1O,EAAOgP,YAAc,IAAIrG,IAC5CrL,GAAAtH,KAAKgU,EAAiBhK,EAAOkK,eAAiB,IAAIyB,IAC7CnH,GAAAA,KAAAA,EAAkBxE,EAAO0E,gBAAkB,CAAE,GAC7CpH,GAAAtH,KAAA2Y,MAAqC7F,KACrCxL,GAAAtH,KAAA4Y,MAAwC9F,KAC7CxL,GAAAtH,KAAK6Y,EAAc,EACvB,CACE,KAAAI,GACElD,GAAA/V,KAAK6Y,GAALxV,IACyB,IAArBqE,GAAA1H,KAAK6Y,KACTvR,GAAAtH,KAAK8Y,EAAoB/R,GAAa5G,WAAUsX,MAAO5P,IACjDA,UACI7H,KAAK4W,wBACXlP,GAAA1H,KAAK0Y,GAAYnR,eAGrBD,GAAAtH,KAAK+Y,EAAqB3Q,GAAcjI,WAAUsX,MAAO9O,IACnDA,UACI3I,KAAK4W,wBACXlP,GAAA1H,KAAK0Y,GAAYnQ,gBAGzB,CACE,OAAA2Q,WACEnD,GAAA/V,KAAK6Y,GAALxV,IACyB,IAArBqE,GAAA1H,KAAK6Y,KACT,OAAA7R,EAAAU,GAAA1H,KAAK8Y,KAAL9R,EAAAxB,KAAAxF,MACAsH,GAAAtH,KAAK8Y,OAAoB,GACzB,OAAAzQ,EAAAX,GAAA1H,KAAK+Y,KAAL1Q,EAAA7C,KAAAxF,MACAsH,GAAAtH,KAAK+Y,OAAqB,GAC9B,CACE,UAAAI,CAAWpX,GACF,OAAA2F,GAAA1H,KAAK0Y,GAAYjF,QAAQ,IAAK1R,EAASG,YAAa,aAAcoC,MAC7E,CACE,UAAA8U,CAAWrX,GACF,OAAA2F,GAAA1H,KAAKgU,GAAeP,QAAQ,IAAK1R,EAASgB,OAAQ,YAAauB,MAC1E,CAQE,YAAA+U,CAAajX,SACX,MAAMI,EAAUxC,KAAKgT,oBAAoB,CAAE5Q,aACpC4E,OAAA,OAAAA,EAAAU,QAAKgR,GAAY7G,IAAIrP,EAAQF,iBAA7B0E,EAAAA,EAAyCpE,MAAM+C,IAC1D,CACE,eAAA2T,CAAgB9W,GACR,MAAA+W,EAAmBvZ,KAAKgT,oBAAoBxQ,GAC5Cb,EAAQ+F,GAAA1H,KAAK0Y,GAAY3F,MAAM/S,KAAMuZ,GACrCC,EAAa7X,EAAMiB,MAAM+C,KAC/B,YAAmB,IAAf6T,EACKxZ,KAAKyZ,WAAWjX,IAErBA,EAAQkX,mBAAqB/X,EAAM8O,cAAc/O,GAAiB6X,EAAiBlY,UAAWM,KAC3F3B,KAAK2Z,cAAcJ,GAEnB9S,QAAQqC,QAAQ0Q,GAC3B,CACE,cAAAI,CAAe7X,GACN,OAAA2F,GAAA1H,KAAK0Y,GAAYjF,QAAQ1R,GAASgV,KAAI,EAAG3U,WAAUQ,WAEjD,CAACR,EADKQ,EAAM+C,OAGzB,CACE,YAAAkU,CAAazX,EAAU0X,EAAStX,GAC9B,MAAM+W,EAAmBvZ,KAAKgT,oBAAoB,CAAE5Q,aAC9CT,EAAQ+F,QAAKgR,GAAY7G,IAC7B0H,EAAiBjX,WAGbqD,EZ9FV,SAA0BmU,EAASC,GACjC,MAA0B,mBAAZD,EAAyBA,EAAQC,GAASD,CAC1D,CY4FiBE,CAAiBF,QADbnY,WAAOiB,MAAM+C,MAE9B,QAAa,IAATA,EAGJ,OAAO+B,GAAK1H,KAAA0Y,GAAY3F,MAAM/S,KAAMuZ,GAAkB1J,QAAQlK,EAAM,IAAKnD,EAASwN,QAAQ,GAC9F,CACE,cAAAiK,CAAelY,EAAS+X,EAAStX,GAC/B,OAAO2J,GAAcS,OACnB,IAAMlF,GAAK1H,KAAA0Y,GAAYjF,QAAQ1R,GAASgV,KAAI,EAAG3U,cAAe,CAC5DA,EACApC,KAAK6Z,aAAazX,EAAU0X,EAAStX,OAG7C,CACE,aAAA0X,CAAc9X,SACZ,MAAMI,EAAUxC,KAAKgT,oBAAoB,CAAE5Q,aACpC4E,OAAA,OAAAA,EAAAU,QAAKgR,GAAY7G,IACtBrP,EAAQF,mBADH0E,EAEJpE,KACP,CACE,aAAAuX,CAAcpY,GACZ,MAAMiX,EAAatR,GAAK1H,KAAA0Y,GACxBvM,GAAcS,OAAM,KAClBoM,EAAWvF,QAAQ1R,GAASiG,SAASrG,IACnCqX,EAAWpJ,OAAOjO,QAG1B,CACE,YAAAyY,CAAarY,EAASS,GACpB,MAAMwW,EAAatR,GAAK1H,KAAA0Y,GACjB,OAAAvM,GAAcS,OAAM,KACzBoM,EAAWvF,QAAQ1R,GAASiG,SAASrG,IACnCA,EAAMwO,WAEDnQ,KAAKqa,eACV,CACErY,KAAM,YACHD,GAELS,KAGR,CACE,aAAA8X,CAAcvY,EAAS4J,EAAgB,IACrC,MAAM4O,EAAyB,CAAE3Q,QAAQ,KAAS+B,GAC5C6O,EAAWrO,GAAcS,OAC7B,IAAMlF,GAAA1H,KAAK0Y,GAAYjF,QAAQ1R,GAASgV,KAAKpV,GAAUA,EAAM+J,OAAO6O,OAE/D,OAAA9T,QAAQqQ,IAAI0D,GAAU5P,KAAK7J,IAAMqI,MAAMrI,GAClD,CACE,iBAAA0Z,CAAkB1Y,EAASS,EAAU,IAC5B,OAAA2J,GAAcS,OAAM,KACzBlF,GAAA1H,KAAK0Y,GAAYjF,QAAQ1R,GAASiG,SAASrG,IACzCA,EAAMyP,gBAEqB,UAAzB,MAAArP,OAAA,EAAAA,EAAS2Y,aACJjU,QAAQqC,UAEV9I,KAAKqa,eACV,IACKtY,EACHC,MAAM,MAAAD,OAAA,EAAAA,EAAS2Y,eAAe,MAAA3Y,OAAA,EAAAA,EAASC,OAAQ,UAEjDQ,KAGR,CACE,cAAA6X,CAAetY,EAASS,EAAU,IAChC,MAAM8D,EAAe,IAChB9D,EACHsO,cAAetO,EAAQsO,gBAAiB,GAEpC0J,EAAWrO,GAAcS,OAC7B,IAAMlF,GAAK1H,KAAA0Y,GAAYjF,QAAQ1R,GAASoP,QAAQxP,IAAWA,EAAM2O,eAAcyG,KAAKpV,IAClF,IAAI8J,EAAU9J,EAAM2P,WAAM,EAAQhL,GAIlC,OAHKA,EAAaO,eACN4E,EAAAA,EAAQrC,MAAMrI,KAES,WAA5BY,EAAMiB,MAAMV,YAA2BuE,QAAQqC,UAAY2C,OAGtE,OAAOhF,QAAQqQ,IAAI0D,GAAU5P,KAAK7J,GACtC,CACE,UAAA0Y,CAAWjX,GACH,MAAA+W,EAAmBvZ,KAAKgT,oBAAoBxQ,QACnB,IAA3B+W,EAAiBrO,QACnBqO,EAAiBrO,OAAQ,GAE3B,MAAMvJ,EAAQ+F,GAAA1H,KAAK0Y,GAAY3F,MAAM/S,KAAMuZ,GAC3C,OAAO5X,EAAM8O,cACX/O,GAAiB6X,EAAiBlY,UAAWM,IAC3CA,EAAM2P,MAAMiI,GAAoB9S,QAAQqC,QAAQnH,EAAMiB,MAAM+C,KACpE,CACE,aAAAgU,CAAcnX,GACL,OAAAxC,KAAKyZ,WAAWjX,GAASoI,KAAK7J,IAAMqI,MAAMrI,GACrD,CACE,kBAAA4Z,CAAmBnY,GAEV,OADCA,EAAA2P,SAAW8E,GAAsBzU,EAAQ0U,OAC1ClX,KAAKyZ,WAAWjX,EAC3B,CACE,qBAAAoY,CAAsBpY,GACb,OAAAxC,KAAK2a,mBAAmBnY,GAASoI,KAAK7J,IAAMqI,MAAMrI,GAC7D,CACE,uBAAA8Z,CAAwBrY,GAEf,OADCA,EAAA2P,SAAW8E,GAAsBzU,EAAQ0U,OAC1ClX,KAAKsZ,gBAAgB9W,EAChC,CACE,qBAAAoU,GACM,OAAAxO,GAAcQ,WACTlB,GAAA1H,KAAKgU,GAAe4C,wBAEtBnQ,QAAQqC,SACnB,CACE,aAAAgG,GACE,OAAOpH,GAAK1H,KAAA0Y,EAChB,CACE,gBAAAoC,GACE,OAAOpT,GAAKsM,KAAAA,EAChB,CACE,iBAAA+G,GACE,OAAOrT,GAAK8G,KAAAA,EAChB,CACE,iBAAAwM,CAAkBxY,GAChB8E,GAAAtH,KAAKwO,EAAkBhM,EAC3B,CACE,gBAAAyY,CAAiB7Y,EAAUI,GACzBkF,GAAA1H,KAAK2Y,GAAexF,IAAIlQ,GAAQb,GAAW,CACzCA,WACAsM,eAAgBlM,GAEtB,CACE,gBAAAyQ,CAAiB7Q,GACf,MAAM8Y,EAAW,IAAIxT,GAAK1H,KAAA2Y,GAAepF,UACnC3P,EAAS,CAAE,EAMV,OALEsX,EAAAlT,SAASmT,IACZ1Y,GAAgBL,EAAU+Y,EAAa/Y,WAClCoB,OAAA2F,OAAOvF,EAAQuX,EAAazM,mBAGhC9K,CACX,CACE,mBAAAwX,CAAoBpY,EAAaR,GAC/BkF,GAAA1H,KAAK4Y,GAAkBzF,IAAIlQ,GAAQD,GAAc,CAC/CA,cACA0L,eAAgBlM,GAEtB,CACE,mBAAA6Y,CAAoBrY,GAClB,MAAMkY,EAAW,IAAIxT,GAAK1H,KAAA4Y,GAAkBrF,UACtC3P,EAAS,CAAE,EAMV,OALEsX,EAAAlT,SAASmT,IACZ1Y,GAAgBO,EAAamY,EAAanY,cACrCQ,OAAA2F,OAAOvF,EAAQuX,EAAazM,mBAGhC9K,CACX,CACE,mBAAAoP,CAAoBxQ,GAClB,GAAIA,EAAQ8Y,WACH,OAAA9Y,EAET,MAAM+W,EAAmB,IACpB7R,QAAK8G,GAAgBkF,WACrB1T,KAAKiT,iBAAiBzQ,EAAQJ,aAC9BI,EACH8Y,YAAY,GAoBP,OAlBF/B,EAAiBjX,YACpBiX,EAAiBjX,UAAYC,GAC3BgX,EAAiBnX,SACjBmX,SAGwC,IAAxCA,EAAiBgC,qBACFhC,EAAAgC,mBAAsD,WAAjChC,EAAiB7P,kBAEnB,IAAlC6P,EAAiB1S,eACF0S,EAAA1S,eAAiB0S,EAAiBiC,WAEhDjC,EAAiB7P,aAAe6P,EAAiBrH,YACpDqH,EAAiB7P,YAAc,gBAE7B6P,EAAiBhT,UAAYJ,KAC/BoT,EAAiB1X,SAAU,GAEtB0X,CACX,CACE,sBAAAvD,CAAuBxT,GACrB,aAAIA,WAAS8Y,YACJ9Y,EAEF,IACFkF,QAAK8G,GAAgBiN,cACZ,MAATjZ,OAAS,EAAAA,EAAAQ,cAAehD,KAAKqb,oBAAoB7Y,EAAQQ,gBACzDR,EACH8Y,YAAY,EAElB,CACE,KAAAjI,GACE3L,GAAA1H,KAAK0Y,GAAYrF,QACjB3L,GAAA1H,KAAKgU,GAAeX,OACxB,GA1REqF,EAAA,IAAAvQ,QACA6L,EAAA,IACAxF,QAAAA,EAAA,YACAmK,EACA,IAAAxQ,QAAAyQ,EAAA,IAAAzQ,QACA0Q,EACA,IAAA1Q,QAAA2Q,EAAA,IAAA3Q,QACA4Q,EARgB,IAAA5Q,QAAAwM,GCAd+G,IAAgB5G,iBAAchV,GAChC,WAAAC,CAAY8O,EAAQrM,GACXyE,QAFSC,GAAAlH,KAAA2b,IAelBrN,GAAAA,KAAAA,GACApH,GAAAlH,KAAA4b,GACA1U,GAAAlH,KAAA6b,GACA3U,GAAAlH,KAAA8b,GACA5U,GAAAlH,KAAA+b,GACA7U,GAAAlH,KAAAgc,GACA9U,GAAAlH,KAAAic,GACA/U,GAAAlH,KAAAkc,GACAhV,GAAAlH,KAAAmc,GACAjV,GAAAlH,KAAAoc,GAGAlV,GAAAlH,KAAAqc,GACAnV,GAAAlH,KAAAsc,GACApV,GAAAlH,KAAAuc,GACArV,GAAAlH,KAAAwc,IACAtV,GAAAlH,KAAAyc,OAAoCvc,KA5BlCF,KAAKwC,QAAUA,EACf8E,GAAAtH,KAAKsO,EAAUO,GACfvH,GAAAtH,KAAKkc,EAAe,MACpB5U,GAAAtH,KAAKic,EAAmBpT,MACnB7I,KAAKwC,QAAQka,+BAChBhV,GAAA1H,KAAKic,GAAiBvV,OACpB,IAAIC,MAAM,8DAGd3G,KAAK2c,cACL3c,KAAK2O,WAAWnM,EACpB,CAkBE,WAAAma,GACE3c,KAAK6Q,QAAU7Q,KAAK6Q,QAAQzQ,KAAKJ,KACrC,CACE,WAAAO,GAC8B,IAAxBP,KAAKC,UAAUU,OACZ+G,GAAA1H,KAAA4b,GAAc5K,YAAYhR,MAC3B4c,GAAmBlV,GAAA1H,KAAK4b,GAAe5b,KAAKwC,SAC9CuN,GAAA/P,KAAK2b,GAALkB,IAAArX,KAAAxF,MAEAA,KAAK8c,eAEP/M,GAAA/P,KAAK2b,GAALoB,IAAAvX,KAAAxF,MAEN,CACE,aAAAS,GACOT,KAAKU,gBACRV,KAAKwN,SAEX,CACE,sBAAAuD,GACS,OAAAiM,GACLtV,GAAK1H,KAAA4b,GACL5b,KAAKwC,QACLxC,KAAKwC,QAAQ+Y,mBAEnB,CACE,wBAAA3K,GACS,OAAAoM,GACLtV,GAAK1H,KAAA4b,GACL5b,KAAKwC,QACLxC,KAAKwC,QAAQya,qBAEnB,CACE,OAAAzP,GACOxN,KAAAC,cAAgCC,IACrC6P,GAAA/P,KAAK2b,GAALuB,IAAA1X,KAAAxF,MACA+P,GAAA/P,KAAK2b,GAALwB,IAAA3X,KAAAxF,MACK0H,GAAA1H,KAAA4b,GAAc1K,eAAelR,KACtC,CACE,UAAA2O,CAAWnM,GACT,MAAM4a,EAAcpd,KAAKwC,QACnB6a,EAAY3V,GAAK1H,KAAA4b,GAEnB,GADJ5b,KAAKwC,QAAUkF,GAAA1H,KAAKsO,GAAQ0E,oBAAoBxQ,QACnB,IAAzBxC,KAAKwC,QAAQX,SAAsD,kBAAzB7B,KAAKwC,QAAQX,SAAyD,mBAAzB7B,KAAKwC,QAAQX,SAA8F,kBAA7DD,GAAe5B,KAAKwC,QAAQX,QAAS6F,GAAA1H,KAAK4b,IACjL,MAAM,IAAIjV,MACR,yEAGJoJ,GAAA/P,KAAK2b,GAAL2B,IAAA9X,KAAAxF,MACK0H,GAAA1H,KAAA4b,GAAcjN,WAAW3O,KAAKwC,SAC/B4a,EAAY9B,aAAezW,GAAoB7E,KAAKwC,QAAS4a,IAC1D9O,GAAAA,KAAAA,GAAQQ,gBAAgBmC,OAAO,CAClCjP,KAAM,yBACNL,MAAO+F,GAAK1H,KAAA4b,GACZvL,SAAUrQ,OAGR,MAAAud,EAAUvd,KAAKU,eACjB6c,GAAWC,GACb9V,GAAK1H,KAAA4b,GACLyB,EACArd,KAAKwC,QACL4a,IAEArN,GAAA/P,KAAK2b,GAALkB,IAAArX,KAAAxF,MAEFA,KAAK8c,gBACDS,GAAY7V,GAAA1H,KAAK4b,KAAkByB,GAAazb,GAAe5B,KAAKwC,QAAQX,QAAS6F,GAAK1H,KAAA4b,MAAmBha,GAAewb,EAAYvb,QAAS6F,GAAK1H,KAAA4b,KAAkBla,GAAiB1B,KAAKwC,QAAQnB,UAAWqG,GAAA1H,KAAK4b,MAAmBla,GAAiB0b,EAAY/b,UAAWqG,GAAA1H,KAAK4b,KACxR7L,GAAA/P,KAAK2b,GAAL8B,IAAAjY,KAAAxF,MAEI,MAAA0d,EAAsB3N,QAAK4L,GAALgC,IAAAnY,KAAAxF,OACxBud,GAAY7V,GAAK1H,KAAA4b,KAAkByB,GAAazb,GAAe5B,KAAKwC,QAAQX,QAAS6F,GAAA1H,KAAK4b,MAAmBha,GAAewb,EAAYvb,QAAS6F,GAAA1H,KAAK4b,KAAkB8B,IAAwBhW,QAAK8U,KACvMzM,GAAA/P,KAAK2b,OAALnW,KAA4BxF,KAAA0d,EAElC,CACE,mBAAAE,CAAoBpb,GACZ,MAAAb,EAAQ+F,QAAK4G,GAAQQ,gBAAgBiE,MAAMrL,GAAA1H,KAAKsO,GAAS9L,GACzDoB,EAAS5D,KAAK6d,aAAalc,EAAOa,GAMjC,OAkUX,SAA+C6N,EAAUyN,GACvD,IAAKjZ,GAAoBwL,EAASG,mBAAoBsN,GAC7C,OAAA,EAEF,OAAA,CACT,CA5UQC,CAAsC/d,KAAM4D,KAC9C0D,GAAAtH,KAAK8b,EAAiBlY,GACtB0D,GAAAtH,KAAKgc,EAAwBhc,KAAKwC,SAC7B8E,GAAAtH,KAAA+b,EAAsBrU,QAAKkU,GAAchZ,QAEzCgB,CACX,CACE,gBAAA4M,GACE,OAAO9I,GAAK1H,KAAA8b,EAChB,CACE,WAAAkC,CAAYpa,EAAQqa,GACX,OAAA,IAAIC,MAAMta,EAAQ,CACvBiO,IAAK,CAACsM,EAAQta,KACZ7D,KAAKoe,UAAUva,GACC,MAAAoa,GAAAA,EAAApa,GACTwa,QAAQxM,IAAIsM,EAAQta,KAGnC,CACE,SAAAua,CAAUva,GACH6D,GAAA1H,KAAAyc,IAAcnc,IAAIuD,EAC3B,CACE,eAAAya,GACE,OAAO5W,GAAK1H,KAAA4b,EAChB,CACE,OAAA/K,KAAarO,GAAY,IACvB,OAAOxC,KAAKsR,MAAM,IACb9O,GAET,CACE,eAAA+b,CAAgB/b,GACd,MAAM+W,EAAmB7R,GAAA1H,KAAKsO,GAAQ0E,oBAAoBxQ,GACpDb,EAAQ+F,QAAK4G,GAAQQ,gBAAgBiE,MAAMrL,GAAA1H,KAAKsO,GAASiL,GACxD,OAAA5X,EAAM2P,QAAQ1G,MAAK,IAAM5K,KAAK6d,aAAalc,EAAO4X,IAC7D,CACE,KAAAjI,CAAMhL,GACG,OAAAyJ,GAAA/P,KAAK2b,OAALnW,KAAmBxF,KAAA,IACrBsG,EACHwK,cAAexK,EAAawK,gBAAiB,IAC5ClG,MAAK,KACN5K,KAAK8c,eACEpV,GAAK1H,KAAA8b,KAElB,CA4DE,YAAA+B,CAAalc,EAAOa,SAClB,MAAM6a,EAAY3V,GAAK1H,KAAA4b,GACjBwB,EAAcpd,KAAKwC,QACnBgc,EAAa9W,GAAK1H,KAAA8b,GAClB2C,EAAkB/W,GAAK1H,KAAA+b,GACvB2C,EAAoBhX,GAAK1H,KAAAgc,GAEzB2C,EADchd,IAAU0b,EACU1b,EAAMiB,MAAQ8E,GAAK1H,KAAA6b,IACrDjZ,MAAEA,GAAUjB,EACd,IAEAgE,EAFAiZ,EAAW,IAAKhc,GAChBic,GAAoB,EAExB,GAAIrc,EAAQsc,mBAAoB,CACxB,MAAAvB,EAAUvd,KAAKU,eACfqe,GAAgBxB,GAAWX,GAAmBjb,EAAOa,GACrDwc,EAAkBzB,GAAWC,GAAsB7b,EAAO0b,EAAW7a,EAAS4a,IAChF2B,GAAgBC,KACPJ,EAAA,IACNA,KACApM,GAAW5P,EAAM+C,KAAMhE,EAAMa,WAGD,gBAA/BA,EAAQsc,qBACVF,EAAS1c,YAAc,OAE/B,CACI,IAAI+I,MAAEA,EAAAoE,eAAOA,EAAgBtM,OAAAA,GAAW6b,EACxCjZ,EAAOiZ,EAASjZ,KAChB,IAAIsZ,GAAa,EACjB,QAAgC,IAA5Bzc,EAAQ0c,sBAAuC,IAATvZ,GAA8B,YAAX5C,EAAsB,CAC7E,IAAAmc,GACY,MAAZV,OAAY,EAAAA,EAAAK,oBAAqBrc,EAAQ0c,yBAAoBR,WAAmBQ,kBAClFA,EAAkBV,EAAW7Y,KAChBsZ,GAAA,GAEbC,EAAqD,mBAA5B1c,EAAQ0c,gBAAiC1c,EAAQ0c,gBACxE,OAAAlY,EAAAU,GAAA1H,KAAKqc,SAAL,EAAArV,EAAgCpE,MAAM+C,KACtC+B,GAAK1H,KAAAqc,IACH7Z,EAAQ0c,qBAEU,IAApBA,IACOnc,EAAA,UACF4C,EAAAF,GACO,MAAZ+Y,OAAY,EAAAA,EAAA7Y,KACZuZ,EACA1c,GAEkBqc,GAAA,EAE5B,CACI,GAAIrc,EAAQ2c,aAAmB,IAATxZ,IAAoBsZ,EACxC,GAAIT,GAAc7Y,KAAS,MAAA8Y,OAAA,EAAAA,EAAiB9Y,OAAQnD,EAAQ2c,SAAWzX,QAAKyU,GAC1ExW,EAAO+B,GAAK1H,KAAAoc,QAER,IACF9U,GAAAtH,KAAKmc,EAAY3Z,EAAQ2c,QAClBxZ,EAAAnD,EAAQ2c,OAAOxZ,GACtBA,EAAOF,GAAY,MAAA+Y,OAAA,EAAAA,EAAY7Y,KAAMA,EAAMnD,GAC3C8E,GAAAtH,KAAKoc,EAAgBzW,GACrB2B,GAAAtH,KAAKkc,EAAe,KACrB,OAAQkD,GACP9X,GAAAtH,KAAKkc,EAAekD,EAC9B,CAGQ1X,QAAKwU,KACPjR,EAAQvD,GAAK1H,KAAAkc,GACbvW,EAAO+B,GAAK1H,KAAAoc,GACZ/M,EAAiB7N,KAAKC,MACbsB,EAAA,SAEL,MAAAoW,EAAsC,aAAzByF,EAAS1c,YACtBmd,EAAuB,YAAXtc,EACZuc,EAAqB,UAAXvc,EACVwc,EAAYF,GAAalG,EACzBnK,OAAmB,IAATrJ,EA4BV6Z,EA3BS,CACbzc,SACAb,YAAa0c,EAAS1c,YACtBmd,YACAI,UAAsB,YAAX1c,EACXuc,UACAI,iBAAkBH,EAClBA,YACA5Z,OACAwJ,cAAeyP,EAASzP,cACxBlE,QACAoE,iBACA9F,aAAcqV,EAAStP,kBACvB6E,cAAeyK,EAASrP,mBACxBH,iBAAkBwP,EAASxP,iBAC3BuQ,UAAWf,EAAS1P,gBAAkB,GAAK0P,EAASxP,iBAAmB,EACvEwQ,oBAAqBhB,EAAS1P,gBAAkByP,EAAkBzP,iBAAmB0P,EAASxP,iBAAmBuP,EAAkBvP,iBACnI+J,aACA0G,aAAc1G,IAAekG,EAC7BS,eAAgBR,IAAYtQ,EAC5BoF,SAAmC,WAAzBwK,EAAS1c,YACnB2c,oBACAkB,eAAgBT,GAAWtQ,EAC3BrM,QAASA,GAAQhB,EAAOa,GACxBqO,QAAS7Q,KAAK6Q,QACdpF,QAAS/D,GAAK1H,KAAAic,IAGZ,GAAAjc,KAAKwC,QAAQka,8BAA+B,CACxC,MAAAsD,EAA8BjX,IACR,UAAtByW,EAAWzc,OACJgG,EAAArC,OAAO8Y,EAAWvU,YACE,IAApBuU,EAAW7Z,MACXoD,EAAAD,QAAQ0W,EAAW7Z,OAG1Bsa,EAAmB,KACvB,MAAMC,EAAU5Y,GAAAtH,KAAKic,EAAmBuD,EAAW/T,QAAU5C,MAC7DmX,EAA2BE,IAEvBC,EAAezY,GAAK1H,KAAAic,GAC1B,OAAQkE,EAAapd,QACnB,IAAK,UACCpB,EAAMW,YAAc+a,EAAU/a,WAChC0d,EAA2BG,GAE7B,MACF,IAAK,YACuB,UAAtBX,EAAWzc,QAAsByc,EAAW7Z,OAASwa,EAAalf,OAClDgf,IAEpB,MACF,IAAK,WACuB,UAAtBT,EAAWzc,QAAsByc,EAAWvU,QAAUkV,EAAa9W,QACnD4W,IAI9B,CACW,OAAAT,CACX,CACE,YAAA1C,GACE,MAAM0B,EAAa9W,GAAK1H,KAAA8b,GAClB0D,EAAaxf,KAAK6d,aAAanW,GAAK1H,KAAA4b,GAAe5b,KAAKwC,SAM1D,GALC8E,GAAAtH,KAAA+b,EAAsBrU,QAAKkU,GAAchZ,OAC9C0E,GAAAtH,KAAKgc,EAAwBhc,KAAKwC,cACI,IAAlCkF,GAAA1H,KAAK+b,GAAoBpW,MAC3B2B,GAAAtH,KAAKqc,EAA4B3U,GAAK1H,KAAA4b,IAEpC/W,GAAoB2a,EAAYhB,GAClC,OAEFlX,GAAAtH,KAAK8b,EAAiB0D,GAsBtBzP,GAAA/P,KAAK2b,GAALyE,IAAA5a,KAAAxF,KAAa,CAAEC,UArBe,MAC5B,IAAKue,EACI,OAAA,EAEH,MAAA6B,oBAAEA,GAAwBrgB,KAAKwC,QAC/B8d,EAA0D,mBAAxBD,EAAqCA,IAAwBA,EACrG,GAAiC,QAA7BC,IAAuCA,IAA6B5Y,GAAA1H,KAAKyc,IAAc9b,KAClF,OAAA,EAET,MAAM4f,EAAgB,IAAIrgB,IACxBogB,GAA4B5Y,GAAK1H,KAAAyc,KAKnC,OAHIzc,KAAKwC,QAAQqE,cACf0Z,EAAcjgB,IAAI,SAEbkD,OAAOC,KAAKiE,GAAA1H,KAAK8b,IAAgB1L,MAAMvM,IAC5C,MAAM2c,EAAW3c,EAEV,OADS6D,GAAK1H,KAAA8b,GAAe0E,KAAchC,EAAWgC,IAC3CD,EAAcrN,IAAIsN,OAGdC,IAC9B,CAcE,aAAA/N,GACE1S,KAAK8c,eACD9c,KAAKU,gBACPqP,GAAA/P,KAAK2b,GAALoB,IAAAvX,KAAAxF,KAEN,GApYEsO,EAAA,IACAnG,QAAAyT,EAAA,IAAAzT,QACA0T,cACAC,EACA,IAAA3T,QAAA4T,EAAA,IAAA5T,QACA6T,cACAC,EACA,IAAA9T,QAAA+T,EAAA,IAAA/T,QACAgU,cACAC,EAGA,IAAAjU,QAAAkU,EAAA,IAAAlU,QACAmU,cACAC,EACA,IAAApU,QAAAqU,GAAA,IAAArU,QACAsU,eA/BkBd,GA0JlB,IAAArJ,QAAAuK,GAAa,SAACvW,GACZyJ,GAAA/P,KAAK2b,GAAL2B,IAAA9X,KAAAxF,MACI,IAAAyL,EAAU/D,QAAKkU,GAActK,MAC/BtR,KAAKwC,QACL8D,GAKK,aAHFA,WAAcO,gBACP4E,EAAAA,EAAQrC,MAAMrI,KAEnB0K,CACX,EACEgS,GAAmB,WACjB1N,GAAA/P,KAAK2b,GAALuB,IAAA1X,KAAAxF,MACA,MAAMqB,EAAYK,GAChB1B,KAAKwC,QAAQnB,UACbqG,GAAK1H,KAAA4b,IAEP,GAAIhb,IAAY8G,GAAK1H,KAAA8b,GAAenZ,UAAY3B,GAAeK,GAC7D,OAEF,MAAMqf,EAAOvf,GAAeuG,GAAK1H,KAAA8b,GAAe3M,cAAe9N,GAE1DiG,GAAAtH,KAAAsc,EAAkB9Q,YAAW,KAC3B9D,GAAK1H,KAAA8b,GAAenZ,SACvB3C,KAAK8c,iBAHO4D,EAAO,GAM3B,EACE/C,GAAuB,WACrB,OAAgD,mBAAjC3d,KAAKwC,QAAQme,gBAAiC3gB,KAAKwC,QAAQme,gBAAgBjZ,GAAK1H,KAAA4b,IAAiB5b,KAAKwC,QAAQme,mBAAoB,CACrJ,EACEC,GAAsB,SAACC,GACrB9Q,GAAA/P,KAAK2b,GAALwB,IAAA3X,KAAAxF,MACAsH,GAAAtH,KAAKwc,GAA0BqE,IAC3BjgB,KAAyE,IAA7DgB,GAAe5B,KAAKwC,QAAQX,QAAS6F,QAAKkU,KAA6B5a,GAAe0G,GAAK1H,KAAAwc,MAA6D,IAAjC9U,GAAA1H,KAAKwc,KAGvIlV,GAAAtH,KAAAuc,EAAqBuE,aAAY,MAChC9gB,KAAKwC,QAAQue,6BAA+Bha,GAAagB,cAC3DgI,GAAA/P,KAAK2b,GAALkB,IAAArX,KAAAxF,QAED0H,QAAK8U,KACZ,EACEO,GAAa,WACXhN,GAAA/P,KAAK2b,GAAL8B,IAAAjY,KAAAxF,MACK+P,GAAA/P,KAAA2b,GAAAiF,IAALpb,KAA4BxF,KAAA+P,GAAA/P,KAAK2b,GAALgC,IAAAnY,KAAAxF,MAChC,EACEkd,GAAkB,WACZxV,QAAK4U,KACPvO,aAAarG,QAAK4U,IAClBhV,GAAAtH,KAAKsc,OAAkB,GAE7B,EACEa,GAAqB,WACfzV,QAAK6U,KACPyE,cAActZ,QAAK6U,IACnBjV,GAAAtH,KAAKuc,OAAqB,GAEhC,EA6KEe,GAAY,WACJ,MAAA3b,EAAQ+F,QAAK4G,GAAQQ,gBAAgBiE,MAAMrL,GAAA1H,KAAKsO,GAAStO,KAAKwC,SAChE,GAAAb,IAAU+F,QAAKkU,GACjB,OAEF,MAAMyB,EAAY3V,GAAK1H,KAAA4b,GACvBtU,GAAAtH,KAAK4b,EAAgBja,GACrB2F,GAAAtH,KAAK6b,EAA4Bla,EAAMiB,OACnC5C,KAAKU,iBACP,MAAA2c,GAAAA,EAAWnM,eAAelR,MAC1B2B,EAAMqP,YAAYhR,MAExB,EAOEogB,GAAO,SAACa,GACN9U,GAAcS,OAAM,KACdqU,EAAchhB,WACXD,KAAAC,UAAU+H,SAAS3H,IACtBA,EAASqH,QAAKoU,OAGbxN,GAAAA,KAAAA,GAAQQ,gBAAgBmC,OAAO,CAClCtP,MAAO+F,GAAK1H,KAAA4b,GACZ5Z,KAAM,6BAGd,EAhaoB8S,IAqapB,SAAS8H,GAAmBjb,EAAOa,GACjC,OAJF,SAA2Bb,EAAOa,GAChC,OAAkD,IAA3CZ,GAAeY,EAAQX,QAASF,SAAyC,IAArBA,EAAMiB,MAAM+C,QAA4C,UAAvBhE,EAAMiB,MAAMG,SAA+C,IAAzBP,EAAQ0e,aACxI,CAESC,CAAkBxf,EAAOa,SAAiC,IAArBb,EAAMiB,MAAM+C,MAAmBqX,GAAcrb,EAAOa,EAASA,EAAQ4e,eACnH,CACA,SAASpE,GAAcrb,EAAOa,EAAS6e,GACrC,IAA+C,IAA3Czf,GAAeY,EAAQX,QAASF,GAAkB,CACpD,MAAMV,EAAyB,mBAAVogB,EAAuBA,EAAM1f,GAAS0f,EAC3D,MAAiB,WAAVpgB,IAAgC,IAAVA,GAAmB0B,GAAQhB,EAAOa,EACnE,CACS,OAAA,CACT,CACA,SAASgb,GAAsB7b,EAAO0b,EAAW7a,EAAS4a,GACxD,OAAQzb,IAAU0b,IAA4D,IAA/Czb,GAAewb,EAAYvb,QAASF,OAAuBa,EAAQgZ,UAAmC,UAAvB7Z,EAAMiB,MAAMG,SAAuBJ,GAAQhB,EAAOa,EAClK,CACA,SAASG,GAAQhB,EAAOa,GACtB,OAAkD,IAA3CZ,GAAeY,EAAQX,QAASF,IAAoBA,EAAM8O,cAAc/O,GAAiBc,EAAQnB,UAAWM,GACrH,CC/bA,IAAI2f,IAAmBzM,iBAAc/U,GAKnC,WAAAC,CAAY8O,EAAQrM,GACXyE,QANYC,GAAAlH,KAAAuhB,IACrBjT,GAAAA,KAAAA,IACAwN,GAAAA,KAAAA,IACA5U,GAAAlH,KAAAwhB,IACAta,GAAAlH,KAAAyhB,IAGEna,GAAAtH,KAAKsO,GAAUO,GACf7O,KAAK2O,WAAWnM,GAChBxC,KAAK2c,cACL5M,GAAA/P,KAAKuhB,GAALG,IAAAlc,KAAAxF,KACJ,CACE,WAAA2c,GACE3c,KAAK2hB,OAAS3hB,KAAK2hB,OAAOvhB,KAAKJ,MAC/BA,KAAKmQ,MAAQnQ,KAAKmQ,MAAM/P,KAAKJ,KACjC,CACE,UAAA2O,CAAWnM,SACT,MAAM4a,EAAcpd,KAAKwC,QACzBxC,KAAKwC,QAAUkF,GAAA1H,KAAKsO,IAAQ0H,uBAAuBxT,GAC9CqC,GAAoB7E,KAAKwC,QAAS4a,IAChC9O,GAAAA,KAAAA,IAAQwM,mBAAmB7J,OAAO,CACrCjP,KAAM,yBACNc,SAAU4E,GAAK1H,KAAAwhB,IACfnR,SAAUrQ,QAGG,MAAbod,OAAa,EAAAA,EAAApa,cAAehD,KAAKwC,QAAQQ,aAAeC,GAAQma,EAAYpa,eAAiBC,GAAQjD,KAAKwC,QAAQQ,aACpHhD,KAAKmQ,QAC4C,aAAxC,OAAAnJ,EAAAU,GAAK1H,KAAAwhB,YAALxa,EAAuBpE,MAAMG,SACjC2E,GAAA1H,KAAAwhB,IAAiB7S,WAAW3O,KAAKwC,QAE5C,CACE,aAAA/B,SACOT,KAAKU,gBACR,OAAAsG,EAAAU,GAAA1H,KAAKwhB,MAALxa,EAAuBkK,eAAelR,KAE5C,CACE,gBAAA0V,CAAiBnD,GACfxC,GAAA/P,KAAKuhB,GAALG,IAAAlc,KAAAxF,MACK+P,GAAA/P,KAAAuhB,GAAAnB,IAAL5a,KAAaxF,KAAAuS,EACjB,CACE,gBAAA/B,GACE,OAAO9I,GAAKoU,KAAAA,GAChB,CACE,KAAA3L,SACE,OAAAnJ,EAAAU,GAAA1H,KAAKwhB,MAALxa,EAAuBkK,eAAelR,MACtCsH,GAAAtH,KAAKwhB,QAAmB,GACxBzR,GAAA/P,KAAKuhB,GAALG,IAAAlc,KAAAxF,MACA+P,GAAA/P,KAAKuhB,GAAAnB,IAAL5a,KAAAxF,KACJ,CACE,MAAA2hB,CAAOtN,EAAW7R,SAKT,OAJP8E,GAAAtH,KAAKyhB,GAAiBjf,GACtB,OAAAwE,EAAAU,GAAA1H,KAAKwhB,MAALxa,EAAuBkK,eAAelR,MACjCsH,GAAAtH,KAAAwhB,GAAmB9Z,QAAK4G,IAAQwM,mBAAmB/H,MAAMrL,GAAA1H,KAAKsO,IAAStO,KAAKwC,UAC5EkF,GAAA1H,KAAAwhB,IAAiBxQ,YAAYhR,MAC3B0H,GAAA1H,KAAKwhB,IAAiBjN,QAAQF,EACzC,GAvDE/F,GAAA,YACAwN,GAAA,IAAA3T,QACAqZ,eACAC,GAJqB,IAAAtZ,QAAAoZ,GAAA,IAAAjP,QAyDrBoP,GAAa,iBACX,MAAM9e,GAAQ,OAAAoE,EAAAU,GAAA1H,KAAKwhB,UAALxa,EAAAA,EAAuBpE,QL8JhC,CACLmP,aAAS,EACTpM,UAAM,EACNsF,MAAO,KACP1B,aAAc,EACd4K,cAAe,KACfC,UAAU,EACVrR,OAAQ,OACRsR,eAAW,EACXC,YAAa,GKtKbhN,GAAAtH,KAAK8b,GAAiB,IACjBlZ,EACHyc,UAA4B,YAAjBzc,EAAMG,OACjB0c,UAA4B,YAAjB7c,EAAMG,OACjBuc,QAA0B,UAAjB1c,EAAMG,OACf6e,OAAyB,SAAjBhf,EAAMG,OACd4e,OAAQ3hB,KAAK2hB,OACbxR,MAAOnQ,KAAKmQ,OAElB,EACEiQ,GAAO,SAAC7N,GACNpG,GAAcS,OAAM,yBAClB,GAAIlF,GAAK1H,KAAAyhB,KAAkBzhB,KAAKU,eAAgB,CACxC,MAAA2T,EAAY3M,QAAKoU,IAAezH,UAChCtC,EAAUrK,QAAKoU,IAAe/J,QACf,aAAjB,MAAAQ,OAAA,EAAAA,EAAQvQ,OACV,OAAAqG,GAAArB,EAAAU,GAAA1H,KAAKyhB,KAAelX,YAApBlC,EAAA7C,KAAAwB,EAAgCuL,EAAO5M,KAAM0O,EAAWtC,GACxD,OAAA9D,GAAAX,EAAA5F,GAAK1H,KAAAyhB,KAAepP,YAApBpE,EAAAzI,KAAA8H,EAAgCiF,EAAO5M,KAAM,KAAM0O,EAAWtC,IACpC,WAAT,MAARQ,OAAQ,EAAAA,EAAAvQ,QACjB,OAAA6R,GAAAjB,EAAAlL,GAAA1H,KAAKyhB,KAAejX,UAApBqJ,EAAArO,KAAAoN,EAA8BL,EAAOtH,MAAOoJ,EAAWtC,GACvD,OAAA4C,GAAAC,EAAAlN,GAAA1H,KAAKyhB,KAAepP,YAApBsC,EAAAnP,KAAAoP,OACE,EACArC,EAAOtH,MACPoJ,EACAtC,GAGZ,CACW/R,KAAAC,UAAU+H,SAAS3H,IACtBA,EAASqH,QAAKoU,UAGtB,EA3FuBjH,ICAnBgN,GAAqBC,GAAmBC,mBAC1C,GAEEC,GAAkBC,IACd,MAAApT,EAASqT,GAAgBC,WAACN,IAIhC,IAAKhT,EACG,MAAA,IAAIlI,MAAM,0DAEX,OAAAkI,GAELuT,GAAsB,EACxBvT,SACAwT,eAEAC,GAAAA,WAAgB,KACdzT,EAAOoK,QACA,KACLpK,EAAOqK,aAER,CAACrK,IACmB0T,GAAAA,IAAIV,GAAmBW,SAAU,CAAEvhB,MAAO4N,EAAQwT,cCxBvEI,GAAqBX,GAAmBC,eAAC,GAEnBU,GAAmBD,SCa7C,IAAIE,GAAiCZ,GAAAA,cAdrC,WACE,IAAIa,GAAU,EACP,MAAA,CACLC,WAAY,KACAD,GAAA,GAEZxS,MAAO,KACKwS,GAAA,GAEZA,QAAS,IACAA,EAGb,CACyDE,ICNrDtE,GAAkB,CAAChF,EAAkBlJ,EAAUyS,IAAuBzS,EAASkO,gBAAgBhF,GAAkBnQ,OAAM,KACzH0Z,EAAmBF,gBCKrB,SAASG,GAAavgB,EAASwgB,EAAUf,iBAQjC,MAAApT,EAASmT,KACTiB,EHvBmBf,GAAgBC,WAACM,IGwBpCK,EFT+BZ,GAAgBC,WAACO,IEUhDnJ,EAAmB1K,EAAOmE,oBAAoBxQ,GAC7C,OAAA6F,EAAA,OAAArB,EAAA6H,EAAAkM,oBAAoBrH,cAApB1M,EAAAA,EAA6Bkc,4BAA7B7a,EAAA7C,KAAAwB,EACLuS,GASeA,EAAAuF,mBAAqBmE,EAAc,cAAgB,aDvC3C,CAAC1J,IAC1B,MAAM4J,EAAoB5J,EAAiBlY,UACvCkY,EAAiBiC,WACnBjC,EAAiBlY,UAAyC,mBAAtB8hB,EAAmC,IAAInW,IAAS1L,KAAKC,IAAI4hB,KAAqBnW,GAAO,KAAO1L,KAAKC,IAAI4hB,GAAqB,IAAK,KAC5H,iBAA5B5J,EAAiB5L,SAC1B4L,EAAiB5L,OAASrM,KAAKC,IAAIgY,EAAiB5L,OAAQ,QCmChEyV,CAAqB7J,GCrCe,EAAC/W,EAASsgB,MAC1CtgB,EAAQgZ,UAAYhZ,EAAQqE,cAAgBrE,EAAQka,iCACjDoG,EAAmBH,YACtBngB,EAAQ0e,cAAe,KDmC3BmC,CAAgC9J,EAAkBuJ,GC/BnB,CAACA,IAChCR,GAAAA,WAAgB,KACdQ,EAAmBF,eAClB,CAACE,KD6BJQ,CAA2BR,GAC3B,MAAMS,GAAmB1U,EAAOC,gBAAgB+C,IAAI0H,EAAiBjX,YAC9D+N,GAAYmT,GAAMC,UACvB,IAAM,IAAIT,EACRnU,EACA0K,KAGE3V,EAASyM,EAASuN,oBAAoBrE,GACtCmK,GAAmBT,IAAsC,IAAvBzgB,EAAQmhB,WAgB5C,GAfEC,GAAAC,qBACJC,GAAMC,aACHC,IACO,MAAAC,EAAcP,EAAkBrT,EAASlQ,UAAUgM,GAAcY,WAAWiX,IAAkBjjB,GAE7F,OADPsP,EAASyM,eACFmH,IAET,CAAC5T,EAAUqT,KAEb,IAAMrT,EAASG,qBACf,IAAMH,EAASG,qBAEjB8R,GAAAA,WAAgB,KACdjS,EAAS1B,WAAW4K,KACnB,CAACA,EAAkBlJ,IDxDJ,EAACkJ,EAAkB3V,KAAW,MAAA2V,OAAA,EAAAA,EAAkBiC,WAAY5X,EAAOyb,UCyDjF6E,CAAc3K,EAAkB3V,GAC5B,MAAA2a,GAAgBhF,EAAkBlJ,EAAUyS,GAEpD,GCvDgB,GAChBlf,SACAkf,qBACAjc,eACAlF,QACA6Z,cAEO5X,EAAO0b,UAAYwD,EAAmBH,YAAc/e,EAAOuV,YAAcxX,IAAU6Z,QAA4B,IAAhB5X,EAAO+B,MAAmBiB,GAAiBC,EAAc,CAACjD,EAAOqH,MAAOtJ,KDgD1KwiB,CAAY,CACdvgB,SACAkf,qBACAjc,aAAc0S,EAAiB1S,aAC/BlF,MAAOkN,EAAOC,gBAAgB+C,IAAI0H,EAAiBjX,WACnDkZ,SAAUjC,EAAiBiC,WAE3B,MAAM5X,EAAOqH,MAOf,GAJO,OAAAgD,EAAA,OAAAX,EAAAuB,EAAAkM,oBAAoBrH,cAApBpG,EAAAA,EAA6B8W,2BAA7BnW,EAAAzI,KAAA8H,EACLiM,EACA3V,GAEE2V,EAAiBmD,gCAAkC9b,ID3EzC,EAACgD,EAAQqf,IAAgBrf,EAAO2b,WAAa3b,EAAOuV,aAAe8J,EC2EdoB,CAAUzgB,EAAQqf,GAAc,CACjG,MAAMxX,EAAU8X,EAEdhF,GAAgBhF,EAAkBlJ,EAAUyS,GAG5C,OAAAlQ,EAAA/D,EAAOC,gBAAgB+C,IAAI0H,EAAiBjX,iBAA5C,EAAAsQ,EAAwDnH,QAEjD,MAAAA,GAAAA,EAAArC,MAAMrI,IAAMujB,SAAQ,KAC3BjU,EAASyM,iBACV,CAEH,OAAQvD,EAAiB8G,oBAAqDzc,EAA/ByM,EAAS2N,YAAYpa,EACtE,CE9FA,SAAS2gB,GAAS/hB,EAASyf,GAClB,OAAAc,GAAavgB,EAASkZ,GAC/B,CCIA,SAAS8I,GAAYhiB,EAASyf,GAC5B,MAAMpT,EAASmT,MACR3R,GAAYmT,GAAcC,UAC/B,IAAM,IAAInC,GACRzS,EACArM,KAGJ8f,GAAAA,WAAgB,KACdjS,EAAS1B,WAAWnM,KACnB,CAAC6N,EAAU7N,IACd,MAAMoB,EAAS6gB,GAA0BZ,qBACvCC,GAAiBC,aACdC,GAAkB3T,EAASlQ,UAAUgM,GAAcY,WAAWiX,KAC/D,CAAC3T,KAEH,IAAMA,EAASG,qBACf,IAAMH,EAASG,qBAEXmR,EAASmC,GAAiBC,aAC9B,CAAC1P,EAAWqQ,KACVrU,EAASsR,OAAOtN,EAAWqQ,GAAetb,MAAMrI,MAElD,CAACsP,IAEC,GAAAzM,EAAOqH,OAASrE,GAAiByJ,EAAS7N,QAAQqE,aAAc,CAACjD,EAAOqH,QAC1E,MAAMrH,EAAOqH,MAEf,MAAO,IAAKrH,EAAQ+d,SAAQgD,YAAa/gB,EAAO+d,OAClD", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}